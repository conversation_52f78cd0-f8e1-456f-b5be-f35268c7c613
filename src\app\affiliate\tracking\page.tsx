'use client'

import { motion } from 'framer-motion'
import { Tb<PERSON><PERSON>, Tb<PERSON>hart<PERSON><PERSON>, Tb<PERSON><PERSON>, Tb<PERSON>sers, TbTarget, TbCopy } from 'react-icons/tb'
import { useState } from 'react'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateTrackingPage = () => {
    const [copiedLink, setCopiedLink] = useState('')

    const trackingFeatures = [
        {
            icon: TbLink,
            title: 'Unique Tracking Links',
            description: 'Generate personalized tracking links for different campaigns and traffic sources.',
            features: ['Custom campaign names', 'UTM parameter support', 'Multiple link formats', 'QR code generation']
        },
        {
            icon: TbChartLine,
            title: 'Real-Time Analytics',
            description: 'Monitor your referral performance with detailed real-time analytics.',
            features: ['Click tracking', 'Conversion rates', 'Revenue analytics', 'Geographic data']
        },
        {
            icon: TbEye,
            title: 'Advanced Reporting',
            description: 'Comprehensive reports to optimize your marketing campaigns.',
            features: ['Daily/Weekly/Monthly reports', 'Traffic source analysis', 'Device breakdown', 'Time-based analytics']
        },
        {
            icon: TbUsers,
            title: 'Client Management',
            description: 'Track and manage all your referred clients in one dashboard.',
            features: ['Client status tracking', 'Trading activity', 'Commission history', 'Client lifetime value']
        }
    ]

    const sampleLinks = [
        {
            campaign: 'Social Media Campaign',
            link: 'https://mbf.mybrokerforex.com/register?ref=AF123&campaign=social',
            clicks: 1247,
            conversions: 23,
            revenue: '$2,875'
        },
        {
            campaign: 'Email Newsletter',
            link: 'https://mbf.mybrokerforex.com/register?ref=AF123&campaign=email',
            clicks: 892,
            conversions: 18,
            revenue: '$2,250'
        },
        {
            campaign: 'Blog Content',
            link: 'https://mbf.mybrokerforex.com/register?ref=AF123&campaign=blog',
            clicks: 654,
            conversions: 12,
            revenue: '$1,500'
        }
    ]

    const copyToClipboard = (link: string, campaign: string) => {
        navigator.clipboard.writeText(link)
        setCopiedLink(campaign)
        setTimeout(() => setCopiedLink(''), 2000)
    }

    const trackingSteps = [
        {
            step: '1',
            title: 'Generate Links',
            description: 'Create unique tracking links for your campaigns'
        },
        {
            step: '2',
            title: 'Share & Promote',
            description: 'Use your links in marketing materials and campaigns'
        },
        {
            step: '3',
            title: 'Track Performance',
            description: 'Monitor clicks, conversions, and earnings in real-time'
        },
        {
            step: '4',
            title: 'Optimize Campaigns',
            description: 'Use analytics to improve your marketing strategy'
        }
    ]

    return (
        <PageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                Tracking Links
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Advanced tracking and analytics for your affiliate marketing campaigns
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* Tracking Features */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Tracking Features
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Comprehensive tracking tools to maximize your affiliate success
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {trackingFeatures.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center mb-6">
                                        <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                            <feature.icon className="w-6 h-6 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {feature.title}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {feature.description}
                                    </p>
                                    <ul className="space-y-2">
                                        {feature.features.map((item, itemIndex) => (
                                            <li key={itemIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                {item}
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Sample Tracking Links */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Sample Tracking Links
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Examples of how your tracking links perform across different campaigns
                            </p>
                        </motion.div>

                        <div className="overflow-x-auto">
                            <table className="w-full bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden">
                                <thead className="bg-gray-100 dark:bg-gray-600">
                                    <tr>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Campaign</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Tracking Link</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Clicks</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Conversions</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Revenue</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {sampleLinks.map((link, index) => (
                                        <motion.tr
                                            key={index}
                                            initial={{ opacity: 0, y: 20 }}
                                            whileInView={{ opacity: 1, y: 0 }}
                                            transition={{ duration: 0.4, delay: index * 0.1 }}
                                            className="border-b border-gray-200 dark:border-gray-600"
                                        >
                                            <td className="px-6 py-4 font-semibold text-gray-900 dark:text-white">
                                                {link.campaign}
                                            </td>
                                            <td className="px-6 py-4 text-gray-600 dark:text-gray-300 font-mono text-sm max-w-xs truncate">
                                                {link.link}
                                            </td>
                                            <td className="px-6 py-4 text-gray-600 dark:text-gray-300">
                                                {link.clicks.toLocaleString()}
                                            </td>
                                            <td className="px-6 py-4 text-gray-600 dark:text-gray-300">
                                                {link.conversions}
                                            </td>
                                            <td className="px-6 py-4 font-semibold text-[#28C76F]">
                                                {link.revenue}
                                            </td>
                                            <td className="px-6 py-4">
                                                <button
                                                    onClick={() => copyToClipboard(link.link, link.campaign)}
                                                    className="flex items-center text-[#EA5455] hover:text-[#d63384] transition-colors duration-200"
                                                >
                                                    <TbCopy className="w-4 h-4 mr-1" />
                                                    {copiedLink === link.campaign ? 'Copied!' : 'Copy'}
                                                </button>
                                            </td>
                                        </motion.tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                {/* How It Works */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                How Tracking Works
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Simple steps to start tracking your affiliate marketing success
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                            {trackingSteps.map((item, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="text-center"
                                >
                                    <div className="w-12 h-12 bg-[#EA5455] text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                                        {item.step}
                                    </div>
                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                        {item.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {item.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Analytics Dashboard Preview */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Analytics Dashboard
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Comprehensive analytics to track your affiliate performance
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
                            {[
                                { label: 'Total Clicks', value: '12,847', change: '+15.3%', color: 'text-blue-500' },
                                { label: 'Conversions', value: '234', change: '+8.7%', color: 'text-[#28C76F]' },
                                { label: 'Conversion Rate', value: '1.82%', change: '+0.3%', color: 'text-orange-500' },
                                { label: 'Total Revenue', value: '$28,750', change: '+22.1%', color: 'text-[#EA5455]' }
                            ].map((stat, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center"
                                >
                                    <div className={`text-3xl font-bold mb-2 ${stat.color}`}>
                                        {stat.value}
                                    </div>
                                    <div className="text-gray-600 dark:text-gray-300 mb-2">
                                        {stat.label}
                                    </div>
                                    <div className="text-[#28C76F] text-sm font-medium">
                                        {stat.change}
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-[#EA5455]">
                    <div className="max-w-7xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                Start Tracking Your Success
                            </h2>
                            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                Join our affiliate program and get access to advanced tracking tools
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                Join Affiliate Program
                            </button>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PageLayout>
    )
}

export default AffiliateTrackingPage
