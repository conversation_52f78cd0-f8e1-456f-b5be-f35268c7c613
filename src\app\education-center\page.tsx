'use client'

import { motion } from 'framer-motion'
import { T<PERSON>Book, TbVideo, TbChartLine, TbUsers, TbClock, TbPlayerPlay, TbDownload, TbCalendar } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import PageLayout from '@/components/layout/PageLayout'

const EducationCenterPage = () => {
  const router = useRouter()

  const courses = [
    {
      id: 1,
      title: "Forex Trading Fundamentals",
      description: "Learn the basics of forex trading, market analysis, and risk management",
      level: "Beginner",
      duration: "4 hours",
      lessons: 12,
      image: "/img/education/forex-fundamentals.jpg",
      color: "text-green-500"
    },
    {
      id: 2,
      title: "Technical Analysis Mastery",
      description: "Master chart patterns, indicators, and technical analysis strategies",
      level: "Intermediate",
      duration: "6 hours",
      lessons: 18,
      image: "/img/education/technical-analysis.jpg",
      color: "text-blue-500"
    },
    {
      id: 3,
      title: "Advanced Trading Strategies",
      description: "Professional trading strategies and portfolio management techniques",
      level: "Advanced",
      duration: "8 hours",
      lessons: 24,
      image: "/img/education/advanced-strategies.jpg",
      color: "text-red-500"
    }
  ]

  const webinars = [
    {
      id: 1,
      title: "Market Outlook 2024",
      presenter: "<PERSON>, Senior Analyst",
      date: "Dec 15, 2024",
      time: "2:00 PM GMT",
      duration: "1 hour",
      status: "upcoming"
    },
    {
      id: 2,
      title: "Risk Management Strategies",
      presenter: "Sarah Johnson, Trading Expert",
      date: "Dec 10, 2024",
      time: "3:00 PM GMT",
      duration: "45 minutes",
      status: "recorded"
    },
    {
      id: 3,
      title: "Platform Tutorial: Advanced Features",
      presenter: "Mike Chen, Product Manager",
      date: "Dec 5, 2024",
      time: "1:00 PM GMT",
      duration: "30 minutes",
      status: "recorded"
    }
  ]

  const tutorials = [
    {
      id: 1,
      title: "Getting Started with the Trading Platform",
      description: "Complete walkthrough of our trading platform features",
      duration: "15 minutes",
      type: "video"
    },
    {
      id: 2,
      title: "How to Place Your First Trade",
      description: "Step-by-step guide to executing your first forex trade",
      duration: "10 minutes",
      type: "video"
    },
    {
      id: 3,
      title: "Setting Up Charts and Indicators",
      description: "Customize your trading workspace for optimal analysis",
      duration: "12 minutes",
      type: "video"
    },
    {
      id: 4,
      title: "Risk Management Tools",
      description: "Learn to use stop loss, take profit, and position sizing",
      duration: "18 minutes",
      type: "video"
    }
  ]

  const glossaryTerms = [
    { term: "Spread", definition: "The difference between the bid and ask price of a currency pair" },
    { term: "Leverage", definition: "The ability to control a large position with a relatively small amount of capital" },
    { term: "Pip", definition: "The smallest price move in a currency pair, typically the fourth decimal place" },
    { term: "Margin", definition: "The required deposit to open a leveraged trading position" },
    { term: "Lot", definition: "The standard trading unit in forex, typically 100,000 units of the base currency" },
    { term: "Bull Market", definition: "A market characterized by rising prices and optimistic sentiment" }
  ]

  return (
      <PageLayout>
        {/* Hero Section */}
        <section className="relative py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-4xl md:text-6xl mt-8 font-bold mb-6">
                Education <span className="text-[#EA5455]">Center</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Master forex trading with our comprehensive educational resources.
                From beginner courses to advanced strategies, we have everything you need to succeed.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Trading Courses */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Trading Courses</h2>
              <p className="text-xl text-muted-foreground">
                Structured learning paths to take you from beginner to professional trader.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {courses.map((course, index) => (
                <motion.div
                  key={course.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-2xl overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300"
                >
                  <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                    <TbBook className="h-16 w-16 text-[#EA5455]" />
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-semibold ${course.color} bg-current bg-opacity-10`}>
                        {course.level}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold mb-3">{course.title}</h3>
                    <p className="text-muted-foreground mb-4">{course.description}</p>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                      <div className="flex items-center gap-1">
                        <TbClock className="h-4 w-4" />
                        {course.duration}
                      </div>
                      <div className="flex items-center gap-1">
                        <TbBook className="h-4 w-4" />
                        {course.lessons} lessons
                      </div>
                    </div>
                    <button className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-2 px-5 rounded-lg font-semibold transition-all duration-300">
                      Start Course
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Webinars */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Live Webinars</h2>
              <p className="text-xl text-muted-foreground">
                Join our expert analysts for live market discussions and trading insights.
              </p>
            </motion.div>

            <div className="space-y-6">
              {webinars.map((webinar, index) => (
                <motion.div
                  key={webinar.id}
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <TbVideo className="h-6 w-6 text-[#EA5455]" />
                        <h3 className="text-xl font-bold">{webinar.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          webinar.status === 'upcoming'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {webinar.status === 'upcoming' ? 'Upcoming' : 'Recorded'}
                        </span>
                      </div>
                      <p className="text-muted-foreground mb-2">{webinar.presenter}</p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <TbCalendar className="h-4 w-4" />
                          {webinar.date}
                        </div>
                        <div className="flex items-center gap-1">
                          <TbClock className="h-4 w-4" />
                          {webinar.time}
                        </div>
                        <div>{webinar.duration}</div>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      {webinar.status === 'upcoming' ? (
                        <button className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-all duration-300">
                          Register
                        </button>
                      ) : (
                        <button className="border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white px-5 py-2 rounded-lg font-semibold transition-all duration-300 flex items-center gap-2">
                          <TbPlayerPlay className="h-4 w-4" />
                          Watch
                        </button>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Platform Tutorials */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Platform Tutorials</h2>
              <p className="text-xl text-muted-foreground">
                Quick video guides to help you master our trading platform.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {tutorials.map((tutorial, index) => (
                <motion.div
                  key={tutorial.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 group"
                >
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-[#EA5455]/10 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <TbPlayerPlay className="h-6 w-6 text-[#EA5455]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold mb-2">{tutorial.title}</h3>
                      <p className="text-muted-foreground mb-3">{tutorial.description}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <TbClock className="h-4 w-4" />
                          {tutorial.duration}
                        </div>
                        <button className="text-[#EA5455] hover:text-[#EA5455]/80 font-semibold text-sm transition-colors">
                          Watch Now →
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Trading Glossary */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Trading Glossary</h2>
              <p className="text-xl text-muted-foreground">
                Essential forex trading terms and definitions to build your knowledge.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {glossaryTerms.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
                >
                  <h3 className="text-lg font-bold text-[#EA5455] mb-2">{item.term}</h3>
                  <p className="text-muted-foreground">{item.definition}</p>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center mt-12"
            >
              <button className="border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white px-5 py-2 rounded-lg font-semibold transition-all duration-300">
                View Complete Glossary
              </button>
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-[#1E1E1E] rounded-3xl p-8 md:p-12 text-white"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Apply Your Knowledge?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Put your learning into practice with a demo account or start trading live with competitive spreads.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')}
                  className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-all duration-300"
                >
                  Try Demo Account
                </button>
                <button
                  onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                  className="border-2 border-white text-white hover:bg-white hover:text-[#1E1E1E] px-5 py-2 rounded-lg font-semibold transition-all duration-300"
                >
                  Open Live Account
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </PageLayout>
  )
}

export default EducationCenterPage