'use client'

import { useState, useEffect, useRef } from 'react'

interface YouTubeVideoBackgroundProps {
    videoId: string
    className?: string
    fallbackImage?: string
}

const YouTubeVideoBackground = ({
    videoId,
    className = '',
    fallbackImage = ''
}: YouTubeVideoBackgroundProps) => {
    const [isLoaded, setIsLoaded] = useState(false)
    const [hasError, setHasError] = useState(false)
    const [isClient, setIsClient] = useState(false)
    const [isMobile, setIsMobile] = useState(false)
    const iframeRef = useRef<HTMLIFrameElement>(null)

    useEffect(() => {
        setIsClient(true)

        // Detect mobile device and orientation
        const checkMobile = () => {
            const isMobileDevice = window.innerWidth <= 768 ||
                                 /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            setIsMobile(isMobileDevice)
        }

        checkMobile()
        window.addEventListener('resize', checkMobile)
        window.addEventListener('orientationchange', checkMobile)

        return () => {
            window.removeEventListener('resize', checkMobile)
            window.removeEventListener('orientationchange', checkMobile)
        }
    }, [])

    const handleLoad = () => {
        setIsLoaded(true)
        setHasError(false)
    }

    const handleError = () => {
        setHasError(true)
        setIsLoaded(false)
    }

    // YouTube embed URL with parameters for autoplay, loop, mute, and hide controls
    const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=1&loop=1&mute=1&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&playsinline=1&enablejsapi=1&playlist=${videoId}&disablekb=1&fs=0&cc_load_policy=0&hl=en&color=white&theme=dark&origin=${typeof window !== 'undefined' ? window.location.origin : ''}`

    if (!isClient) {
        return (
            <div
                className={`absolute inset-0 bg-gradient-to-br from-[#1E1E1E] via-gray-800 to-gray-900 ${className}`}
            />
        )
    }

    return (
        <div className={`absolute inset-0 overflow-hidden ${className}`}>
            {/* Video Background */}
            {!hasError && (
                <div className="absolute inset-0 w-full h-full">
                    <iframe
                        ref={iframeRef}
                        src={embedUrl}
                        title="Background Video"
                        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                        style={isMobile ? {
                            // Mobile portrait optimization - eliminate letterboxing
                            width: '100vw',
                            height: '177.78vw', // 16:9 aspect ratio (100vw * 16/9)
                            minWidth: '177.78vh', // Ensure minimum width based on viewport height
                            minHeight: '100vh',
                            maxWidth: 'none',
                            maxHeight: 'none',
                        } : {
                            // Desktop optimization
                            width: 'calc(100vw + 400px)',
                            height: 'calc(100vh + 400px)',
                            minWidth: '100%',
                            minHeight: '100%',
                        }}
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        allowFullScreen={false}
                        onLoad={handleLoad}
                        onError={handleError}
                        loading="lazy"
                    />
                </div>
            )}

            {/* Fallback Background */}
            {(hasError || !isLoaded) && (
                <div
                    className="absolute inset-0 bg-gradient-to-br from-[#1E1E1E] via-gray-800 to-gray-900 transition-opacity duration-1000"
                    style={{
                        opacity: hasError || !isLoaded ? 1 : 0
                    }}
                />
            )}

            {/* Dark Overlay for better text readability */}
            <div className="absolute inset-0 bg-black/40" />

            {/* Loading State */}
            {!isLoaded && !hasError && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
                    <div className="flex flex-col items-center space-y-4">
                        <div className="w-8 h-8 border-2 border-[#EA5455] border-t-transparent rounded-full animate-spin" />
                        <span className="text-white text-sm">Loading video...</span>
                    </div>
                </div>
            )}

            {/* Error State */}
            {hasError && (
                <div className="absolute top-4 right-4 z-10">
                    <div className="bg-red-500/80 text-white px-3 py-1 rounded-lg text-xs">
                        Video unavailable
                    </div>
                </div>
            )}
        </div>
    )
}

export default YouTubeVideoBackground
