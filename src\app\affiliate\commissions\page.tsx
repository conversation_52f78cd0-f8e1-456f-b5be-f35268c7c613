'use client'

import { motion } from 'framer-motion'
import { TbCash, TbTrendingUp, TbUsers, TbCalendar } from 'react-icons/tb'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateCommissionsPage = () => {
  const commissionTiers = [
    {
      tier: "Bronze",
      clients: "1-10 clients",
      commission: "$400",
      color: "text-amber-600",
      bgColor: "bg-amber-50 dark:bg-amber-900/20",
      features: ["Basic commission rate", "Monthly payments", "Email support"]
    },
    {
      tier: "Silver", 
      clients: "11-25 clients",
      commission: "$600",
      color: "text-gray-600",
      bgColor: "bg-gray-50 dark:bg-gray-900/20",
      features: ["Higher commission rate", "Bi-weekly payments", "Priority support"]
    },
    {
      tier: "Gold",
      clients: "26-50 clients", 
      commission: "$800",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
      features: ["Premium commission rate", "Weekly payments", "Dedicated manager"]
    },
    {
      tier: "Platinum",
      clients: "51+ clients",
      commission: "$1,200",
      color: "text-[#EA5455]",
      bgColor: "bg-[#EA5455]/10",
      features: ["Maximum commission rate", "Real-time payments", "VIP support"]
    }
  ]

  const paymentMethods = [
    { name: "Bank Transfer", fee: "Free", time: "1-3 business days" },
    { name: "PayPal", fee: "2.9%", time: "Instant" },
    { name: "Skrill", fee: "1.45%", time: "Instant" },
    { name: "Neteller", fee: "1.45%", time: "Instant" },
    { name: "Cryptocurrency", fee: "Network fees", time: "10-60 minutes" }
  ]

  return (
    <PageLayout>
      <div className="bg-gray-50 dark:bg-gray-900">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center text-white"
            >
              <h1 className="text-4xl text-gray-300 mt-8 md:text-6xl font-bold mb-6">
                Commission Structure
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Competitive commission rates with transparent tier-based rewards
              </p>
            </motion.div>
          </div>
        </section>

        {/* Commission Tiers */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Commission Tiers
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Earn more as you refer more clients with our progressive commission structure
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {commissionTiers.map((tier, index) => (
                <motion.div
                  key={tier.tier}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`${tier.bgColor} rounded-2xl p-6 border border-gray-200 dark:border-gray-700`}
                >
                  <div className="text-center mb-6">
                    <h3 className={`text-2xl font-bold mb-2 ${tier.color}`}>{tier.tier}</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">{tier.clients}</p>
                    <div className={`text-4xl font-bold ${tier.color}`}>{tier.commission}</div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">per qualified client</p>
                  </div>
                  
                  <ul className="space-y-2">
                    {tier.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                        <span className="w-2 h-2 bg-[#28C76F] rounded-full mr-3 flex-shrink-0"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                How Commissions Work
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Simple and transparent commission calculation process
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <TbUsers className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  1. Refer Clients
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Share your unique referral link and bring new clients to MyBrokerForex
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#28C76F] rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <TbTrendingUp className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  2. Client Qualifies
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Client opens an account, makes a deposit, and completes qualifying trades
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <TbCash className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  3. Earn Commission
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Receive your commission based on your current tier level
                </p>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Payment Methods */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Payment Methods
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Choose from multiple payment options for receiving your commissions
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto">
              {paymentMethods.map((method, index) => (
                <motion.div
                  key={method.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-4 flex items-center justify-between"
                >
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {method.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Processing time: {method.time}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Fee</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {method.fee}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-[#EA5455]">
          <div className="max-w-7xl mx-auto px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start Earning?
              </h2>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                Join our affiliate program today and start earning competitive commissions
              </p>
              <button 
                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
              >
                Join Affiliate Program
              </button>
            </motion.div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}

export default AffiliateCommissionsPage
