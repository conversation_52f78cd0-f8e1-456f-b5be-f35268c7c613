'use client'

import { motion } from 'framer-motion'
import { TbTrendingUp, TbWorld, TbShield, TbClock, TbChartLine, TbBuildingBank } from 'react-icons/tb'
import { HiArrowRight } from 'react-icons/hi2'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function IndicesPage() {
    const majorIndices = [
        {
            name: 'S&P 500',
            symbol: 'US500',
            price: '4,567.89',
            change: '+0.45%',
            flag: '🇺🇸'
        },
        {
            name: 'NASDAQ 100',
            symbol: 'NAS100',
            price: '15,234.56',
            change: '+0.78%',
            flag: '🇺🇸'
        },
        {
            name: '<PERSON>',
            symbol: 'US30',
            price: '34,567.12',
            change: '+0.23%',
            flag: '🇺🇸'
        },
        {
            name: 'FTSE 100',
            symbol: 'UK100',
            price: '7,456.78',
            change: '-0.12%',
            flag: '🇬🇧'
        },
        {
            name: 'DAX 40',
            symbol: 'GER40',
            price: '15,678.90',
            change: '+0.34%',
            flag: '🇩🇪'
        },
        {
            name: 'Nikkei 225',
            symbol: 'JPN225',
            price: '32,456.78',
            change: '-0.18%',
            flag: '🇯🇵'
        }
    ]

    const features = [
        {
            icon: TbTrendingUp,
            title: 'Market Exposure',
            description: 'Gain exposure to entire markets and sectors with a single trade on major indices.'
        },
        {
            icon: TbWorld,
            title: 'Global Markets',
            description: 'Trade indices from major economies including US, Europe, Asia, and Australia.'
        },
        {
            icon: TbShield,
            title: 'Diversification',
            description: 'Diversify your portfolio by trading baskets of top-performing stocks.'
        },
        {
            icon: TbClock,
            title: 'Extended Hours',
            description: 'Trade indices beyond traditional market hours with our extended trading sessions.'
        }
    ]

    const tradingBenefits = [
        'Trade 20+ major global indices',
        'Competitive spreads and low costs',
        'No stamp duty or commission fees',
        'Advanced charting and analysis tools',
        'Real-time market data and news',
        'Mobile and desktop platforms'
    ]

    const indexCategories = [
        {
            region: 'United States',
            indices: ['S&P 500', 'NASDAQ 100', 'Dow Jones', 'Russell 2000'],
            flag: '🇺🇸',
            description: 'Trade the world\'s largest equity markets'
        },
        {
            region: 'Europe',
            indices: ['FTSE 100', 'DAX 40', 'CAC 40', 'EURO STOXX 50'],
            flag: '🇪🇺',
            description: 'Access major European stock indices'
        },
        {
            region: 'Asia Pacific',
            indices: ['Nikkei 225', 'Hang Seng', 'ASX 200', 'KOSPI'],
            flag: '🌏',
            description: 'Trade Asian and Pacific market indices'
        }
    ]

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-5xl  text-gray-300 font-bold mb-6 mt-8">
                                Indices Trading
                            </h1>
                            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Trade CFDs on major global stock indices. Get exposure to entire markets
                                and economies with competitive spreads and professional trading tools.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center"
                                >
                                    Start Trading Indices
                                    <HiArrowRight className="ml-2 w-5 h-5" />
                                </button>
                                <button className="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-gray-900 transition-colors duration-200">
                                    Try Demo Account
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* Live Prices Section */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Live Index Prices
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Real-time pricing on major global indices
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {majorIndices.map((index, idx) => (
                                <motion.div
                                    key={index.symbol}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * idx }}
                                    className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex items-center">
                                            <span className="text-2xl mr-3">{index.flag}</span>
                                            <div>
                                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                                    {index.name}
                                                </h3>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    {index.symbol}
                                                </p>
                                            </div>
                                        </div>
                                        <TbChartLine className="w-6 h-6 text-[#EA5455]" />
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {index.price}
                                        </span>
                                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                            index.change.startsWith('+') 
                                                ? 'bg-[#28C76F] bg-opacity-10 text-[#28C76F]' 
                                                : 'bg-[#EA5455] bg-opacity-10 text-[#EA5455]'
                                        }`}>
                                            {index.change}
                                        </span>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Why Trade Indices?
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Discover the benefits of index trading
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <feature.icon className="w-8 h-8 text-white" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Regional Markets */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Global Market Access
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Trade indices from major economies worldwide
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {indexCategories.map((category, index) => (
                                <motion.div
                                    key={category.region}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg"
                                >
                                    <div className="text-center mb-6">
                                        <span className="text-4xl mb-4 block">{category.flag}</span>
                                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                            {category.region}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {category.description}
                                        </p>
                                    </div>
                                    <ul className="space-y-2">
                                        {category.indices.map((indexName, idx) => (
                                            <li key={idx} className="flex items-center">
                                                <div className="w-2 h-2 bg-[#EA5455] rounded-full mr-3"></div>
                                                <span className="text-gray-700 dark:text-gray-300">{indexName}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Trading Benefits Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                            <motion.div
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Advanced Trading Platform
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                    Trade global indices with our professional platform featuring 
                                    real-time data, advanced charting, and seamless execution.
                                </p>
                                <ul className="space-y-4">
                                    {tradingBenefits.map((benefit, index) => (
                                        <li key={index} className="flex items-center">
                                            <div className="w-2 h-2 bg-[#EA5455] rounded-full mr-4"></div>
                                            <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                                        </li>
                                    ))}
                                </ul>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, x: 30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl"
                            >
                                <div className="text-center">
                                    <TbBuildingBank className="w-16 h-16 text-[#EA5455] mx-auto mb-6" />
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        Start Trading Today
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        Open your account and start trading global indices with 
                                        competitive spreads and professional tools.
                                    </p>
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="w-full py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                    >
                                        Open Trading Account
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </section>
            </div>
        </PublicPageLayout>
    )
}
