'use client'

import { motion } from 'framer-motion'
import CurrencyWidget from '@/components/shared/CurrencyWidget'

import HydrationBoundary from '@/components/shared/HydrationBoundary'
import type { Mode } from '@/@types/theme'

interface TradingWidgetsProps {
    mode: Mode
}

const TradingWidgets = ({ mode }: TradingWidgetsProps) => {
    return (
        <div className="relative -mt-[30px] sm:-mt-[50px] z-30" suppressHydrationWarning>
            <div className="container mx-auto px-4 sm:px-6">
                {/* Forex Widget - Positioned to overlap video background */}
                <motion.div
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                    className="w-[95%] sm:w-[85%] md:w-[75%] lg:w-[70%] mx-auto bg-white dark:bg-gray-800 rounded-xl sm:rounded-2xl lg:rounded-[32px] mb-8 sm:mb-12 relative shadow-2xl overflow-hidden"
                    suppressHydrationWarning
                >
                    <div className="p-4 sm:p-6" suppressHydrationWarning>
                        {/* Real-time Currency Widget */}
                        <HydrationBoundary
                            fallback={
                                <div className="h-48 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg flex items-center justify-center">
                                    <span className="text-gray-500 dark:text-gray-400">Loading forex data...</span>
                                </div>
                            }
                            suppressHydrationWarning
                        >
                            <CurrencyWidget />
                        </HydrationBoundary>
                    </div>
                </motion.div>


            </div>
        </div>
    )
}

export default TradingWidgets
