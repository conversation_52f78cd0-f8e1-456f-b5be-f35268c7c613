import { NextResponse } from 'next/server'
import pool from '@/server/db'

export async function POST() {
    try {
        // Create user_profiles table
        await pool.execute(`
            CREATE TABLE IF NOT EXISTS user_profiles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL DEFAULT 1,
                firstName VARCHAR(255),
                lastName VARCHAR(255),
                email VARCHAR(255),
                phoneNumber VARCHAR(50),
                dialCode VARCHAR(10),
                img VARCHAR(500),
                country VARCHAR(255),
                address TEXT,
                postcode VARCHAR(20),
                city VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_user (user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `)

        return NextResponse.json({ 
            success: true, 
            message: 'User profiles table created successfully' 
        })
    } catch (error) {
        console.error('Database setup error:', error)
        return NextResponse.json({ 
            error: 'Failed to create user profiles table',
            details: error 
        }, { status: 500 })
    }
}
