'use client'

import { useEffect, useState, ReactNode } from 'react'
import useTheme from '@/utils/hooks/useTheme'
import { MODE_DARK, MODE_LIGHT } from '@/constants/theme.constant'

interface ThemeSafeWrapperProps {
    children: ReactNode
    className?: string
}

/**
 * React 19 compatible theme wrapper that prevents hydration mismatches
 * and DOM manipulation conflicts during theme switching
 */
const ThemeSafeWrapper = ({ children, className = '' }: ThemeSafeWrapperProps) => {
    const [isClient, setIsClient] = useState(false)
    const [pendingThemeChange, setPendingThemeChange] = useState(false)
    
    const { mode } = useTheme((state) => ({
        mode: state.mode
    }))

    useEffect(() => {
        setIsClient(true)
    }, [])

    useEffect(() => {
        if (!isClient) return

        // Handle theme changes with proper timing
        if (pendingThemeChange) {
            const timer = setTimeout(() => {
                setPendingThemeChange(false)
            }, 100) // Allow DOM to settle

            return () => clearTimeout(timer)
        }
    }, [isClient, pendingThemeChange])

    // Listen for theme changes
    useEffect(() => {
        if (isClient) {
            setPendingThemeChange(true)
        }
    }, [mode, isClient])

    const wrapperClass = `${className} ${pendingThemeChange ? 'transition-colors duration-200' : ''}`

    return (
        <div className={wrapperClass} suppressHydrationWarning>
            {children}
        </div>
    )
}

export default ThemeSafeWrapper
