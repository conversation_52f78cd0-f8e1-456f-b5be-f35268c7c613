'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { TbArrowsExchange, TbClock, TbTrendingUp, TbWorld } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function CurrencyConverterPage() {
    const [fromCurrency, setFromCurrency] = useState('USD')
    const [toCurrency, setToCurrency] = useState('EUR')
    const [amount, setAmount] = useState('1000')
    const [convertedAmount, setConvertedAmount] = useState(0)
    const [exchangeRate, setExchangeRate] = useState(0)

    const currencies = [
        { code: 'USD', name: 'US Dollar', flag: '🇺🇸' },
        { code: 'EUR', name: 'Euro', flag: '🇪🇺' },
        { code: 'GBP', name: 'British Pound', flag: '🇬🇧' },
        { code: 'JPY', name: 'Japanese Yen', flag: '🇯🇵' },
        { code: 'AUD', name: 'Australian Dollar', flag: '🇦🇺' },
        { code: 'CAD', name: 'Canadian Dollar', flag: '🇨🇦' },
        { code: 'CHF', name: 'Swiss Franc', flag: '🇨🇭' },
        { code: 'NZD', name: 'New Zealand Dollar', flag: '🇳🇿' }
    ]

    const convertCurrency = () => {
        const inputAmount = parseFloat(amount)
        if (!inputAmount || inputAmount <= 0) {
            setConvertedAmount(0)
            setExchangeRate(0)
            return
        }

        // Simplified exchange rate calculation (in reality, this would use live rates)
        let rate = 1

        // Same currency conversion
        if (fromCurrency === toCurrency) {
            rate = 1.0
        }
        // Specific currency pair rates
        else if (fromCurrency === 'USD' && toCurrency === 'EUR') rate = 0.85
        else if (fromCurrency === 'EUR' && toCurrency === 'USD') rate = 1.18
        else if (fromCurrency === 'USD' && toCurrency === 'GBP') rate = 0.73
        else if (fromCurrency === 'GBP' && toCurrency === 'USD') rate = 1.37
        else if (fromCurrency === 'USD' && toCurrency === 'JPY') rate = 110.25
        else if (fromCurrency === 'JPY' && toCurrency === 'USD') rate = 0.009
        else if (fromCurrency === 'EUR' && toCurrency === 'GBP') rate = 0.86
        else if (fromCurrency === 'GBP' && toCurrency === 'EUR') rate = 1.16
        // Default rate for other pairs (approximate)
        else rate = 1.05

        setExchangeRate(rate)
        setConvertedAmount(inputAmount * rate)
    }

    const swapCurrencies = () => {
        setFromCurrency(toCurrency)
        setToCurrency(fromCurrency)
    }

    const features = [
        {
            icon: TbClock,
            title: 'Real-Time Rates',
            description: 'Get live exchange rates updated every second from major financial markets worldwide.'
        },
        {
            icon: TbWorld,
            title: 'Global Currencies',
            description: 'Convert between 180+ currencies including major, minor, and exotic currency pairs.'
        },
        {
            icon: TbTrendingUp,
            title: 'Historical Data',
            description: 'Access historical exchange rates and charts to analyze currency trends over time.'
        },
        {
            icon: TbArrowsExchange,
            title: 'Quick Conversion',
            description: 'Instantly convert any amount with our fast and accurate currency conversion tool.'
        }
    ]

    return (
        <div className="mt-0">
            <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
            {/* Hero Section */}
            <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        <h1 className="text-5xl text-gray-300 mt-8 font-bold mb-6">
                            Currency Converter
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Convert currencies instantly with real-time exchange rates. 
                            Essential tool for forex traders and international businesses.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Converter Section */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        {/* Converter */}
                        <motion.div
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg"
                        >
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                                Convert Currency
                            </h2>

                            <div className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Amount
                                    </label>
                                    <input
                                        type="number"
                                        value={amount}
                                        onChange={(e) => setAmount(e.target.value)}
                                        placeholder="1000"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-lg"
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            From
                                        </label>
                                        <select
                                            value={fromCurrency}
                                            onChange={(e) => setFromCurrency(e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        >
                                            {currencies.map(currency => (
                                                <option key={currency.code} value={currency.code}>
                                                    {currency.flag} {currency.code}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            To
                                        </label>
                                        <select
                                            value={toCurrency}
                                            onChange={(e) => setToCurrency(e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        >
                                            {currencies.map(currency => (
                                                <option key={currency.code} value={currency.code}>
                                                    {currency.flag} {currency.code}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>

                                <div className="flex justify-center">
                                    <button
                                        onClick={swapCurrencies}
                                        className="p-3 bg-gray-100 dark:bg-gray-700 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                                    >
                                        <TbArrowsExchange className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                                    </button>
                                </div>

                                <button
                                    onClick={convertCurrency}
                                    className="w-full py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                >
                                    Convert Currency
                                </button>

                                {convertedAmount > 0 && (
                                    <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6"
                                    >
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            Conversion Result
                                        </h3>
                                        <p className="text-3xl font-bold text-gray-900 dark:text-white">
                                            {convertedAmount.toLocaleString(undefined, { 
                                                minimumFractionDigits: 2, 
                                                maximumFractionDigits: 2 
                                            })} {toCurrency}
                                        </p>
                                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                                            1 {fromCurrency} = {exchangeRate.toFixed(4)} {toCurrency}
                                        </p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Last updated: {new Date().toLocaleTimeString()}
                                        </p>
                                    </motion.div>
                                )}
                            </div>
                        </motion.div>

                        {/* Information */}
                        <motion.div
                            initial={{ opacity: 0, x: 30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            className="space-y-8"
                        >
                            <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg">
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                    Live Exchange Rates
                                </h3>
                                <div className="space-y-4">
                                    <div className="flex justify-between items-center py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span className="text-gray-600 dark:text-gray-300">EUR/USD</span>
                                        <span className="font-semibold text-gray-900 dark:text-white">1.1850</span>
                                    </div>
                                    <div className="flex justify-between items-center py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span className="text-gray-600 dark:text-gray-300">GBP/USD</span>
                                        <span className="font-semibold text-gray-900 dark:text-white">1.3720</span>
                                    </div>
                                    <div className="flex justify-between items-center py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span className="text-gray-600 dark:text-gray-300">USD/JPY</span>
                                        <span className="font-semibold text-gray-900 dark:text-white">110.25</span>
                                    </div>
                                    <div className="flex justify-between items-center py-3">
                                        <span className="text-gray-600 dark:text-gray-300">AUD/USD</span>
                                        <span className="font-semibold text-gray-900 dark:text-white">0.7340</span>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg">
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                    Currency Trading Tips
                                </h3>
                                <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                                    <li className="flex items-start">
                                        <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        Monitor economic news and events that affect currency values
                                    </li>
                                    <li className="flex items-start">
                                        <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        Use technical analysis to identify trading opportunities
                                    </li>
                                    <li className="flex items-start">
                                        <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        Practice risk management with proper position sizing
                                    </li>
                                    <li className="flex items-start">
                                        <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        Keep track of central bank policies and interest rates
                                    </li>
                                </ul>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20 bg-white dark:bg-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center mb-16"
                    >
                        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                            Why Use Our Currency Converter?
                        </h2>
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {features.map((feature, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 * index }}
                                className="text-center"
                            >
                                <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                    <feature.icon className="w-8 h-8 text-white" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    {feature.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    {feature.description}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>
            </div>
                    </PublicPageLayout>
        </div>
    )
}
