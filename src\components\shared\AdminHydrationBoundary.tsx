'use client'

import { useEffect, useState, ReactNode } from 'react'
import Loading from '@/components/shared/Loading'

interface AdminHydrationBoundaryProps {
    children: ReactNode
    fallback?: ReactNode
    suppressHydrationWarning?: boolean
    className?: string
}

/**
 * Admin-specific React 19 compatible hydration boundary
 * Optimized for admin panel components with proper loading states
 */
const AdminHydrationBoundary = ({ 
    children, 
    fallback,
    suppressHydrationWarning = true,
    className = ''
}: AdminHydrationBoundaryProps) => {
    const [isHydrated, setIsHydrated] = useState(false)

    useEffect(() => {
        // Mark as hydrated after client-side mount
        setIsHydrated(true)
    }, [])

    // During SSR and initial client render, show fallback
    if (!isHydrated) {
        return (
            <div className={className} suppressHydrationWarning={suppressHydrationWarning}>
                {fallback || (
                    <div className="flex items-center justify-center p-4">
                        <Loading loading />
                    </div>
                )}
            </div>
        )
    }

    // After hydration, show actual content
    return <div className={className}>{children}</div>
}

export default AdminHydrationBoundary
