'use client'

import { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { TrendingUpIcon, TrendingDownIcon } from 'lucide-react'
import { forexDataService, UpdateTimeline, TIMELINE_INTERVALS } from '@/services/ForexDataService'

interface MarketData {
    symbol: string
    price: number
    change: number
    changePercent: number
    category: 'forex' | 'commodities' | 'crypto' | 'stocks' | 'indices'
}

interface LiveCurrencyTickerProps {
    className?: string
    showTimelineSelector?: boolean
}

const LiveCurrencyTicker = ({ className = '', showTimelineSelector = true }: LiveCurrencyTickerProps) => {
    const [selectedCategory, setSelectedCategory] = useState<string>('forex')
    const [selectedTimeline, setSelectedTimeline] = useState<UpdateTimeline>('1m')
    const [isClient, setIsClient] = useState(false)
    const [marketData, setMarketData] = useState<MarketData[]>([
        // Forex
        { symbol: 'EUR/USD', price: 1.0856, change: 0.0012, changePercent: 0.11, category: 'forex' },
        { symbol: 'GBP/USD', price: 1.2634, change: -0.0023, changePercent: -0.18, category: 'forex' },
        { symbol: 'USD/JPY', price: 149.82, change: 0.45, changePercent: 0.30, category: 'forex' },
        { symbol: 'AUD/USD', price: 0.6789, change: 0.0034, changePercent: 0.50, category: 'forex' },
        { symbol: 'USD/CAD', price: 1.3456, change: -0.0012, changePercent: -0.09, category: 'forex' },
        { symbol: 'USD/CHF', price: 0.8923, change: 0.0018, changePercent: 0.20, category: 'forex' },
        
        // Commodities
        { symbol: 'GOLD', price: 2034.50, change: 12.30, changePercent: 0.61, category: 'commodities' },
        { symbol: 'SILVER', price: 24.85, change: -0.15, changePercent: -0.60, category: 'commodities' },
        { symbol: 'OIL', price: 78.45, change: 1.20, changePercent: 1.55, category: 'commodities' },
        { symbol: 'COPPER', price: 3.85, change: 0.05, changePercent: 1.32, category: 'commodities' },
        { symbol: 'WHEAT', price: 645.25, change: -8.50, changePercent: -1.30, category: 'commodities' },
        
        // Crypto
        { symbol: 'BTC/USD', price: 43250.00, change: 850.00, changePercent: 2.01, category: 'crypto' },
        { symbol: 'ETH/USD', price: 2580.50, change: -45.20, changePercent: -1.72, category: 'crypto' },
        { symbol: 'ADA/USD', price: 0.485, change: 0.012, changePercent: 2.54, category: 'crypto' },
        { symbol: 'XRP/USD', price: 0.625, change: -0.008, changePercent: -1.26, category: 'crypto' },
        { symbol: 'SOL/USD', price: 98.75, change: 3.25, changePercent: 3.40, category: 'crypto' },
        
        // Stocks
        { symbol: 'AAPL', price: 185.25, change: 2.15, changePercent: 1.17, category: 'stocks' },
        { symbol: 'GOOGL', price: 142.80, change: -1.25, changePercent: -0.87, category: 'stocks' },
        { symbol: 'MSFT', price: 378.50, change: 4.80, changePercent: 1.28, category: 'stocks' },
        { symbol: 'TSLA', price: 248.75, change: -3.20, changePercent: -1.27, category: 'stocks' },
        { symbol: 'NVDA', price: 485.60, change: 12.40, changePercent: 2.62, category: 'stocks' },
        
        // Indices
        { symbol: 'S&P 500', price: 4785.25, change: 25.80, changePercent: 0.54, category: 'indices' },
        { symbol: 'NASDAQ', price: 15234.50, change: -45.20, changePercent: -0.30, category: 'indices' },
        { symbol: 'DOW', price: 37845.60, change: 125.40, changePercent: 0.33, category: 'indices' },
        { symbol: 'FTSE 100', price: 7685.25, change: 15.80, changePercent: 0.21, category: 'indices' },
        { symbol: 'DAX', price: 16425.80, change: -35.60, changePercent: -0.22, category: 'indices' },
    ])

    const categories = [
        { key: 'forex', label: 'Forex' },
        { key: 'commodities', label: 'Commodities' },
        { key: 'crypto', label: 'Crypto' },
        { key: 'stocks', label: 'Stocks' },
        { key: 'indices', label: 'Indices' },
    ]

    // Set client-side flag to prevent hydration mismatch
    useEffect(() => {
        setIsClient(true)
    }, [])

    const updateForexData = useCallback(async () => {
        if (!isClient) return

        try {
            forexDataService.setTimeline(selectedTimeline)
            const forexPairs = forexDataService.getAvailablePairs()
            const rates = await forexDataService.getRealTimeRates(forexPairs)

            if (rates.length > 0) {
                setMarketData(prevData => {
                    const updatedData = [...prevData]

                    // Update forex data with real rates
                    rates.forEach(rate => {
                        const index = updatedData.findIndex(item =>
                            item.symbol === rate.symbol && item.category === 'forex'
                        )
                        if (index !== -1) {
                            updatedData[index] = {
                                symbol: rate.symbol,
                                price: parseFloat(rate.price.toFixed(4)),
                                change: parseFloat(rate.change.toFixed(4)),
                                changePercent: parseFloat(rate.changePercent.toFixed(2)),
                                category: 'forex'
                            }
                        }
                    })

                    // Update non-forex data with simulated changes
                    return updatedData.map(item => {
                        if (item.category !== 'forex') {
                            const volatility = item.category === 'crypto' ? 0.02 :
                                             item.category === 'stocks' ? 0.015 : 0.005
                            const randomChange = (Math.random() - 0.5) * 2 * volatility
                            const newPrice = item.price * (1 + randomChange)
                            const priceChange = newPrice - item.price
                            const changePercent = (priceChange / item.price) * 100

                            return {
                                ...item,
                                price: parseFloat(newPrice.toFixed(item.category === 'crypto' ? 2 : 2)),
                                change: parseFloat(priceChange.toFixed(2)),
                                changePercent: parseFloat(changePercent.toFixed(2))
                            }
                        }
                        return item
                    })
                })
                console.log('Updated forex data in ticker from real API')
            } else {
                // Fallback to simulated updates
                updatePricesSimulated()
            }
        } catch (error) {
            console.error('Error updating forex data in ticker:', error)
            // Fallback to simulated updates
            updatePricesSimulated()
        }
    }, [isClient])

    const updatePricesSimulated = useCallback(() => {
        setMarketData(prevData =>
            prevData.map(item => {
                const volatility = item.category === 'crypto' ? 0.02 :
                                 item.category === 'stocks' ? 0.015 : 0.005
                const randomChange = (Math.random() - 0.5) * 2 * volatility
                const newPrice = item.price * (1 + randomChange)
                const priceChange = newPrice - item.price
                const changePercent = (priceChange / item.price) * 100

                return {
                    ...item,
                    price: parseFloat(newPrice.toFixed(item.category === 'forex' && item.symbol.includes('JPY') ? 2 :
                                                      item.category === 'crypto' ? 2 :
                                                      item.category === 'forex' ? 4 : 2)),
                    change: parseFloat(priceChange.toFixed(2)),
                    changePercent: parseFloat(changePercent.toFixed(2))
                }
            })
        )
    }, [])

    // Update prices based on selected timeline
    useEffect(() => {
        if (!isClient) return

        // Initial update
        updateForexData()

        const intervalTime = TIMELINE_INTERVALS[selectedTimeline]
        const interval = setInterval(updateForexData, intervalTime)
        return () => clearInterval(interval)
    }, [isClient, updateForexData, selectedTimeline])

    const filteredData = useMemo(() =>
        marketData.filter(item => item.category === selectedCategory),
        [marketData, selectedCategory]
    )

    const formatPrice = useCallback((price: number, symbol: string) => {
        if (symbol.includes('JPY') || symbol === 'BTC/USD' || symbol === 'ETH/USD') {
            return price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
        }
        if (symbol.includes('/')) {
            return price.toFixed(4)
        }
        return price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    }, [])

    return (
        <div className={`bg-white dark:bg-gray-900 border-y mt-5 border-gray-200 dark:border-gray-700 py-4 ${className}`}>
            <div className="container mx-auto px-4">
                {/* Timeline Selector */}
                {showTimelineSelector && (
                    <div className="flex items-center justify-center mb-3">
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Update every:</span>
                            <div className="flex space-x-1">
                                {(['5s', '1m', '15m', '1h'] as UpdateTimeline[]).map((timeline) => (
                                    <button
                                        key={timeline}
                                        onClick={() => setSelectedTimeline(timeline)}
                                        className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                                            selectedTimeline === timeline
                                                ? 'bg-[#EA5455] text-white'
                                                : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
                                        }`}
                                    >
                                        {timeline}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                {/* Category Tabs */}
                <div className="flex items-center justify-center mb-4 space-x-1">
                    {categories.map((category) => (
                        <button
                            key={category.key}
                            onClick={() => setSelectedCategory(category.key)}
                            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                                selectedCategory === category.key
                                    ? 'bg-[#EA5455] text-white'
                                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                            }`}
                        >
                            {category.label}
                        </button>
                    ))}
                </div>
                    
                {/* Scrolling Ticker */}
                <div className="relative overflow-hidden">
                    <div className="animate-scroll flex space-x-8 whitespace-nowrap">
                        {/* Duplicate the data for seamless scrolling */}
                        {[...filteredData, ...filteredData].map((item, index) => (
                            <div
                                key={`${item.symbol}-${index}`}
                                className="flex items-center space-x-3 bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 min-w-max"
                            >
                                <div className="flex items-center space-x-2">
                                    <span className="font-semibold text-gray-900 dark:text-gray-100">
                                        {item.symbol}
                                    </span>
                                    <span className="text-lg font-mono text-gray-800 dark:text-gray-200">
                                        {formatPrice(item.price, item.symbol)}
                                    </span>
                                </div>
                                <div className="flex items-center space-x-1">
                                    {item.changePercent > 0 ? (
                                        <TrendingUpIcon className="h-4 w-4 text-[#28C76F]" />
                                    ) : (
                                        <TrendingDownIcon className="h-4 w-4 text-[#EA5455]" />
                                    )}
                                    <span className={`text-sm font-medium ${
                                        item.changePercent > 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'
                                    }`}>
                                        {item.changePercent > 0 ? '+' : ''}{item.changePercent}%
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Live indicator */}
                <div className="flex items-center justify-center mt-3">
                    <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-[#28C76F] rounded-full animate-pulse"></div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                            Live Market Data • Updates every 30 seconds
                        </span>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default memo(LiveCurrencyTicker)
