import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket } from 'mysql2'

// GET /api/cms/blog/stats - Get blog posts statistics
export async function GET(request: NextRequest) {
    try {
        // Get total count
        const [totalResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as total FROM posts'
        )
        const total = totalResult[0].total

        // Get published count
        const [publishedResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM posts WHERE status = "published"'
        )
        const published = publishedResult[0].count

        // Get draft count
        const [draftResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM posts WHERE status = "draft"'
        )
        const draft = draftResult[0].count

        // Get archived count
        const [archivedResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM posts WHERE status = "archived"'
        )
        const archived = archivedResult[0].count

        // Get recent posts
        const [recentPosts] = await pool.execute<RowDataPacket[]>(
            'SELECT id, title, status, created_at, updated_at FROM posts ORDER BY updated_at DESC LIMIT 5'
        )

        return NextResponse.json({
            success: true,
            data: {
                total,
                published,
                draft,
                archived,
                recent: recentPosts
            }
        })
    } catch (error) {
        console.error('Error fetching blog stats:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch blog statistics' },
            { status: 500 }
        )
    }
} 