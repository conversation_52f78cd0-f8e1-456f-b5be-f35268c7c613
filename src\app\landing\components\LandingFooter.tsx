'use client'

import React, { useState } from 'react'
import Container from './LandingContainer'
import Button from '@/components/ui/Button'
import AuroraBackground from './AuroraBackground'
import { motion } from 'framer-motion'
import { MODE_DARK, MODE_LIGHT } from '@/constants/theme.constant'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Logo from '@/components/template/Logo'
import { TbMail, TbPhone, TbMapPin, TbBrandFacebook, TbBrandTwitter, TbBrandLinkedin, TbBrandYoutube, TbShield, TbCertificate, TbArrowRight, TbChartLine, TbChevronDown, TbChevronUp } from 'react-icons/tb'
import type { Mode } from '@/@types/theme'

const LandingFooter = ({ mode }: { mode: Mode }) => { // eslint-disable-line @typescript-eslint/no-unused-vars
    const year = new Date().getFullYear()
    const router = useRouter()

    // Mobile footer section collapse states
    const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
        trading: false,
        tools: false,
        education: false,
        affiliate: false,
        company: false,
        legal: false
    })

    const toggleSection = (section: string) => {
        setExpandedSections(prev => ({
            ...prev,
            [section]: !prev[section]
        }))
    }

    const handleStartTrading = () => {
        if (typeof window !== 'undefined') {
            window.open('https://mbf.mybrokerforex.com/user/register', '_blank')
        }
    }

    const tradingLinks = [
        { name: 'Account Types', href: '/account-type' },
        { name: 'Web Platform', href: '/trading/web-platform' },
        { name: 'Desktop Platform', href: '/trading/desktop' },
        { name: 'Mobile Trading', href: '/trading/mobile' },
        { name: 'Forex', href: '/trading/forex' },
        { name: 'Commodities', href: '/trading/commodities' },
        { name: 'Indices', href: '/trading/indices' },
        { name: 'Cryptocurrencies', href: '/trading/crypto' },
        { name: 'Market Analysis', href: '/trading/analysis' },
        { name: 'Trading Signals', href: '/trading/signals' },
        { name: 'Economic Calendar', href: '/trading/calendar' },
        { name: 'Trading APIs', href: '/trading/apis' },
    ]

    const toolsLinks = [
        { name: 'Pip Calculator', href: '/tools/pip-calculator' },
        { name: 'Margin Calculator', href: '/tools/margin-calculator' },
        { name: 'Profit Calculator', href: '/tools/profit-calculator' },
        { name: 'Risk Calculator', href: '/tools/risk-calculator' },
        { name: 'Live Rates', href: '/tools/live-rates' },
        { name: 'Market Hours', href: '/tools/market-hours' },
        { name: 'Currency Converter', href: '/tools/currency-converter' },
        { name: 'Volatility Tracker', href: '/tools/volatility' },
        { name: 'Correlation Matrix', href: '/tools/correlation' },
        { name: 'Fibonacci Calculator', href: '/tools/fibonacci' },
        { name: 'Position Size Calculator', href: '/tools/position-size' },
        { name: 'Swap Calculator', href: '/tools/swap-calculator' },
    ]

    const educationLinks = [
        { name: 'Trading Basics', href: '/education/trading-basics' },
        { name: 'Advanced Strategies', href: '/education/advanced-strategies' },
        { name: 'Market Analysis', href: '/education/market-analysis' },
        { name: 'Risk Management', href: '/education/risk-management' },
        { name: 'Video Tutorials', href: '/education/video-tutorials' },
        { name: 'Trading Webinars', href: '/education/trading-webinars' },
        { name: 'E-books & Guides', href: '/education/ebooks-guides' },
        { name: 'Demo Account', href: '/education/demo-account' },
        { name: 'Trading Glossary', href: '/education/glossary' }
    ]

    const companyLinks = [
        { name: 'About Us', href: '/about' },
        { name: 'Contact Us', href: '/contact' },
        { name: 'Support Center', href: '/support' },
        { name: 'Promotions', href: '/promotion' },
        { name: 'News & Analysis', href: '/news' },
        { name: 'Careers', href: '#' },
    ]

    const affiliateLinks = [
        { name: 'Affiliate Program', href: '/affiliate' },
        { name: 'How It Works', href: '/affiliate/how-it-works' },
        { name: 'Getting Started', href: '/affiliate/getting-started' },
        { name: 'Commission Structure', href: '/affiliate/commissions' },
        { name: 'Payment Methods', href: '/affiliate/payments' },
        { name: 'Marketing Materials', href: '/affiliate/materials' },
        { name: 'Tracking Tools', href: '/affiliate/tracking' },
        { name: 'Reports & Analytics', href: '/affiliate/reports' },
        { name: 'API Integration', href: '/affiliate/api' },
        { name: 'Affiliate Portal', href: 'https://mbf.mybrokerforex.com/user/login' },
        { name: 'FAQ', href: '/affiliate/faq' },
        { name: 'Terms & Conditions', href: '/affiliate/terms' },
        { name: 'Support Center', href: '/affiliate/support' },
    ]

    const legalLinks = [
        { name: 'Terms & Conditions', href: '/affiliate/terms' },
        { name: 'Privacy Policy', href: '#' },
        { name: 'Risk Disclosure', href: '#' },
        { name: 'Regulatory Information', href: '#' },
        { name: 'Complaints Procedure', href: '#' },
        { name: 'Cookie Policy', href: '#' },
    ]

    const socialLinks = [
        { name: 'Facebook', href: 'https://facebook.com/mybrokerforex', icon: TbBrandFacebook },
        { name: 'Twitter', href: 'https://twitter.com/mybrokerforex', icon: TbBrandTwitter },
        { name: 'LinkedIn', href: 'https://linkedin.com/company/mybrokerforex', icon: TbBrandLinkedin },
        { name: 'YouTube', href: 'https://youtube.com/mybrokerforex', icon: TbBrandYoutube }
    ]

    return (
        <footer className="bg-[#1E1E1E] text-white relative z-20" suppressHydrationWarning>
            {/* CTA Section */}
            <div className="relative" suppressHydrationWarning>
                <Container className="relative">
                    <div className="py-16" suppressHydrationWarning>
                        <AuroraBackground
                            className="rounded-3xl"
                            auroraClassName="rounded-3xl"
                        >
                            <motion.div
                                initial={{ opacity: 0.0, y: 40 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{
                                    delay: 0.3,
                                    duration: 0.3,
                                    ease: 'easeInOut',
                                }}
                                className="relative flex flex-col gap-4 items-center justify-center py-20 px-8 text-center"
                                suppressHydrationWarning
                            >
                                <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white px-4 sm:px-0">Ready to Start Trading?</h2>
                                <p className="mt-3 sm:mt-4 max-w-[500px] mx-auto text-base sm:text-lg text-gray-700 dark:text-gray-300 px-4 sm:px-0">
                                    Join thousands of successful traders on MyBrokerForex.
                                    Open your account today and start trading with competitive spreads.
                                </p>
                                <div className="mt-6 sm:mt-8 flex flex-col sm:flex-row gap-3 sm:gap-4 px-4 sm:px-0" suppressHydrationWarning>
                                    <Button
                                        variant="solid"
                                        size="lg"
                                        onClick={handleStartTrading}
                                        className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-8 py-3 text-lg"
                                        icon={<TbArrowRight />}
                                        iconAlignment="end"
                                    >
                                        <span>Open Live Account</span>
                                    </Button>
                                    <Button
                                        variant="plain"
                                        size="lg"
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')}
                                        className="border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white dark:border-white dark:text-white dark:hover:bg-white dark:hover:text-[#1E1E1E] px-8 py-3 text-lg"
                                        icon={<TbChartLine />}
                                        iconAlignment="start"
                                    >
                                        <span>Try Demo Account</span>
                                    </Button>
                                </div>
                            </motion.div>
                        </AuroraBackground>
                    </div>
                </Container>
            </div>

            {/* Main Footer Content */}
            <div className="bg-[#1E1E1E] border-t border-gray-800" suppressHydrationWarning>
                <Container>
                    <div className="py-16" suppressHydrationWarning>
                        {/* Desktop Footer Layout */}
                        <div className="hidden lg:grid lg:grid-cols-6 gap-8" suppressHydrationWarning>
                            {/* Company Info */}
                            <div className="lg:col-span-2" suppressHydrationWarning>
                                <Link href="/" className="flex items-center gap-2 mb-6">
                                    <Logo mode="dark" logoWidth={160} logoHeight={50} />
                                </Link>
                                <p className="text-gray-300 mb-6 leading-relaxed">
                                    MyBrokerForex is a leading online forex broker providing professional trading services
                                    to retail and institutional clients worldwide. Trade with confidence on our secure,
                                    regulated platform with competitive spreads and expert support.
                                </p>

                                {/* Contact Info */}
                                <div className="space-y-3 mb-6" suppressHydrationWarning>
                                    <div className="flex items-center gap-3">
                                        <TbMail className="h-5 w-5 text-[#EA5455]" />
                                        <span className="text-gray-300"><EMAIL></span>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <TbPhone className="h-5 w-5 text-[#EA5455]" />
                                        <span className="text-gray-300">+****************</span>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <TbMapPin className="h-5 w-5 text-[#EA5455]" />
                                        <span className="text-gray-300">123 Financial District, London EC2V 8RF, UK</span>
                                    </div>
                                </div>

                                {/* Social Links */}
                                <div className="flex gap-4" suppressHydrationWarning>
                                    {socialLinks.map((social) => {
                                        const IconComponent = social.icon
                                        return (
                                            <a
                                                key={social.name}
                                                href={social.href}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="w-10 h-10 bg-gray-800 hover:bg-[#EA5455] rounded-full flex items-center justify-center transition-colors duration-300"
                                                suppressHydrationWarning
                                            >
                                                <IconComponent className="h-5 w-5 text-white" />
                                            </a>
                                        )
                                    })}
                                </div>
                            </div>

                            {/* Trading Links */}
                            <div>
                                <h3 className="text-lg font-semibold text-white mb-6">Trading</h3>
                                <div className="space-y-2">
                                    {tradingLinks.map((link) => (
                                        <Link
                                            key={link.name}
                                            href={link.href}
                                            className="text-xs text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                            suppressHydrationWarning
                                        >
                                            {link.name}
                                        </Link>
                                    ))}
                                </div>
                            </div>

                            {/* Tools Links */}
                            <div>
                                <h3 className="text-lg font-semibold text-white mb-6">Tools</h3>
                                <div className="space-y-2">
                                    {toolsLinks.map((link) => (
                                        <Link
                                            key={link.name}
                                            href={link.href}
                                            className="text-xs text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                            suppressHydrationWarning
                                        >
                                            {link.name}
                                        </Link>
                                    ))}
                                </div>
                            </div>

                            {/* Education Links */}
                            <div>
                                <h3 className="text-lg font-semibold text-white mb-6">Education</h3>
                                <div className="space-y-2">
                                    {educationLinks.map((link) => (
                                        <Link
                                            key={link.name}
                                            href={link.href}
                                            className="text-xs text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                            suppressHydrationWarning
                                        >
                                            {link.name}
                                        </Link>
                                    ))}
                                </div>
                            </div>

                            {/* Affiliate Links */}
                            <div>
                                <h3 className="text-lg font-semibold text-white mb-6">Affiliate</h3>
                                <div className="space-y-2">
                                    {affiliateLinks.map((link) => (
                                        <Link
                                            key={link.name}
                                            href={link.href}
                                            className="text-xs text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                            suppressHydrationWarning
                                        >
                                            {link.name}
                                        </Link>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Mobile Footer Layout - Collapsible Sections */}
                        <div className="lg:hidden space-y-4" suppressHydrationWarning>
                            {/* Company Info - Always Visible on Mobile */}
                            <div className="mb-8" suppressHydrationWarning>
                                <Link href="/" className="flex items-center gap-2 mb-4">
                                    <Logo mode="dark" logoWidth={140} logoHeight={40} />
                                </Link>
                                <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                                    MyBrokerForex is a leading online forex broker providing professional trading services worldwide.
                                </p>

                                {/* Social Links */}
                                <div className="flex gap-3 mb-4" suppressHydrationWarning>
                                    {socialLinks.map((social) => {
                                        const IconComponent = social.icon
                                        return (
                                            <a
                                                key={social.name}
                                                href={social.href}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="w-8 h-8 bg-gray-800 hover:bg-[#EA5455] rounded-full flex items-center justify-center transition-colors duration-300"
                                                suppressHydrationWarning
                                            >
                                                <IconComponent className="h-4 w-4 text-white" />
                                            </a>
                                        )
                                    })}
                                </div>
                            </div>

                            {/* Trading Section */}
                            <div className="border-b border-gray-700 pb-4">
                                <button
                                    onClick={() => toggleSection('trading')}
                                    className="flex items-center justify-between w-full text-left py-3"
                                    suppressHydrationWarning
                                >
                                    <h3 className="text-lg font-semibold text-white">Trading</h3>
                                    {expandedSections.trading ? (
                                        <TbChevronUp className="h-5 w-5 text-gray-400" />
                                    ) : (
                                        <TbChevronDown className="h-5 w-5 text-gray-400" />
                                    )}
                                </button>
                                {expandedSections.trading && (
                                    <div className="space-y-2 mt-2 pl-4" suppressHydrationWarning>
                                        {tradingLinks.map((link) => (
                                            <Link
                                                key={link.name}
                                                href={link.href}
                                                className="text-sm text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                                suppressHydrationWarning
                                            >
                                                {link.name}
                                            </Link>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Tools Section */}
                            <div className="border-b border-gray-700 pb-4">
                                <button
                                    onClick={() => toggleSection('tools')}
                                    className="flex items-center justify-between w-full text-left py-3"
                                    suppressHydrationWarning
                                >
                                    <h3 className="text-lg font-semibold text-white">Tools</h3>
                                    {expandedSections.tools ? (
                                        <TbChevronUp className="h-5 w-5 text-gray-400" />
                                    ) : (
                                        <TbChevronDown className="h-5 w-5 text-gray-400" />
                                    )}
                                </button>
                                {expandedSections.tools && (
                                    <div className="space-y-2 mt-2 pl-4" suppressHydrationWarning>
                                        {toolsLinks.map((link) => (
                                            <Link
                                                key={link.name}
                                                href={link.href}
                                                className="text-sm text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                                suppressHydrationWarning
                                            >
                                                {link.name}
                                            </Link>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Education Section */}
                            <div className="border-b border-gray-700 pb-4">
                                <button
                                    onClick={() => toggleSection('education')}
                                    className="flex items-center justify-between w-full text-left py-3"
                                    suppressHydrationWarning
                                >
                                    <h3 className="text-lg font-semibold text-white">Education</h3>
                                    {expandedSections.education ? (
                                        <TbChevronUp className="h-5 w-5 text-gray-400" />
                                    ) : (
                                        <TbChevronDown className="h-5 w-5 text-gray-400" />
                                    )}
                                </button>
                                {expandedSections.education && (
                                    <div className="space-y-2 mt-2 pl-4" suppressHydrationWarning>
                                        {educationLinks.map((link) => (
                                            <Link
                                                key={link.name}
                                                href={link.href}
                                                className="text-sm text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                                suppressHydrationWarning
                                            >
                                                {link.name}
                                            </Link>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Affiliate Section */}
                            <div className="border-b border-gray-700 pb-4">
                                <button
                                    onClick={() => toggleSection('affiliate')}
                                    className="flex items-center justify-between w-full text-left py-3"
                                    suppressHydrationWarning
                                >
                                    <h3 className="text-lg font-semibold text-white">Affiliate</h3>
                                    {expandedSections.affiliate ? (
                                        <TbChevronUp className="h-5 w-5 text-gray-400" />
                                    ) : (
                                        <TbChevronDown className="h-5 w-5 text-gray-400" />
                                    )}
                                </button>
                                {expandedSections.affiliate && (
                                    <div className="space-y-2 mt-2 pl-4" suppressHydrationWarning>
                                        {affiliateLinks.map((link) => (
                                            <Link
                                                key={link.name}
                                                href={link.href}
                                                className="text-sm text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                                suppressHydrationWarning
                                            >
                                                {link.name}
                                            </Link>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Contact Info - Mobile */}
                            <div className="pt-4">
                                <h3 className="text-lg font-semibold text-white mb-4">Contact Us</h3>
                                <div className="space-y-3" suppressHydrationWarning>
                                    <div className="flex items-center gap-3">
                                        <TbMail className="h-4 w-4 text-[#EA5455]" />
                                        <span className="text-gray-300 text-sm"><EMAIL></span>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <TbPhone className="h-4 w-4 text-[#EA5455]" />
                                        <span className="text-gray-300 text-sm">+****************</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Container>
            </div>

            {/* Payment Methods Section */}
            <div className="bg-gray-800 border-t border-gray-700" suppressHydrationWarning>
                <Container>
                    <div className="py-8" suppressHydrationWarning>
                        <div className="text-center mb-6">
                            <h3 className="text-lg font-semibold text-white mb-4">Accepted Payment Methods</h3>
                            <p className="text-gray-300 text-sm">We accept a wide range of payment methods for your convenience</p>
                        </div>
                        <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-4" suppressHydrationWarning>
                            {Array.from({ length: 12 }, (_, i) => (
                                <div key={i + 1} className="bg-white rounded-lg p-1.5 sm:p-2 shadow-md hover:shadow-lg transition-shadow duration-300">
                                    <img
                                        src={`/img/payment/${String(i + 1).padStart(2, '0')}.png`}
                                        alt={`Payment method ${i + 1}`}
                                        className="h-4 sm:h-6 w-auto object-contain"
                                        loading="lazy"
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                </Container>
            </div>

            {/* Legal & Regulatory Section */}
            <div className="bg-gray-900 border-t border-gray-800">
                <Container>
                    <div className="py-12">
                        {/* Desktop Legal Section */}
                        <div className="hidden lg:grid lg:grid-cols-4 gap-8">
                            {/* Legal Links */}
                            <div>
                                <h3 className="text-lg font-semibold text-white mb-6">Legal & Compliance</h3>
                                <div className="space-y-3" suppressHydrationWarning>
                                    {legalLinks.map((link) => (
                                        <Link
                                            key={link.name}
                                            href={link.href}
                                            className="text-gray-300 hover:text-[#EA5455] transition-colors duration-300 text-sm block"
                                            suppressHydrationWarning
                                        >
                                            {link.name}
                                        </Link>
                                    ))}
                                </div>
                            </div>

                            {/* Company Links */}
                            <div>
                                <h3 className="text-lg font-semibold text-white mb-6">Company</h3>
                                <div className="space-y-2">
                                    {companyLinks.map((link) => (
                                        <Link
                                            key={link.name}
                                            href={link.href}
                                            className="text-xs text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                            suppressHydrationWarning
                                        >
                                            {link.name}
                                        </Link>
                                    ))}
                                </div>
                            </div>

                            {/* Regulatory Information */}
                            <div className="lg:col-span-2">
                                <h3 className="text-lg font-semibold text-white mb-6">Regulatory Information</h3>
                                <div className="space-y-4">
                                    <div className="flex items-start gap-3">
                                        <TbShield className="h-5 w-5 text-[#28C76F] mt-0.5 flex-shrink-0" />
                                        <div>
                                            <p className="text-sm text-gray-300">
                                                <strong>FCA Regulated:</strong> MyBrokerForex Ltd is authorized and regulated by the
                                                Financial Conduct Authority (FCA) under firm reference number 123456.
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <TbCertificate className="h-5 w-5 text-[#28C76F] mt-0.5 flex-shrink-0" />
                                        <div>
                                            <p className="text-sm text-gray-300">
                                                <strong>Client Protection:</strong> Client funds are held in segregated accounts
                                                with tier-1 banks and protected up to £85,000 by the FSCS.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Mobile Legal Section */}
                        <div className="lg:hidden space-y-4" suppressHydrationWarning>
                            {/* Company Section */}
                            <div className="border-b border-gray-700 pb-4">
                                <button
                                    onClick={() => toggleSection('company')}
                                    className="flex items-center justify-between w-full text-left py-3"
                                    suppressHydrationWarning
                                >
                                    <h3 className="text-lg font-semibold text-white">Company</h3>
                                    {expandedSections.company ? (
                                        <TbChevronUp className="h-5 w-5 text-gray-400" />
                                    ) : (
                                        <TbChevronDown className="h-5 w-5 text-gray-400" />
                                    )}
                                </button>
                                {expandedSections.company && (
                                    <div className="space-y-2 mt-2 pl-4" suppressHydrationWarning>
                                        {companyLinks.map((link) => (
                                            <Link
                                                key={link.name}
                                                href={link.href}
                                                className="text-sm text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                                suppressHydrationWarning
                                            >
                                                {link.name}
                                            </Link>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Legal Section */}
                            <div className="border-b border-gray-700 pb-4">
                                <button
                                    onClick={() => toggleSection('legal')}
                                    className="flex items-center justify-between w-full text-left py-3"
                                    suppressHydrationWarning
                                >
                                    <h3 className="text-lg font-semibold text-white">Legal & Compliance</h3>
                                    {expandedSections.legal ? (
                                        <TbChevronUp className="h-5 w-5 text-gray-400" />
                                    ) : (
                                        <TbChevronDown className="h-5 w-5 text-gray-400" />
                                    )}
                                </button>
                                {expandedSections.legal && (
                                    <div className="space-y-2 mt-2 pl-4" suppressHydrationWarning>
                                        {legalLinks.map((link) => (
                                            <Link
                                                key={link.name}
                                                href={link.href}
                                                className="text-sm text-gray-300 hover:text-[#EA5455] transition-colors py-1 block"
                                                suppressHydrationWarning
                                            >
                                                {link.name}
                                            </Link>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Regulatory Information - Mobile */}
                            <div className="pt-4">
                                <h3 className="text-lg font-semibold text-white mb-4">Regulatory Information</h3>
                                <div className="space-y-4">
                                    <div className="flex items-start gap-3">
                                        <TbShield className="h-4 w-4 text-[#28C76F] mt-0.5 flex-shrink-0" />
                                        <div>
                                            <p className="text-xs text-gray-300">
                                                <strong>FCA Regulated:</strong> MyBrokerForex Ltd is authorized and regulated by the
                                                Financial Conduct Authority (FCA) under firm reference number 123456.
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <TbCertificate className="h-4 w-4 text-[#28C76F] mt-0.5 flex-shrink-0" />
                                        <div>
                                            <p className="text-xs text-gray-300">
                                                <strong>Client Protection:</strong> Client funds are held in segregated accounts
                                                with tier-1 banks and protected up to £85,000 by the FSCS.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Container>
            </div>

            {/* Comprehensive Disclaimer */}
            <div className="bg-gray-900 border-t border-gray-800">
                <Container>
                    <div className="py-6">
                        <div className="text-left">
                            <p className="text-xs text-gray-500 leading-relaxed max-w-full">
                                <strong>Disclaimer:</strong> This website provides general information only and is not intended to provide personal financial advice. Before acting on any of the information on this website, it is important to consider the appropriateness of the information in relation to your objectives, financial situation, and needs. There are significant risks associated with investing in CFDs and margin FX contracts, and they are not suitable for all investors. You may lose more money than you deposited. You do not own any assets that are underlying the security. We recommend that you get independent advice and make sure you fully understand the risks before trading. Before you make any purchases, be sure to read the disclosure documents associated with the products you are considering. This website does not condone the use of its information or advertisements in any country or jurisdiction where it would be illegal. This website does not offer products or services that are intended for children. This website's products and services are not intended for citizens of the United States, Iran, and Turkey. Privacy Policy
                                <br /><br />
                                This website mbfx.co is operated under MBFX Global (UK) Ltd, which is registered in England and Wales.
                                <br />
                                Registered address: 71-75 SHELTON ST COVENT GARDEN LONDON WC2H 9JQ
                            </p>
                        </div>
                    </div>
                </Container>
            </div>

            {/* Copyright */}
            <div className="bg-black border-t border-gray-700">
                <Container>
                    <div className="py-6">
                        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                            <div className="flex items-center gap-4">
                                <p className="text-sm text-gray-400">
                                    © {year} MyBrokerForex Ltd. All rights reserved.
                                </p>
                                <div className="hidden md:flex items-center gap-4 text-xs text-gray-500">
                                    <span>Company Registration: 12345678</span>
                                    <span>FCA Reference: 123456</span>
                                </div>
                            </div>
                            <div className="flex items-center gap-6 text-xs text-gray-500" suppressHydrationWarning>
                                <Link href="#" className="hover:text-[#EA5455] transition-colors" suppressHydrationWarning>
                                    Terms
                                </Link>
                                <Link href="#" className="hover:text-[#EA5455] transition-colors" suppressHydrationWarning>
                                    Privacy
                                </Link>
                                <Link href="#" className="hover:text-[#EA5455] transition-colors" suppressHydrationWarning>
                                    Cookies
                                </Link>
                                <Link href="#" className="hover:text-[#EA5455] transition-colors" suppressHydrationWarning>
                                    Sitemap
                                </Link>
                            </div>
                        </div>
                    </div>
                </Container>
            </div>
        </footer>
    )
}

export default LandingFooter
