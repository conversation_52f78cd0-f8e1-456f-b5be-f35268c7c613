const mysql = require('mysql2/promise');

async function setupCMSTables() {
    let connection;
    
    try {
        // Create connection
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'mybrokerforex'
        });

        console.log('Connected to MySQL database');

        // Create posts table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS posts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) UNIQUE NOT NULL,
                content LONGTEXT,
                excerpt TEXT,
                category VARCHAR(100),
                image VARCHAR(255),
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                featured BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Create menus table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS menus (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
                location VARCHAR(100) NOT NULL,
                items JSON,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Create hero_sections table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS hero_sections (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                subtitle TEXT,
                description TEXT,
                background_image VARCHAR(255),
                cta_text VARCHAR(100),
                cta_link VARCHAR(255),
                status ENUM('active', 'inactive') DEFAULT 'active',
                order_index INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Create widgets table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS widgets (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                type VARCHAR(100) NOT NULL,
                content JSON,
                position VARCHAR(100),
                status ENUM('active', 'inactive') DEFAULT 'active',
                order_index INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Create ib_partners table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS ib_partners (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                company VARCHAR(255),
                phone VARCHAR(50),
                country VARCHAR(100),
                commission_rate DECIMAL(5,2) DEFAULT 0.00,
                status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Create affiliate_partners table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS affiliate_partners (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                website VARCHAR(255),
                traffic_source VARCHAR(255),
                monthly_visitors INT DEFAULT 0,
                commission_rate DECIMAL(5,2) DEFAULT 0.00,
                status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        console.log('All CMS tables created successfully');

        // Insert sample data
        console.log('Inserting sample data...');

        // Sample posts
        const samplePosts = [
            {
                title: 'Getting Started with Forex Trading',
                slug: 'getting-started-forex-trading',
                content: '<h1>Getting Started with Forex Trading</h1><p>Learn the basics of forex trading and how to get started in the foreign exchange market.</p>',
                excerpt: 'Learn the basics of forex trading and how to get started in the foreign exchange market.',
                category: 'Education',
                status: 'published',
                featured: true
            },
            {
                title: 'Understanding Market Analysis',
                slug: 'understanding-market-analysis',
                content: '<h1>Understanding Market Analysis</h1><p>Master the art of technical and fundamental analysis in forex trading.</p>',
                excerpt: 'Master the art of technical and fundamental analysis in forex trading.',
                category: 'Analysis',
                status: 'published',
                featured: false
            }
        ];

        for (const post of samplePosts) {
            await connection.execute(
                "INSERT IGNORE INTO posts (title, slug, content, excerpt, category, status, featured) VALUES (?, ?, ?, ?, ?, ?, ?)",
                [post.title, post.slug, post.content, post.excerpt, post.category, post.status, post.featured]
            );
        }

        // Sample hero sections
        const sampleHeroSections = [
            {
                title: 'Trade Forex with Confidence',
                subtitle: 'Professional Trading Platform',
                cta_text: 'Start Trading',
                cta_link: '/register',
                is_active: 1
            }
        ];

        for (const hero of sampleHeroSections) {
            await connection.execute(
                "INSERT IGNORE INTO hero_sections (title, subtitle, cta_text, cta_link, is_active) VALUES (?, ?, ?, ?, ?)",
                [hero.title, hero.subtitle, hero.cta_text, hero.cta_link, hero.is_active]
            );
        }

        // Sample menus
        const sampleMenus = [
            {
                name: 'Main Navigation',
                location: 'header',
                items: JSON.stringify([
                    { label: 'Home', url: '/', order: 1 },
                    { label: 'About', url: '/about', order: 2 },
                    { label: 'Trading', url: '/trading', order: 3 },
                    { label: 'Education', url: '/education', order: 4 },
                    { label: 'Contact', url: '/contact', order: 5 }
                ]),
                status: 'active'
            }
        ];

        for (const menu of sampleMenus) {
            await connection.execute(
                "INSERT IGNORE INTO menus (name, location, items, status) VALUES (?, ?, ?, ?)",
                [menu.name, menu.location, menu.items, menu.status]
            );
        }

        console.log('Sample data inserted successfully');
        console.log('CMS tables setup completed!');

    } catch (error) {
        console.error('Error setting up CMS tables:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

setupCMSTables();
