// ForexDataService.ts - Real-time forex data integration service
import { cache } from 'react'

export interface ForexRate {
    symbol: string
    bid: number
    ask: number
    price: number
    change: number
    changePercent: number
    timestamp: number
    category: 'forex' | 'crypto' | 'stocks'
}

export interface ForexDataProvider {
    name: string
    getRates(pairs: string[]): Promise<ForexRate[]>
    isAvailable(): boolean
    priority: number
}

// Alpha Vantage Provider (Free tier for development)
class AlphaVantageProvider implements ForexDataProvider {
    name = 'Alpha Vantage'
    priority = 1
    private apiKey: string
    private baseUrl = 'https://www.alphavantage.co/query'
    private lastCallTime = 0
    private minInterval = 12000 // 12 seconds between calls (5 calls per minute limit)

    constructor(apiKey: string) {
        this.apiKey = apiKey
    }

    isAvailable(): boolean {
        return !!this.apiKey && Date.now() - this.lastCallTime > this.minInterval
    }

    async getRates(pairs: string[]): Promise<ForexRate[]> {
        if (!this.isAvailable()) {
            throw new Error('Alpha Vantage provider not available')
        }

        const rates: ForexRate[] = []
        
        // Process pairs one by one due to API limitations
        for (const pair of pairs.slice(0, 3)) { // Limit to 3 pairs for free tier
            try {
                const [from, to] = pair.split('/')
                const url = `${this.baseUrl}?function=CURRENCY_EXCHANGE_RATE&from_currency=${from}&to_currency=${to}&apikey=${this.apiKey}`
                
                const response = await fetch(url)
                const data = await response.json()
                
                if (data['Realtime Currency Exchange Rate']) {
                    const exchangeRate = data['Realtime Currency Exchange Rate']
                    const price = parseFloat(exchangeRate['5. Exchange Rate'])
                    const previousPrice = price * (1 + (Math.random() - 0.5) * 0.01) // Simulate previous price
                    const change = price - previousPrice
                    const changePercent = (change / previousPrice) * 100

                    rates.push({
                        symbol: pair,
                        bid: price - 0.0001, // Simulate spread
                        ask: price + 0.0001,
                        price,
                        change,
                        changePercent,
                        timestamp: Date.now(),
                        category: 'forex'
                    })
                }
                
                // Respect rate limits
                await new Promise(resolve => setTimeout(resolve, 1000))
            } catch (error) {
                console.error(`Error fetching ${pair} from Alpha Vantage:`, error)
            }
        }

        this.lastCallTime = Date.now()
        return rates
    }
}

// Simulated Data Provider (Fallback)
class SimulatedDataProvider implements ForexDataProvider {
    name = 'Simulated Data'
    priority = 999
    
    isAvailable(): boolean {
        return true
    }

    async getRates(pairs: string[]): Promise<ForexRate[]> {
        const baseRates: Record<string, number> = {
            'EUR/USD': 1.0845,
            'GBP/USD': 1.2634,
            'USD/JPY': 149.85,
            'USD/CHF': 0.8756,
            'AUD/USD': 0.6523,
            'USD/CAD': 1.3689,
            'NZD/USD': 0.5892,
            'EUR/GBP': 0.8567
        }

        return pairs.map(pair => {
            const basePrice = baseRates[pair] || 1.0000
            const volatility = 0.005 // 0.5% volatility
            const randomChange = (Math.random() - 0.5) * 2 * volatility
            const price = basePrice * (1 + randomChange)
            const previousPrice = basePrice
            const change = price - previousPrice
            const changePercent = (change / previousPrice) * 100
            const spread = pair.includes('JPY') ? 0.01 : 0.0001

            return {
                symbol: pair,
                bid: price - spread,
                ask: price + spread,
                price,
                change,
                changePercent,
                timestamp: Date.now(),
                category: 'forex' as const
            }
        })
    }
}

// Main Forex Data Service
class ForexDataService {
    private providers: ForexDataProvider[] = []
    private cache = new Map<string, { data: ForexRate[], timestamp: number }>()
    private cacheTimeout = 30000 // 30 seconds cache

    constructor() {
        this.initializeProviders()
    }

    private initializeProviders() {
        // Add Alpha Vantage if API key is available
        const alphaVantageKey = process.env.NEXT_PUBLIC_ALPHA_VANTAGE_API_KEY
        if (alphaVantageKey) {
            this.providers.push(new AlphaVantageProvider(alphaVantageKey))
        }

        // Always add simulated provider as fallback
        this.providers.push(new SimulatedDataProvider())

        // Sort by priority
        this.providers.sort((a, b) => a.priority - b.priority)
    }

    async getRealTimeRates(pairs: string[]): Promise<ForexRate[]> {
        const cacheKey = pairs.sort().join(',')
        const cached = this.cache.get(cacheKey)
        
        // Return cached data if still valid
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data
        }

        // Try providers in order of priority
        for (const provider of this.providers) {
            if (provider.isAvailable()) {
                try {
                    console.log(`Fetching forex data from ${provider.name}`)
                    const rates = await provider.getRates(pairs)
                    
                    if (rates.length > 0) {
                        // Cache the results
                        this.cache.set(cacheKey, {
                            data: rates,
                            timestamp: Date.now()
                        })
                        
                        return rates
                    }
                } catch (error) {
                    console.error(`Provider ${provider.name} failed:`, error)
                    continue
                }
            }
        }

        // If all providers fail, return empty array
        console.error('All forex data providers failed')
        return []
    }

    // Get available currency pairs
    getAvailablePairs(): string[] {
        return [
            'EUR/USD',
            'GBP/USD', 
            'USD/JPY',
            'USD/CHF',
            'AUD/USD',
            'USD/CAD',
            'NZD/USD',
            'EUR/GBP'
        ]
    }

    // Clear cache
    clearCache() {
        this.cache.clear()
    }

    // Get provider status
    getProviderStatus() {
        return this.providers.map(provider => ({
            name: provider.name,
            available: provider.isAvailable(),
            priority: provider.priority
        }))
    }
}

// Export singleton instance
export const forexDataService = new ForexDataService()

// Cached function for React Server Components
export const getCachedForexRates = cache(async (pairs: string[]) => {
    return forexDataService.getRealTimeRates(pairs)
})
