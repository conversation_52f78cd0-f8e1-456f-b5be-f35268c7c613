import { NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket } from 'mysql2'

// GET /api/test-db - Test database connection
export async function GET() {
    try {
        // Test basic connection
        const [rows] = await pool.execute<RowDataPacket[]>('SELECT 1 as test')
        
        // Test if our tables exist
        const [tables] = await pool.execute<RowDataPacket[]>(
            "SHOW TABLES"
        )
        
        // Get some sample data (with error handling for missing tables)
        let menuCount = 0, pageCount = 0, postCount = 0

        try {
            const [menuResult] = await pool.execute<RowDataPacket[]>('SELECT COUNT(*) as count FROM menus')
            menuCount = menuResult[0]?.count || 0
        } catch (e) { /* Table might not exist */ }

        try {
            const [pageResult] = await pool.execute<RowDataPacket[]>('SELECT COUNT(*) as count FROM pages')
            pageCount = pageResult[0]?.count || 0
        } catch (e) { /* Table might not exist */ }

        try {
            const [postResult] = await pool.execute<RowDataPacket[]>('SELECT COUNT(*) as count FROM posts')
            postCount = postResult[0]?.count || 0
        } catch (e) { /* Table might not exist */ }
        
        return NextResponse.json({
            success: true,
            message: 'Database connection successful',
            data: {
                connection: rows[0],
                tables: tables.length,
                counts: {
                    menus: menuCount,
                    pages: pageCount,
                    posts: postCount
                }
            }
        })
    } catch (error) {
        console.error('Database connection error:', error)
        return NextResponse.json(
            { 
                success: false, 
                error: 'Database connection failed',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        )
    }
}
