'use client'

import { useEffect, useRef } from 'react'

const DynamicFavicon = () => {
    const isUpdatingRef = useRef(false)
    const addedLinksRef = useRef<HTMLLinkElement[]>([])

    useEffect(() => {
        const updateFavicon = async () => {
            // Prevent multiple simultaneous updates
            if (isUpdatingRef.current) return
            isUpdatingRef.current = true

            try {
                // Add cache-busting parameter to ensure latest favicon
                const response = await fetch(`/api/settings/favicon?t=${Date.now()}`)
                const data = await response.json()

                if (data.success && data.faviconPath) {
                    // Clean up previously added links
                    addedLinksRef.current.forEach(link => {
                        try {
                            if (link.parentNode && document.head.contains(link)) {
                                document.head.removeChild(link)
                            }
                        } catch (error) {
                            // Silently handle removal errors
                        }
                    })
                    addedLinksRef.current = []

                    // Add new favicon link with cache-busting
                    const faviconUrl = `${data.faviconPath}?t=${Date.now()}`
                    const link = document.createElement('link')
                    link.type = 'image/x-icon'
                    link.rel = 'shortcut icon'
                    link.href = faviconUrl

                    // Add apple-touch-icon for mobile devices
                    const appleLink = document.createElement('link')
                    appleLink.rel = 'apple-touch-icon'
                    appleLink.href = faviconUrl

                    // Safely append to head
                    try {
                        document.head.appendChild(link)
                        document.head.appendChild(appleLink)
                        addedLinksRef.current = [link, appleLink]
                    } catch (error) {
                        console.error('Error appending favicon links:', error)
                    }
                }
            } catch (error) {
                console.error('Error updating favicon:', error)
            } finally {
                isUpdatingRef.current = false
            }
        }

        updateFavicon()

        // Cleanup function to remove added links when component unmounts
        return () => {
            addedLinksRef.current.forEach(link => {
                try {
                    if (link.parentNode && document.head.contains(link)) {
                        document.head.removeChild(link)
                    }
                } catch (error) {
                    // Silently handle cleanup errors
                }
            })
            addedLinksRef.current = []
        }
    }, [])

    return null
}

export default DynamicFavicon
