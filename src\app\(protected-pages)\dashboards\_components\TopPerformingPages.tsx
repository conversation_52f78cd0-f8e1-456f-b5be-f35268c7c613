'use client'

import { Card } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import { ExternalLinkIcon, TrendingUpIcon, TrendingDownIcon } from 'lucide-react'

interface TopPerformingPagesProps {
    data: {
        topPerformingPages: Array<{
            page: string
            views: number
            uniqueViews: number
            bounceRate: number
        }>
    }
}

const TopPerformingPages = ({ data }: TopPerformingPagesProps) => {
    const getBounceRateColor = (rate: number) => {
        if (rate < 30) return 'text-green-600'
        if (rate < 50) return 'text-yellow-600'
        return 'text-red-600'
    }

    const getBounceRateBadge = (rate: number) => {
        if (rate < 30) return 'bg-green-500 text-white'
        if (rate < 50) return 'bg-yellow-500 text-white'
        return 'bg-red-500 text-white'
    }

    return (
        <Card 
            header={{ content: 'Top Performing Pages' }}
            className="p-6"
        >
            <div className="space-y-4">
                {data?.topPerformingPages?.length > 0 ? data.topPerformingPages.map((page, index) => (
                    <div key={page.page} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <div className="flex items-center gap-4 flex-1">
                            <div className="flex items-center justify-center w-8 h-8 bg-primary/10 text-primary rounded-lg font-semibold text-sm">
                                {index + 1}
                            </div>
                            
                            <div className="flex-1">
                                <div className="flex items-center gap-2">
                                    <h4 className="font-medium">{page.page}</h4>
                                    <ExternalLinkIcon className="h-3 w-3 text-muted-foreground" />
                                </div>
                                <div className="flex items-center gap-4 mt-1">
                                    <span className="text-sm text-muted-foreground">
                                        {page.views.toLocaleString()} views
                                    </span>
                                    <span className="text-sm text-muted-foreground">
                                        {page.uniqueViews.toLocaleString()} unique
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div className="flex items-center gap-4">
                            <div className="text-right">
                                <div className="flex items-center gap-1">
                                    {page.bounceRate < 50 ? (
                                        <TrendingUpIcon className="h-4 w-4 text-green-600" />
                                    ) : (
                                        <TrendingDownIcon className="h-4 w-4 text-red-600" />
                                    )}
                                    <span className={`font-semibold ${getBounceRateColor(page.bounceRate)}`}>
                                        {page.bounceRate}%
                                    </span>
                                </div>
                                <p className="text-sm text-muted-foreground">bounce rate</p>
                            </div>
                            
                            <Badge innerClass={getBounceRateBadge(page.bounceRate)} content={
                                page.bounceRate < 30 ? 'Excellent' :
                                page.bounceRate < 50 ? 'Good' : 'Needs Improvement'
                            } />
                        </div>
                    </div>
                )) : (
                    <div className="text-center py-8">
                        <p className="text-muted-foreground">No page data available</p>
                    </div>
                )}
            </div>
            
            {data?.topPerformingPages?.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <p className="text-xl font-bold text-blue-600">
                                {data.topPerformingPages.reduce((sum, page) => sum + page.views, 0).toLocaleString()}
                            </p>
                            <p className="text-sm text-muted-foreground">Total Page Views</p>
                        </div>
                        <div>
                            <p className="text-xl font-bold text-green-600">
                                {data.topPerformingPages.reduce((sum, page) => sum + page.uniqueViews, 0).toLocaleString()}
                            </p>
                            <p className="text-sm text-muted-foreground">Unique Views</p>
                        </div>
                        <div>
                            <p className="text-xl font-bold text-orange-600">
                                {(data.topPerformingPages.reduce((sum, page) => sum + page.bounceRate, 0) / data.topPerformingPages.length).toFixed(1)}%
                            </p>
                            <p className="text-sm text-muted-foreground">Avg Bounce Rate</p>
                        </div>
                    </div>
                </div>
            )}
        </Card>
    )
}

export default TopPerformingPages
