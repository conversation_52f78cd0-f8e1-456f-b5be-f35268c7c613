'use client'

import Navigation from '@/app/landing/components/NavigationBar'
import LandingFooter from '@/app/landing/components/LandingFooter'
import useTheme from '@/utils/hooks/useTheme'
import { MODE_DARK, MODE_LIGHT } from '@/constants/theme.constant'
import type { ReactNode } from 'react'

interface PublicPageLayoutProps {
    children: ReactNode
}

const PublicPageLayout = ({ children }: PublicPageLayoutProps) => {
    const mode = useTheme((state) => state.mode)
    const setMode = useTheme((state) => state.setMode)

    const toggleMode = () => {
        setMode(mode === MODE_LIGHT ? MODE_DARK : MODE_LIGHT)
    }

    return (
        <div className="min-h-screen bg-white dark:bg-gray-900">
            <Navigation toggleMode={toggleMode} mode={mode} />
            <main>
                {children}
            </main>
            <LandingFooter mode={mode} />
        </div>
    )
}

export default PublicPageLayout
