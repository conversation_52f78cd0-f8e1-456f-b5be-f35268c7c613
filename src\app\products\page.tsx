'use client'

import { motion } from 'framer-motion'
import { TbChartLine, TbCurrencyDollar, TbTrendingUp, TbDeviceDesktop, TbDeviceMobile, TbApi } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import PageLayout from '@/components/layout/PageLayout'

const ProductsPage = () => {
  const router = useRouter()

  const products = [
    {
      id: 1,
      name: "Forex Trading",
      description: "Trade 50+ major, minor, and exotic currency pairs with competitive spreads",
      icon: TbCurrencyDollar,
      features: [
        "50+ Currency Pairs",
        "Spreads from 0.1 pips",
        "Leverage up to 1:500",
        "24/7 Market Access",
        "Real-time Quotes",
        "Advanced Charting"
      ],
      color: "text-[#EA5455]",
      bgColor: "bg-[#EA5455]/10"
    },
    {
      id: 2,
      name: "Trading Platform",
      description: "Professional trading platform with advanced tools and analytics",
      icon: TbChartLine,
      features: [
        "Advanced Charting",
        "Technical Indicators",
        "One-Click Trading",
        "Risk Management",
        "Market Analysis",
        "Trading Signals"
      ],
      color: "text-[#28C76F]",
      bgColor: "bg-[#28C76F]/10"
    },
    {
      id: 3,
      name: "Mobile Trading",
      description: "Trade on the go with our powerful mobile trading applications",
      icon: TbDeviceMobile,
      features: [
        "iOS & Android Apps",
        "Real-time Trading",
        "Push Notifications",
        "Touch ID Security",
        "Offline Charts",
        "News & Analysis"
      ],
      color: "text-blue-500",
      bgColor: "bg-blue-500/10"
    },
    {
      id: 4,
      name: "Web Platform",
      description: "Browser-based trading platform accessible from anywhere",
      icon: TbDeviceDesktop,
      features: [
        "No Download Required",
        "Cross-Platform",
        "Cloud Sync",
        "Multi-Monitor Support",
        "Customizable Layout",
        "Real-time Data"
      ],
      color: "text-purple-500",
      bgColor: "bg-purple-500/10"
    },
    {
      id: 5,
      name: "Market Analysis",
      description: "Professional market research and trading insights",
      icon: TbTrendingUp,
      features: [
        "Daily Market Reports",
        "Technical Analysis",
        "Economic Calendar",
        "Trading Signals",
        "Expert Commentary",
        "Video Analysis"
      ],
      color: "text-orange-500",
      bgColor: "bg-orange-500/10"
    },
    {
      id: 6,
      name: "Trading APIs",
      description: "Integrate our trading services into your applications",
      icon: TbApi,
      features: [
        "REST API",
        "WebSocket Feeds",
        "Historical Data",
        "Real-time Quotes",
        "Order Management",
        "Account Information"
      ],
      color: "text-teal-500",
      bgColor: "bg-teal-500/10"
    }
  ]

  const handleLearnMore = (productName: string) => {
    router.push(`/products/${productName.toLowerCase().replace(/\s+/g, '-')}`)
  }

  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Our <span className="text-[#EA5455]">Products</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Comprehensive trading solutions designed to meet the needs of traders at every level, 
              from beginners to institutional clients.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.map((product, index) => {
              const IconComponent = product.icon
              return (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 group"
                >
                  <div className={`w-16 h-16 ${product.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className={`h-8 w-8 ${product.color}`} />
                  </div>
                  
                  <h3 className="text-2xl font-bold mb-3">{product.name}</h3>
                  <p className="text-muted-foreground mb-6">{product.description}</p>
                  
                  <div className="space-y-2 mb-6">
                    {product.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${product.color.replace('text-', 'bg-')}`}></div>
                        <span className="text-sm text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <button
                    onClick={() => handleLearnMore(product.name)}
                    className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 border-2 ${product.color.replace('text-', 'border-')} ${product.color} hover:${product.color.replace('text-', 'bg-')} hover:text-white`}
                  >
                    Learn More
                  </button>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Start Trading?
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Choose from our range of account types and start trading with professional tools and competitive spreads.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                Open Live Account
              </button>
              <button
                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')}
                className="border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                Try Demo Account
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    </PageLayout>
  )
}

export default ProductsPage
