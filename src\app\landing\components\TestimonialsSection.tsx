'use client'

import { motion } from 'framer-motion'
import Container from './LandingContainer'
import { TbStar, TbQuote } from 'react-icons/tb'
import { useTestimonials } from '@/utils/hooks/useTestimonials'
import type { Mode } from '@/@types/theme'

type TestimonialsSectionProps = {
    mode: Mode
}



const TestimonialsSection = ({ mode }: TestimonialsSectionProps) => {
    const { testimonials, loading, error } = useTestimonials(true, false) // Get active testimonials

    // Fallback testimonials if database is not available
    const fallbackTestimonials = [
        {
            id: 1,
            client_name: "<PERSON>",
            position: "Professional Trader",
            company: "Independent",
            rating: 5,
            quote: "MyBrokerForex has transformed my trading experience. The spreads are incredibly competitive, and the execution speed is lightning fast. I've been trading with them for over 2 years and couldn't be happier.",
            image: "/img/avatars/avatar-1.jpg"
        },
        {
            id: 2,
            client_name: "<PERSON>",
            position: "Portfolio Manager",
            company: "Chen Investments",
            rating: 5,
            quote: "The platform's advanced charting tools and technical indicators are exactly what I need for my analysis. Customer support is exceptional - they're always available when I need assistance.",
            image: "/img/avatars/avatar-2.jpg"
        },
        {
            id: 3,
            client_name: "Emma Rodriguez",
            position: "Day Trader",
            company: "Freelance",
            rating: 5,
            quote: "Security and regulation were my top priorities when choosing a broker. MyBrokerForex exceeds expectations with segregated funds and transparent operations. Highly recommended!",
            image: "/img/avatars/avatar-3.jpg"
        },
        {
            id: 4,
            client_name: "David Thompson",
            position: "Forex Analyst",
            company: "Thompson Trading",
            rating: 5,
            quote: "The educational resources and market analysis provided by MyBrokerForex have significantly improved my trading strategies. It's more than just a broker - it's a complete trading ecosystem.",
            image: "/img/avatars/avatar-4.jpg"
        }
    ]

    // Use database testimonials if available, otherwise use fallback
    const displayTestimonials = testimonials.length > 0 ? testimonials : fallbackTestimonials

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <TbStar
                key={i}
                className={`h-4 w-4 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
            />
        ))
    }

    return (
        <Container>
            <div className="max-w-7xl mx-auto px-4 py-20">
                <div className="text-center mb-16">
                    <motion.h2 
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="text-3xl md:text-5xl font-bold mb-4"
                    >
                        What Our <span className="text-[#EA5455]">Traders</span> Say
                    </motion.h2>
                    <motion.p 
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="text-xl text-muted-foreground max-w-3xl mx-auto"
                    >
                        Join thousands of successful traders who trust MyBrokerForex for their trading journey.
                    </motion.p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {displayTestimonials.slice(0, 4).map((testimonial, index) => (
                        <motion.div
                            key={testimonial.id}
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-300 relative"
                        >
                            <div className="absolute top-4 right-4">
                                <TbQuote className="h-8 w-8 text-[#EA5455]/20" />
                            </div>
                            
                            <div className="flex items-center gap-4 mb-4">
                                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                    <span className="text-lg font-semibold text-[#EA5455]">
                                        {testimonial.client_name.split(' ').map(n => n[0]).join('')}
                                    </span>
                                </div>
                                <div>
                                    <h4 className="font-semibold text-lg">{testimonial.client_name}</h4>
                                    <p className="text-sm text-muted-foreground">{testimonial.position}</p>
                                    <p className="text-xs text-muted-foreground">{testimonial.company}</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-1 mb-4">
                                {renderStars(testimonial.rating)}
                            </div>

                            <p className="text-muted-foreground italic leading-relaxed">
                                "{testimonial.quote}"
                            </p>
                        </motion.div>
                    ))}
                </div>

                {/* Trust Indicators */}
                <motion.div 
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mt-16 text-center"
                >
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                        <div>
                            <div className="text-3xl font-bold text-[#EA5455] mb-2">4.9/5</div>
                            <div className="text-sm text-muted-foreground">Average Rating</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold text-[#EA5455] mb-2">10K+</div>
                            <div className="text-sm text-muted-foreground">Happy Traders</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold text-[#EA5455] mb-2">99.9%</div>
                            <div className="text-sm text-muted-foreground">Uptime</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold text-[#EA5455] mb-2">24/7</div>
                            <div className="text-sm text-muted-foreground">Support</div>
                        </div>
                    </div>
                </motion.div>
            </div>
        </Container>
    )
}

export default TestimonialsSection
