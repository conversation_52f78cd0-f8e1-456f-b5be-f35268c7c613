'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { TbCalculator, TbShield, TbTarget, TbTrendingUp } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function PositionSizePage() {
    const [accountBalance, setAccountBalance] = useState('10000')
    const [riskPercentage, setRiskPercentage] = useState('2')
    const [entryPrice, setEntryPrice] = useState('1.1000')
    const [stopLoss, setStopLoss] = useState('1.0950')
    const [currencyPair, setCurrencyPair] = useState('EUR/USD')
    const [positionSize, setPositionSize] = useState(0)
    const [riskAmount, setRiskAmount] = useState(0)

    const currencyPairs = [
        'EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 'AUD/USD', 'USD/CAD',
        'NZD/USD', 'EUR/GBP', 'EUR/JPY', 'GBP/JPY', 'CHF/JPY', 'AUD/JPY'
    ]

    const calculatePositionSize = () => {
        const balance = parseFloat(accountBalance)
        const risk = parseFloat(riskPercentage)
        const entry = parseFloat(entryPrice)
        const stop = parseFloat(stopLoss)

        if (!balance || !risk || !entry || !stop || balance <= 0 || risk <= 0 || entry <= 0 || stop <= 0) {
            setPositionSize(0)
            setRiskAmount(0)
            return
        }

        const riskAmountCalc = (balance * risk) / 100
        const pipDifference = Math.abs(entry - stop)
        
        let pipValue = 0
        if (currencyPair.includes('JPY')) {
            pipValue = pipDifference * 100 // For JPY pairs
        } else {
            pipValue = pipDifference * 10000 // For other pairs
        }

        // Calculate position size in lots
        const positionSizeCalc = riskAmountCalc / (pipValue * 10) // Simplified calculation
        
        setRiskAmount(riskAmountCalc)
        setPositionSize(positionSizeCalc)
    }

    const features = [
        {
            icon: TbShield,
            title: 'Risk Management',
            description: 'Calculate optimal position sizes based on your risk tolerance and account balance.'
        },
        {
            icon: TbTarget,
            title: 'Precise Calculations',
            description: 'Accurate position sizing using entry price, stop loss, and risk percentage.'
        },
        {
            icon: TbTrendingUp,
            title: 'Account Protection',
            description: 'Protect your trading capital by never risking more than you can afford to lose.'
        },
        {
            icon: TbCalculator,
            title: 'Easy to Use',
            description: 'Simple interface for quick position size calculations before entering trades.'
        }
    ]

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-5xl text-white mt-8 font-bold mb-6">
                                Position Size Calculator
                            </h1>
                            <p className="text-xl text-gray-100 mb-8 max-w-3xl mx-auto">
                                Calculate the optimal position size for your trades based on your account balance,
                                risk tolerance, and stop loss levels. Essential for proper risk management.
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* Calculator Section */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                            {/* Calculator */}
                            <motion.div
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8 }}
                                className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg"
                            >
                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                                    Calculate Position Size
                                </h2>

                                <div className="space-y-6">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Account Balance (USD)
                                        </label>
                                        <input
                                            type="number"
                                            value={accountBalance}
                                            onChange={(e) => setAccountBalance(e.target.value)}
                                            placeholder="10000"
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Risk Percentage (%)
                                        </label>
                                        <input
                                            type="number"
                                            value={riskPercentage}
                                            onChange={(e) => setRiskPercentage(e.target.value)}
                                            placeholder="2"
                                            step="0.1"
                                            max="10"
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        />
                                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Recommended: 1-3% per trade
                                        </p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Currency Pair
                                        </label>
                                        <select
                                            value={currencyPair}
                                            onChange={(e) => setCurrencyPair(e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        >
                                            {currencyPairs.map(pair => (
                                                <option key={pair} value={pair}>{pair}</option>
                                            ))}
                                        </select>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Entry Price
                                            </label>
                                            <input
                                                type="number"
                                                value={entryPrice}
                                                onChange={(e) => setEntryPrice(e.target.value)}
                                                placeholder="1.1000"
                                                step="0.0001"
                                                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Stop Loss
                                            </label>
                                            <input
                                                type="number"
                                                value={stopLoss}
                                                onChange={(e) => setStopLoss(e.target.value)}
                                                placeholder="1.0950"
                                                step="0.0001"
                                                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                            />
                                        </div>
                                    </div>

                                    <button
                                        onClick={calculatePositionSize}
                                        className="w-full py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                    >
                                        Calculate Position Size
                                    </button>

                                    {positionSize > 0 && (
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            className="bg-[#28C76F] bg-opacity-10 border border-[#28C76F] rounded-xl p-6"
                                        >
                                            <h3 className="text-lg font-semibold text-[#28C76F] mb-4">
                                                Recommended Position Size
                                            </h3>
                                            <div className="space-y-3">
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600 dark:text-gray-300">Position Size:</span>
                                                    <span className="font-bold text-gray-900 dark:text-white">
                                                        {positionSize.toFixed(2)} lots
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600 dark:text-gray-300">Risk Amount:</span>
                                                    <span className="font-bold text-gray-900 dark:text-white">
                                                        ${riskAmount.toFixed(2)}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600 dark:text-gray-300">Risk Distance:</span>
                                                    <span className="font-bold text-gray-900 dark:text-white">
                                                        {Math.abs(parseFloat(entryPrice) - parseFloat(stopLoss)).toFixed(4)} pips
                                                    </span>
                                                </div>
                                            </div>
                                        </motion.div>
                                    )}
                                </div>
                            </motion.div>

                            {/* Information */}
                            <motion.div
                                initial={{ opacity: 0, x: 30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="space-y-8"
                            >
                                <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg">
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                        Risk Management Rules
                                    </h3>
                                    <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                            Never risk more than 1-3% of your account per trade
                                        </li>
                                        <li className="flex items-start">
                                            <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                            Always use stop losses to limit potential losses
                                        </li>
                                        <li className="flex items-start">
                                            <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                            Calculate position size before entering any trade
                                        </li>
                                        <li className="flex items-start">
                                            <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                            Adjust position size based on market volatility
                                        </li>
                                    </ul>
                                </div>

                                <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg">
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                        Position Sizing Formula
                                    </h3>
                                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                                            Position Size = Risk Amount ÷ (Entry Price - Stop Loss)
                                        </p>
                                        <p className="text-sm text-gray-600 dark:text-gray-300">
                                            Risk Amount = Account Balance × Risk Percentage
                                        </p>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Why Position Sizing Matters
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <feature.icon className="w-8 h-8 text-white" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>
            </div>
                    </PublicPageLayout>
    )
}
