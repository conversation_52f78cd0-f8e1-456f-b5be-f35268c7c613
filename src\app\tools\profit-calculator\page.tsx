'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { TbCal<PERSON><PERSON>, TbTrendingUp, TbCash, TbChartLine } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function ProfitCalculatorPage() {
    const [accountCurrency, setAccountCurrency] = useState('USD')
    const [currencyPair, setCurrencyPair] = useState('EUR/USD')
    const [tradeType, setTradeType] = useState('buy')
    const [lotSize, setLotSize] = useState('1')
    const [openPrice, setOpenPrice] = useState('1.1000')
    const [closePrice, setClosePrice] = useState('1.1050')
    const [profit, setProfit] = useState(0)

    const currencyPairs = [
        'EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 'AUD/USD', 'USD/CAD',
        'NZD/USD', 'EUR/GBP', 'EUR/JPY', 'GBP/JPY', 'CHF/JPY', 'AUD/JPY'
    ]

    const accountCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'NZD']

    const calculateProfit = () => {
        const lots = parseFloat(lotSize)
        const open = parseFloat(openPrice)
        const close = parseFloat(closePrice)
        
        if (!lots || !open || !close || lots <= 0 || open <= 0 || close <= 0) {
            setProfit(0)
            return
        }

        let pipDifference = 0
        let pipValue = 0

        // Calculate pip difference based on trade type
        if (tradeType === 'buy') {
            pipDifference = close - open
        } else {
            pipDifference = open - close
        }

        // Calculate pip value based on currency pair
        if (currencyPair.includes('JPY')) {
            pipDifference = pipDifference * 100 // For JPY pairs
            pipValue = lots * 100000 * 0.01 / close // Standard lot size
        } else {
            pipDifference = pipDifference * 10000 // For other pairs
            pipValue = lots * 100000 * 0.0001 // Standard lot size
        }

        const profitLoss = pipDifference * pipValue
        setProfit(profitLoss)
    }

    const features = [
        {
            icon: TbCalculator,
            title: 'Accurate Calculations',
            description: 'Calculate exact profit and loss for any trade with precise pip values and lot sizes.'
        },
        {
            icon: TbTrendingUp,
            title: 'Multiple Trade Types',
            description: 'Support for both buy and sell positions across all major currency pairs.'
        },
        {
            icon: TbCash,
            title: 'Real-Time Results',
            description: 'Instant profit/loss calculations in your account currency for better planning.'
        },
        {
            icon: TbChartLine,
            title: 'Risk Assessment',
            description: 'Understand potential outcomes before entering trades for better risk management.'
        }
    ]

    return (
        <div className="mt-0">
            <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-5xl text-gray-300 mt-8 font-bold mb-6">
                                Profit Calculator
                            </h1>
                            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Calculate potential profit and loss for your forex trades before you enter the market. 
                                Essential tool for trade planning and risk management.
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* Calculator Section */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                            {/* Calculator */}
                            <motion.div
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8 }}
                                className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg"
                            >
                                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                                    Calculate Profit/Loss
                                </h2>

                                <div className="space-y-6">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Account Currency
                                            </label>
                                            <select
                                                value={accountCurrency}
                                                onChange={(e) => setAccountCurrency(e.target.value)}
                                                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                            >
                                                {accountCurrencies.map(currency => (
                                                    <option key={currency} value={currency}>{currency}</option>
                                                ))}
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Currency Pair
                                            </label>
                                            <select
                                                value={currencyPair}
                                                onChange={(e) => setCurrencyPair(e.target.value)}
                                                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                            >
                                                {currencyPairs.map(pair => (
                                                    <option key={pair} value={pair}>{pair}</option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Trade Type
                                            </label>
                                            <select
                                                value={tradeType}
                                                onChange={(e) => setTradeType(e.target.value)}
                                                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                            >
                                                <option value="buy">Buy (Long)</option>
                                                <option value="sell">Sell (Short)</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Lot Size
                                            </label>
                                            <input
                                                type="number"
                                                value={lotSize}
                                                onChange={(e) => setLotSize(e.target.value)}
                                                placeholder="1.0"
                                                step="0.01"
                                                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Open Price
                                            </label>
                                            <input
                                                type="number"
                                                value={openPrice}
                                                onChange={(e) => setOpenPrice(e.target.value)}
                                                placeholder="1.1000"
                                                step="0.0001"
                                                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Close Price
                                            </label>
                                            <input
                                                type="number"
                                                value={closePrice}
                                                onChange={(e) => setClosePrice(e.target.value)}
                                                placeholder="1.1050"
                                                step="0.0001"
                                                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                            />
                                        </div>
                                    </div>

                                    <button
                                        onClick={calculateProfit}
                                        className="w-full py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                    >
                                        Calculate Profit/Loss
                                    </button>

                                    {profit !== 0 && (
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            className={`border rounded-xl p-6 ${
                                                profit >= 0
                                                    ? 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                                                    : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                                            }`}
                                        >
                                            <h3 className={`text-lg font-semibold mb-2 ${
                                                profit >= 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'
                                            }`}>
                                                {profit >= 0 ? 'Profit' : 'Loss'}
                                            </h3>
                                            <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                                {profit >= 0 ? '+' : ''}{profit.toFixed(2)} {accountCurrency}
                                            </p>
                                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                                                {tradeType === 'buy' ? 'Long' : 'Short'} position on {currencyPair} with {lotSize} lots
                                            </p>
                                        </motion.div>
                                    )}
                                </div>
                            </motion.div>

                            {/* Information */}
                            <motion.div
                                initial={{ opacity: 0, x: 30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="space-y-8"
                            >
                                <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg">
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                        How to Use
                                    </h3>
                                    <ol className="list-decimal list-inside space-y-3 text-gray-600 dark:text-gray-300">
                                        <li>Select your account currency</li>
                                        <li>Choose the currency pair you want to trade</li>
                                        <li>Select trade type (Buy/Sell)</li>
                                        <li>Enter lot size and open/close prices</li>
                                        <li>Click calculate to see profit/loss</li>
                                    </ol>
                                </div>

                                <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg">
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                        Trading Tips
                                    </h3>
                                    <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                            Always calculate potential profit/loss before entering trades
                                        </li>
                                        <li className="flex items-start">
                                            <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                            Use proper risk management with stop losses
                                        </li>
                                        <li className="flex items-start">
                                            <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                            Consider the risk-reward ratio for each trade
                                        </li>
                                        <li className="flex items-start">
                                            <span className="w-2 h-2 bg-[#EA5455] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                            Factor in spread costs when calculating profits
                                        </li>
                                    </ul>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Why Use Our Profit Calculator?
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <feature.icon className="w-8 h-8 text-white" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>
            </div>
                    </PublicPageLayout>
        </div>
    )
}
