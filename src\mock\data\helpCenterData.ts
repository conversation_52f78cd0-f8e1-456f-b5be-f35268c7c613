export const categoriesData = [
    {
        name: 'Getting Started',
        topics: [
            {
                id: 'account-setup',
                name: 'Account Setup',
                description: 'Learn how to create and verify your trading account.',
                articleCounts: 3,
            },
            {
                id: 'platform-basics',
                name: 'Platform Basics',
                description: 'Get familiar with our trading platform features.',
                articleCounts: 4,
            },
            {
                id: 'first-trade',
                name: 'Your First Trade',
                description: 'Step-by-step guide to placing your first trade.',
                articleCounts: 2,
            },
        ],
    },
    {
        name: 'Trading & Security',
        topics: [
            {
                id: 'deposits-withdrawals',
                name: 'Deposits & Withdrawals',
                description: 'How to fund your account and withdraw profits.',
                articleCounts: 3,
            },
            {
                id: 'trading-tools',
                name: 'Trading Tools',
                description: 'Advanced tools and features for professional trading.',
                articleCounts: 4,
            },
            {
                id: 'security',
                name: 'Account Security',
                description: 'Keep your account safe and secure.',
                articleCounts: 2,
            },
            {
                id: 'risk-management',
                name: 'Risk Management',
                description: 'Learn to manage risk and protect your capital.',
                articleCounts: 3,
            },
        ],
    },
]

export const articleListData = [
    {
        id: 'account_setup_001',
        title: 'How to Create Your Trading Account',
        content: 'Learn how to create your MyBrokerForex trading account in just a few simple steps. This guide covers account registration, document verification, and initial setup.',
        category: 'account-setup',
        authors: [
            {
                name: 'Support Team',
                img: '/img/avatars/thumb-1.jpg',
            },
        ],
        tags: [
            { id: 1, label: 'Account' },
            { id: 2, label: 'Setup' },
        ],
        starred: true,
        published: true,
        updateTime: '1 week ago',
        updateTimeStamp: Date.now() - *********,
        createdBy: 'Support Team',
        timeToRead: 3,
        viewCount: 245,
        commentCount: 12,
    },
    {
        id: 'account_setup_002',
        title: 'Account Verification Process',
        content: 'Complete guide to verifying your trading account including required documents, verification timeline, and common issues.',
        category: 'account-setup',
        authors: [
            {
                name: 'Support Team',
                img: '/img/avatars/thumb-2.jpg',
            },
        ],
        tags: [
            { id: 1, label: 'Account' },
            { id: 3, label: 'Verification' },
        ],
        starred: false,
        published: true,
        updateTime: '2 weeks ago',
        updateTimeStamp: Date.now() - **********,
        createdBy: 'Support Team',
        timeToRead: 4,
        viewCount: 189,
        commentCount: 8,
    },
    {
        id: 'platform_basics_001',
        title: 'Trading Platform Overview',
        content: 'Get familiar with our advanced trading platform interface, charts, order types, and essential features for successful trading.',
        category: 'platform-basics',
        authors: [
            {
                name: 'Trading Team',
                img: '/img/avatars/thumb-3.jpg',
            },
        ],
        tags: [
            { id: 4, label: 'Platform' },
            { id: 5, label: 'Trading' },
        ],
        starred: true,
        published: true,
        updateTime: '3 days ago',
        updateTimeStamp: Date.now() - *********,
        createdBy: 'Trading Team',
        timeToRead: 5,
        viewCount: 312,
        commentCount: 15,
    },
    {
        id: 'first_trade_001',
        title: 'How to Place Your First Trade',
        content: 'Step-by-step instructions for placing your first forex trade including market analysis, position sizing, and risk management.',
        category: 'first-trade',
        authors: [
            {
                name: 'Trading Team',
                img: '/img/avatars/thumb-4.jpg',
            },
        ],
        tags: [
            { id: 5, label: 'Trading' },
            { id: 6, label: 'Beginner' },
        ],
        starred: true,
        published: true,
        updateTime: '5 days ago',
        updateTimeStamp: Date.now() - *********,
        createdBy: 'Trading Team',
        timeToRead: 6,
        viewCount: 428,
        commentCount: 23,
    },
    {
        id: 'deposits_001',
        title: 'How to Fund Your Account',
        content: 'Learn about available deposit methods, processing times, minimum amounts, and fees for funding your trading account.',
        category: 'deposits-withdrawals',
        authors: [
            {
                name: 'Finance Team',
                img: '/img/avatars/thumb-5.jpg',
            },
        ],
        tags: [
            { id: 7, label: 'Deposits' },
            { id: 8, label: 'Funding' },
        ],
        starred: false,
        published: true,
        updateTime: '1 week ago',
        updateTimeStamp: Date.now() - *********,
        createdBy: 'Finance Team',
        timeToRead: 4,
        viewCount: 156,
        commentCount: 6,
    },
    {
        id: 'security_001',
        title: 'Keeping Your Account Secure',
        content: 'Essential security practices including two-factor authentication, strong passwords, and recognizing phishing attempts.',
        category: 'security',
        authors: [
            {
                name: 'Security Team',
                img: '/img/avatars/thumb-6.jpg',
            },
        ],
        tags: [
            { id: 9, label: 'Security' },
            { id: 10, label: 'Safety' },
        ],
        starred: true,
        published: true,
        updateTime: '4 days ago',
        updateTimeStamp: Date.now() - *********,
        createdBy: 'Security Team',
        timeToRead: 3,
        viewCount: 203,
        commentCount: 9,
    },
]

export const articleTagsData = {
    tags: [
        { id: 1, label: 'Account' },
        { id: 2, label: 'Setup' },
        { id: 3, label: 'Verification' },
        { id: 4, label: 'Platform' },
        { id: 5, label: 'Trading' },
        { id: 6, label: 'Beginner' },
        { id: 7, label: 'Deposits' },
        { id: 8, label: 'Funding' },
        { id: 9, label: 'Security' },
        { id: 10, label: 'Safety' },
    ],
}

class ArticleList {
    list = articleListData

    getList() {
        return this.list
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setList(list: any) {
        this.list = list
    }
}

export const articleList = new ArticleList()

export const articleDetailData = {
    content: `
    <h1>How to Create Your Trading Account</h1>
    <p>Welcome to MyBrokerForex! Creating your trading account is the first step towards accessing the global forex markets. This comprehensive guide will walk you through the entire process.</p>

    <div id="registration">
        <h3>Account Registration</h3>
        <p>To begin trading with MyBrokerForex, you'll need to complete our simple registration process:</p>
        <ol>
            <li>Visit our registration page and provide your basic information</li>
            <li>Choose your preferred account type based on your trading experience</li>
            <li>Set up a secure password for your account</li>
            <li>Agree to our terms and conditions</li>
        </ol>
    </div>

    <div id="verification">
        <h3>Account Verification</h3>
        <p>For your security and regulatory compliance, we require account verification:</p>
        <ul>
            <li>Upload a government-issued photo ID</li>
            <li>Provide proof of address (utility bill or bank statement)</li>
            <li>Complete our risk assessment questionnaire</li>
        </ul>
        <p>Verification typically takes 24-48 hours during business days.</p>
    </div>

    <div id="funding">
        <h3>Fund Your Account</h3>
        <p>Once verified, you can fund your account using various methods:</p>
        <ul>
            <li>Bank transfer (3-5 business days)</li>
            <li>Credit/Debit card (instant)</li>
            <li>E-wallets (instant)</li>
        </ul>
    </div>
    `,
    tableOfContent: [
        {
            id: 'registration',
            label: 'Account Registration',
        },
        {
            id: 'verification',
            label: 'Account Verification',
        },
        {
            id: 'funding',
            label: 'Fund Your Account',
        },
    ],
}