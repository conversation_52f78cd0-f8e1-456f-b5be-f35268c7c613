'use client'

import { motion } from 'framer-motion'
import { TbTrendingUp, TbWorld, TbShield, Tb<PERSON>lock, TbChartLine, TbCurrencyBitcoin } from 'react-icons/tb'
import { HiArrowRight } from 'react-icons/hi2'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function CryptoPage() {
    const majorCryptos = [
        {
            name: 'Bitcoin',
            symbol: 'BTCUSD',
            price: '$43,567.89',
            change: '+2.45%',
            icon: '₿'
        },
        {
            name: 'Ethereum',
            symbol: 'ETHUSD',
            price: '$2,634.56',
            change: '+1.78%',
            icon: 'Ξ'
        },
        {
            name: 'Rip<PERSON>',
            symbol: 'XRPUSD',
            price: '$0.6234',
            change: '-0.89%',
            icon: '◉'
        },
        {
            name: 'Litecoin',
            symbol: 'LTCUSD',
            price: '$73.45',
            change: '+0.56%',
            icon: 'Ł'
        },
        {
            name: '<PERSON><PERSON>',
            symbol: 'ADAUSD',
            price: '$0.4567',
            change: '+3.21%',
            icon: '₳'
        },
        {
            name: '<PERSON><PERSON><PERSON>',
            symbol: 'DOTUSD',
            price: '$6.78',
            change: '-1.23%',
            icon: '●'
        }
    ]

    const features = [
        {
            icon: TbTrendingUp,
            title: 'High Volatility',
            description: 'Capitalize on the high volatility of cryptocurrency markets for potential profits.'
        },
        {
            icon: TbWorld,
            title: '24/7 Trading',
            description: 'Trade cryptocurrencies 24 hours a day, 7 days a week, including weekends.'
        },
        {
            icon: TbShield,
            title: 'Secure Trading',
            description: 'Trade crypto CFDs without owning the underlying assets, reducing security risks.'
        },
        {
            icon: TbClock,
            title: 'Instant Execution',
            description: 'Execute trades instantly with our advanced trading infrastructure.'
        }
    ]

    const tradingBenefits = [
        'Trade 15+ major cryptocurrencies',
        'Leverage up to 1:10 on crypto CFDs',
        'No digital wallet required',
        'Advanced charting and analysis',
        'Real-time market data and news',
        'Mobile and desktop platforms'
    ]

    const cryptoCategories = [
        {
            category: 'Major Coins',
            cryptos: ['Bitcoin (BTC)', 'Ethereum (ETH)', 'Ripple (XRP)', 'Litecoin (LTC)'],
            description: 'Trade the most established cryptocurrencies'
        },
        {
            category: 'Altcoins',
            cryptos: ['Cardano (ADA)', 'Polkadot (DOT)', 'Chainlink (LINK)', 'Stellar (XLM)'],
            description: 'Access emerging alternative cryptocurrencies'
        },
        {
            category: 'DeFi Tokens',
            cryptos: ['Uniswap (UNI)', 'Aave (AAVE)', 'Compound (COMP)', 'Maker (MKR)'],
            description: 'Trade decentralized finance tokens'
        }
    ]

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-5xl text-gray-300 font-bold mb-6 mt-8">
                                Cryptocurrency Trading
                            </h1>
                            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Trade CFDs on Bitcoin, Ethereum, and other major cryptocurrencies.
                                Access the crypto market 24/7 with competitive spreads and professional tools.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center"
                                >
                                    Start Trading Crypto
                                    <HiArrowRight className="ml-2 w-5 h-5" />
                                </button>
                                <button className="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-gray-900 transition-colors duration-200">
                                    Try Demo Account
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* Live Prices Section */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Live Crypto Prices
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Real-time pricing on major cryptocurrencies
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {majorCryptos.map((crypto, index) => (
                                <motion.div
                                    key={crypto.symbol}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex items-center">
                                            <span className="text-2xl mr-3 font-bold">{crypto.icon}</span>
                                            <div>
                                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                                    {crypto.name}
                                                </h3>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    {crypto.symbol}
                                                </p>
                                            </div>
                                        </div>
                                        <TbChartLine className="w-6 h-6 text-[#EA5455]" />
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {crypto.price}
                                        </span>
                                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                            crypto.change.startsWith('+') 
                                                ? 'bg-[#28C76F] bg-opacity-10 text-[#28C76F]' 
                                                : 'bg-[#EA5455] bg-opacity-10 text-[#EA5455]'
                                        }`}>
                                            {crypto.change}
                                        </span>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Why Trade Crypto CFDs?
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Discover the advantages of cryptocurrency trading
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <feature.icon className="w-8 h-8 text-white" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Crypto Categories */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Cryptocurrency Categories
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Trade different types of digital assets
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {cryptoCategories.map((category, index) => (
                                <motion.div
                                    key={category.category}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg"
                                >
                                    <div className="text-center mb-6">
                                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                            {category.category}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {category.description}
                                        </p>
                                    </div>
                                    <ul className="space-y-2">
                                        {category.cryptos.map((crypto, idx) => (
                                            <li key={idx} className="flex items-center">
                                                <div className="w-2 h-2 bg-[#EA5455] rounded-full mr-3"></div>
                                                <span className="text-gray-700 dark:text-gray-300">{crypto}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Trading Benefits Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                            <motion.div
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Advanced Crypto Trading
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                    Trade cryptocurrencies with our professional platform featuring 
                                    real-time data, advanced charting, and secure execution.
                                </p>
                                <ul className="space-y-4">
                                    {tradingBenefits.map((benefit, index) => (
                                        <li key={index} className="flex items-center">
                                            <div className="w-2 h-2 bg-[#EA5455] rounded-full mr-4"></div>
                                            <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                                        </li>
                                    ))}
                                </ul>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, x: 30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl"
                            >
                                <div className="text-center">
                                    <TbCurrencyBitcoin className="w-16 h-16 text-[#EA5455] mx-auto mb-6" />
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        Start Trading Today
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        Open your account and start trading cryptocurrencies with 
                                        competitive spreads and professional tools.
                                    </p>
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="w-full py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                    >
                                        Open Trading Account
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </section>

                {/* Risk Warning */}
                <section className="py-12 bg-gray-100 dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center"
                        >
                            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6">
                                <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                                    Risk Warning
                                </h3>
                                <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                                    Cryptocurrency trading involves substantial risk and may not be suitable for all investors.
                                    Prices can be extremely volatile and you may lose your entire investment. Please ensure you
                                    understand the risks involved before trading.
                                </p>
                            </div>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PublicPageLayout>
    )
}
