MyBrokerForex Website – Sitemap & Structure
==========================================

Home
└── /

About
└── /about

Account Types
├── /account-types         (Overview)
└── /account-type          (Individual Details)

Contact & Support
├── /contact
└── /support

Education Center
├── /education-center      (Main)
├── /education/market-news
├── /education/economic-calendar
├── /education/trading-simulator
├── /education/glossary
├── /education/demo-account
├── /education/ebooks-guides
├── /education/trading-webinars
├── /education/video-tutorials
├── /education/risk-management
├── /education/market-analysis
├── /education/advanced-strategies
└── /education/trading-basics

Analysis Center
└── /analysis-center

News
└── /news

Partner System
└── /partner-system

Pricing
└── /pricing

Products
└── /products

Promotion
├── /promotion
└── /promotions

Tools Suite
├── /tools                 (Overview)
├── /tools/live-rates
├── /tools/correlation
├── /tools/volatility
├── /tools/market-hours
├── /tools/fibonacci
├── /tools/risk-calculator
├── /tools/swap-calculator
├── /tools/margin-calculator
├── /tools/position-size
├── /tools/profit-calculator
├── /tools/currency-converter
└── /tools/pip-calculator

Affiliate Program
├── /affiliate             (Main)
├── /affiliate/support
├── /affiliate/faq
├── /affiliate/getting-started
├── /affiliate/api
├── /affiliate/reports
├── /affiliate/tracking
├── /affiliate/portal
├── /affiliate/terms
├── /affiliate/payments
├── /affiliate/commissions
├── /affiliate/materials
└── /affiliate/how-it-works

Trading Section
├── /trading               (Overview)
├── /trading/news
├── /trading/calendar
├── /trading/signals
├── /trading/analysis
├── /trading/apis
├── /trading/crypto
├── /trading/indices
├── /trading/forex
├── /trading/commodities
├── /trading/desktop
├── /trading/mobile
└── /trading/web-platform

Landing Page
└── /landing

Other
├── /not-found             (Custom 404)
└── [Protected/Admin Pages]

------------------------------------------
This structure demonstrates the depth and organization of the website, with 40+ unique, content-rich pages, grouped by feature and user journey. Each section is implemented as a dedicated Next.js page or module, reflecting significant development and planning effort. 