'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { HiArrowLeft, HiShieldCheck } from 'react-icons/hi2'
import { useRouter } from 'next/navigation'

const RiskCalculatorPage = () => {
    const router = useRouter()
    const [accountBalance, setAccountBalance] = useState('10000')
    const [riskPercentage, setRiskPercentage] = useState('2')
    const [entryPrice, setEntryPrice] = useState('1.0850')
    const [stopLoss, setStopLoss] = useState('1.0800')
    const [takeProfit, setTakeProfit] = useState('1.0950')
    const [accountCurrency, setAccountCurrency] = useState('USD')
    const [instrument, setInstrument] = useState('EUR/USD')
    const [riskAmount, setRiskAmount] = useState(0)
    const [rewardAmount, setRewardAmount] = useState(0)
    const [riskRewardRatio, setRiskRewardRatio] = useState(0)
    const [positionSize, setPositionSize] = useState(0)
    const [pipValue, setPipValue] = useState(0)

    const instruments = [
        { value: 'EUR/USD', label: 'EUR/USD', pipValue: 10 },
        { value: 'GBP/USD', label: 'GBP/USD', pipValue: 10 },
        { value: 'USD/JPY', label: 'USD/JPY', pipValue: 0.91 },
        { value: 'AUD/USD', label: 'AUD/USD', pipValue: 10 },
        { value: 'USD/CAD', label: 'USD/CAD', pipValue: 7.43 },
        { value: 'USD/CHF', label: 'USD/CHF', pipValue: 11.17 },
        { value: 'NZD/USD', label: 'NZD/USD', pipValue: 10 },
        { value: 'EUR/GBP', label: 'EUR/GBP', pipValue: 12.65 }
    ]

    useEffect(() => {
        calculateRisk()
    }, [accountBalance, riskPercentage, entryPrice, stopLoss, takeProfit, instrument])

    const calculateRisk = () => {
        const balance = parseFloat(accountBalance) || 0
        const riskPercent = parseFloat(riskPercentage) || 0
        const entry = parseFloat(entryPrice) || 0
        const sl = parseFloat(stopLoss) || 0
        const tp = parseFloat(takeProfit) || 0

        const selectedInstrument = instruments.find(inst => inst.value === instrument)
        if (!selectedInstrument) return

        // Calculate risk amount
        const riskAmt = (balance * riskPercent) / 100
        setRiskAmount(riskAmt)

        // Calculate pip values
        const riskPips = Math.abs(entry - sl) * 10000
        const rewardPips = Math.abs(tp - entry) * 10000
        
        // Calculate reward amount
        const rewardAmt = (rewardPips / riskPips) * riskAmt
        setRewardAmount(rewardAmt)

        // Calculate risk-reward ratio
        const rrRatio = rewardPips / riskPips
        setRiskRewardRatio(rrRatio)

        // Calculate position size
        const pipVal = selectedInstrument.pipValue
        setPipValue(pipVal)
        const posSize = riskAmt / (riskPips * pipVal / 100000)
        setPositionSize(posSize)
    }



    const getRiskLevel = () => {
        const risk = parseFloat(riskPercentage)
        if (risk <= 1) return { level: 'Conservative', color: 'text-[#28C76F]', bg: 'bg-[#28C76F]/10 dark:bg-[#28C76F]/20' }
        if (risk <= 2) return { level: 'Moderate', color: 'text-gray-600 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-700' }
        if (risk <= 5) return { level: 'Aggressive', color: 'text-[#EA5455]', bg: 'bg-[#EA5455]/10 dark:bg-[#EA5455]/20' }
        return { level: 'High Risk', color: 'text-[#EA5455]', bg: 'bg-[#EA5455]/20 dark:bg-[#EA5455]/30' }
    }

    const riskLevel = getRiskLevel()

    return (
        <PublicPageLayout>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black py-20 overflow-hidden">
                <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >

                        <h1 className="text-5xl text-gray-300 font-bold mt-8 mb-6">
                            Risk Calculator
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Calculate your trading risk, position size, and risk-reward ratio. 
                            Essential for proper risk management and consistent trading results.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Calculator Section */}
            <section className="py-16 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        {/* Calculator Form */}
                        <motion.div
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl"
                        >
                            <div className="flex items-center gap-3 mb-8">
                                <div className="p-3 bg-[#EA5455] bg-opacity-10 rounded-xl">
                                    <HiShieldCheck className="w-6 h-6 text-[#EA5455]" />
                                </div>
                                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                                    Risk Calculator
                                </h2>
                            </div>

                            <div className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Account Balance ({accountCurrency})
                                    </label>
                                    <input
                                        type="number"
                                        value={accountBalance}
                                        onChange={(e) => setAccountBalance(e.target.value)}
                                        placeholder="10000"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Risk Percentage (%)
                                    </label>
                                    <input
                                        type="number"
                                        value={riskPercentage}
                                        onChange={(e) => setRiskPercentage(e.target.value)}
                                        placeholder="2"
                                        step="0.1"
                                        max="10"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                    <div className={`mt-2 px-3 py-2 rounded-lg ${riskLevel.bg}`}>
                                        <span className={`text-sm font-medium ${riskLevel.color}`}>
                                            Risk Level: {riskLevel.level}
                                        </span>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Trading Instrument
                                    </label>
                                    <select
                                        value={instrument}
                                        onChange={(e) => setInstrument(e.target.value)}
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    >
                                        {instruments.map((inst) => (
                                            <option key={inst.value} value={inst.value}>
                                                {inst.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Entry Price
                                    </label>
                                    <input
                                        type="number"
                                        value={entryPrice}
                                        onChange={(e) => setEntryPrice(e.target.value)}
                                        placeholder="1.0850"
                                        step="0.0001"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Stop Loss
                                    </label>
                                    <input
                                        type="number"
                                        value={stopLoss}
                                        onChange={(e) => setStopLoss(e.target.value)}
                                        placeholder="1.0800"
                                        step="0.0001"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Take Profit
                                    </label>
                                    <input
                                        type="number"
                                        value={takeProfit}
                                        onChange={(e) => setTakeProfit(e.target.value)}
                                        placeholder="1.0950"
                                        step="0.0001"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                </div>
                            </div>
                        </motion.div>

                        {/* Results */}
                        <motion.div
                            initial={{ opacity: 0, x: 30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            className="space-y-6"
                        >
                            {riskAmount > 0 && (
                                <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                        Risk Analysis Results
                                    </h3>
                                    <div className="space-y-4">
                                        <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                            <span className="text-gray-600 dark:text-gray-400">Risk Amount:</span>
                                            <span className="font-semibold text-[#EA5455]">
                                                {riskAmount.toFixed(2)} {accountCurrency}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                            <span className="text-gray-600 dark:text-gray-400">Potential Reward:</span>
                                            <span className="font-semibold text-[#28C76F]">
                                                {rewardAmount.toFixed(2)} {accountCurrency}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                            <span className="text-gray-600 dark:text-gray-400">Risk:Reward Ratio:</span>
                                            <span className={`font-semibold ${riskRewardRatio >= 2 ? 'text-[#28C76F]' : riskRewardRatio >= 1 ? 'text-gray-600 dark:text-gray-400' : 'text-[#EA5455]'}`}>
                                                1:{riskRewardRatio.toFixed(2)}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center py-2">
                                            <span className="text-gray-600 dark:text-gray-400">Position Size:</span>
                                            <span className="font-semibold text-gray-900 dark:text-white">
                                                {positionSize.toFixed(0)} units
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Risk Assessment */}
                            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                    Risk Assessment
                                </h3>
                                <div className="space-y-3">
                                    <div className={`p-4 rounded-xl ${riskLevel.bg}`}>
                                        <div className={`font-semibold ${riskLevel.color} mb-1`}>
                                            {riskLevel.level} Risk Profile
                                        </div>
                                        <div className="text-sm text-gray-600 dark:text-gray-400">
                                            {parseFloat(riskPercentage)}% of account balance at risk
                                        </div>
                                    </div>
                                    
                                    {riskRewardRatio > 0 && (
                                        <div className={`p-4 rounded-xl ${
                                            riskRewardRatio >= 2
                                                ? 'bg-[#28C76F]/10 dark:bg-[#28C76F]/20'
                                                : riskRewardRatio >= 1
                                                    ? 'bg-gray-100 dark:bg-gray-700'
                                                    : 'bg-[#EA5455]/10 dark:bg-[#EA5455]/20'
                                        }`}>
                                            <div className={`font-semibold mb-1 ${
                                                riskRewardRatio >= 2
                                                    ? 'text-[#28C76F]'
                                                    : riskRewardRatio >= 1
                                                        ? 'text-gray-600 dark:text-gray-400'
                                                        : 'text-[#EA5455]'
                                            }`}>
                                                {riskRewardRatio >= 2 ? 'Excellent' : riskRewardRatio >= 1 ? 'Good' : 'Poor'} Risk:Reward
                                            </div>
                                            <div className="text-sm text-gray-600 dark:text-gray-400">
                                                {riskRewardRatio >= 2 
                                                    ? 'Great risk-reward ratio for this trade' 
                                                    : riskRewardRatio >= 1 
                                                        ? 'Acceptable risk-reward ratio' 
                                                        : 'Consider adjusting stop loss or take profit'
                                                }
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Educational Content */}
                            <div className="bg-[#28C76F]/10 dark:bg-[#28C76F]/20 border border-[#28C76F]/30 dark:border-[#28C76F]/40 rounded-xl p-6">
                                <h3 className="text-lg font-semibold text-[#28C76F] mb-3">
                                    Risk Management Tips
                                </h3>
                                <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                                    <li>• <strong>2% Rule:</strong> Never risk more than 2% per trade</li>
                                    <li>• <strong>Risk:Reward:</strong> Aim for minimum 1:2 ratio</li>
                                    <li>• <strong>Position Size:</strong> Adjust based on stop loss distance</li>
                                    <li>• <strong>Consistency:</strong> Use same risk % for all trades</li>
                                </ul>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </section>
        </PublicPageLayout>
    )
}

export default RiskCalculatorPage
