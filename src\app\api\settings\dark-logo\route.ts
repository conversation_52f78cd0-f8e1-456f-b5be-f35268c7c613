import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import pool from '@/server/db'

export async function GET() {
    try {
        const [rows] = await pool.query(
            "SELECT setting_value FROM settings WHERE setting_key = 'dark_logo' LIMIT 1"
        )

        if (Array.isArray(rows) && rows.length > 0 && (rows as any[])[0].setting_value) {
            return NextResponse.json({
                success: true,
                darkLogoPath: (rows as any[])[0].setting_value
            })
        }

        return NextResponse.json({
            success: false,
            darkLogoPath: null
        })
    } catch (error) {
        console.log('Dark logo fetch error:', error)
        return NextResponse.json({
            success: false,
            darkLogoPath: null
        })
    }
}

export async function POST(request: NextRequest) {
    try {
        const formData = await request.formData()
        const file = formData.get('darkLogo') as File
        
        if (!file) {
            return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
        }

        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml']
        if (!allowedTypes.includes(file.type)) {
            return NextResponse.json({ 
                error: 'Invalid file type. Only PNG, JPG, and SVG files are allowed.' 
            }, { status: 400 })
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            return NextResponse.json({ 
                error: 'File too large. Maximum size is 5MB.' 
            }, { status: 400 })
        }

        const bytes = await file.arrayBuffer()
        const buffer = Buffer.from(bytes)

        // Create uploads directory if it doesn't exist
        const uploadsDir = join(process.cwd(), 'public', 'uploads', 'logos')
        await mkdir(uploadsDir, { recursive: true })

        // Generate unique filename
        const timestamp = Date.now()
        const extension = file.name.split('.').pop()
        const filename = `dark-logo-${timestamp}.${extension}`
        const filepath = join(uploadsDir, filename)

        // Save file
        await writeFile(filepath, buffer)

        // Update database with new dark logo path
        const darkLogoPath = `/uploads/logos/${filename}`
        await pool.query(
            "INSERT INTO settings (setting_key, setting_value) VALUES ('dark_logo', ?) ON DUPLICATE KEY UPDATE setting_value = ?",
            [darkLogoPath, darkLogoPath]
        )

        return NextResponse.json({ 
            success: true, 
            darkLogoPath,
            message: 'Dark logo uploaded successfully' 
        })

    } catch (error) {
        console.error('Dark logo upload error:', error)
        return NextResponse.json({ 
            error: 'Failed to upload dark logo' 
        }, { status: 500 })
    }
}
