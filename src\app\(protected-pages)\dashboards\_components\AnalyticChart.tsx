'use client'

import { Card } from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'
import { useState } from 'react'
import Button from '@/components/ui/Button'

interface AnalyticChartProps {
    data: {
        webAnalytic: {
            pageView: { value: number; growShrink: number }
            avgTimeOnPage: { value: string; growShrink: number }
            series: Array<{
                name: string
                data: number[]
            }>
            date: string[]
        }
    }
}

const AnalyticChart = ({ data }: AnalyticChartProps) => {
    const [chartType, setChartType] = useState<'area' | 'line' | 'bar'>('area')

    return (
        <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h3 className="text-lg font-semibold">Website Analytics Overview</h3>
                    <p className="text-sm text-muted-foreground">
                        Track page views and user engagement over time
                    </p>
                </div>
                
                <div className="flex items-center gap-2">
                    <Button
                        variant={chartType === 'area' ? 'default' : 'plain'}
                        size="sm"
                        onClick={() => setChartType('area')}
                    >
                        Area
                    </Button>
                    <Button
                        variant={chartType === 'line' ? 'default' : 'plain'}
                        size="sm"
                        onClick={() => setChartType('line')}
                    >
                        Line
                    </Button>
                    <Button
                        variant={chartType === 'bar' ? 'default' : 'plain'}
                        size="sm"
                        onClick={() => setChartType('bar')}
                    >
                        Bar
                    </Button>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-blue-600 dark:text-blue-400">Total Page Views</p>
                            <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                                {data.webAnalytic.pageView.value.toLocaleString()}
                            </p>
                        </div>
                        <div className={`text-sm ${data.webAnalytic.pageView.growShrink > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {data.webAnalytic.pageView.growShrink > 0 ? '+' : ''}{data.webAnalytic.pageView.growShrink}%
                        </div>
                    </div>
                </div>
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-green-600 dark:text-green-400">Avg Time on Page</p>
                            <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                                {data.webAnalytic.avgTimeOnPage.value}
                            </p>
                        </div>
                        <div className={`text-sm ${data.webAnalytic.avgTimeOnPage.growShrink > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {data.webAnalytic.avgTimeOnPage.growShrink > 0 ? '+' : ''}{data.webAnalytic.avgTimeOnPage.growShrink}%
                        </div>
                    </div>
                </div>
            </div>

            <Chart
                type={chartType}
                height={400}
                series={data.webAnalytic.series}
                xAxis={data.webAnalytic.date}
                customOptions={{
                    chart: {
                        toolbar: {
                            show: true,
                            tools: {
                                download: true,
                                selection: true,
                                zoom: true,
                                zoomin: true,
                                zoomout: true,
                                pan: true,
                                reset: true
                            }
                        }
                    },
                    colors: ['#EA5455', '#28C76F', '#2a85ff'],
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth',
                        width: chartType === 'line' ? 3 : 2
                    },
                    fill: {
                        type: chartType === 'area' ? 'gradient' : 'solid',
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0.1,
                            stops: [0, 90, 100]
                        }
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: 'left'
                    },
                    grid: {
                        borderColor: '#e0e6ed',
                        strokeDashArray: 5
                    },
                    tooltip: {
                        theme: 'dark',
                        y: {
                            formatter: function (val: number) {
                                return val.toLocaleString() + ' views'
                            }
                        }
                    }
                }}
            />
        </Card>
    )
}

export default AnalyticChart
