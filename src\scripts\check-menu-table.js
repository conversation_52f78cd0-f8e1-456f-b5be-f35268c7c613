const mysql = require('mysql2/promise');

async function checkMenuTable() {
    let connection;
    
    try {
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'mybrokerforex'
        });

        console.log('Connected to MySQL database');

        // Check menus table
        try {
            const [structure] = await connection.execute("DESCRIBE menus");
            console.log('\nMenus table structure:');
            structure.forEach(col => {
                console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'} ${col.Key ? `[${col.Key}]` : ''}`);
            });
        } catch (error) {
            console.log('Menus table does not exist');
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

checkMenuTable();
