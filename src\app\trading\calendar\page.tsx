'use client'

import { motion } from 'framer-motion'
import { TbCalendar, Tb<PERSON>lock, TbTrendingUp, Tb<PERSON>lertTriangle, TbTarget, TbChartLine } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

const TradingCalendarPage = () => {
    const calendarFeatures = [
        {
            icon: TbCalendar,
            title: 'Economic Events',
            description: 'Stay informed about major economic events that impact currency markets.',
            features: ['Central Bank Meetings', 'Economic Releases', 'Political Events', 'Market Holidays']
        },
        {
            icon: TbClock,
            title: 'Real-Time Updates',
            description: 'Get instant updates on economic events as they happen around the world.',
            features: ['Live Updates', 'Time Zone Conversion', 'Event Notifications', 'Historical Data']
        },
        {
            icon: TbTrendingUp,
            title: 'Market Impact',
            description: 'Understand the potential market impact of each economic event.',
            features: ['Impact Rating', 'Currency Affected', 'Volatility Forecast', 'Trading Opportunities']
        },
        {
            icon: Tb<PERSON><PERSON><PERSON>Triangle,
            title: 'Risk Management',
            description: 'Plan your trades around high-impact events to manage risk effectively.',
            features: ['Risk Alerts', 'Position Sizing', 'Stop Loss Guidance', 'Market Volatility']
        }
    ]

    const upcomingEvents = [
        {
            time: '08:30',
            currency: 'USD',
            event: 'Non-Farm Payrolls',
            impact: 'High',
            forecast: '185K',
            previous: '178K',
            date: 'Today'
        },
        {
            time: '10:00',
            currency: 'EUR',
            event: 'ECB Interest Rate Decision',
            impact: 'High',
            forecast: '4.50%',
            previous: '4.50%',
            date: 'Today'
        },
        {
            time: '14:30',
            currency: 'GBP',
            event: 'GDP Growth Rate',
            impact: 'Medium',
            forecast: '0.2%',
            previous: '0.1%',
            date: 'Tomorrow'
        },
        {
            time: '23:50',
            currency: 'JPY',
            event: 'Bank of Japan Meeting',
            impact: 'High',
            forecast: '-0.10%',
            previous: '-0.10%',
            date: 'Tomorrow'
        },
        {
            time: '12:30',
            currency: 'CAD',
            event: 'Employment Change',
            impact: 'Medium',
            forecast: '25K',
            previous: '22K',
            date: 'Friday'
        }
    ]

    const impactColors: { [key: string]: string } = {
        'High': 'bg-[#EA5455] text-white',
        'Medium': 'bg-orange-500 text-white',
        'Low': 'bg-[#28C76F] text-white'
    }

    const currencyFlags: { [key: string]: string } = {
        'USD': '🇺🇸',
        'EUR': '🇪🇺',
        'GBP': '🇬🇧',
        'JPY': '🇯🇵',
        'CAD': '🇨🇦',
        'AUD': '🇦🇺',
        'CHF': '🇨🇭',
        'NZD': '🇳🇿'
    }

    return (
        <div className="mt-0">
            <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl text-gray-300 mt-8 md:text-6xl font-bold mb-6">
                                    Economic Calendar
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Stay ahead of market-moving events with our comprehensive economic calendar
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-[#EA5455] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#d63384] transition-colors duration-200"
                                >
                                    Start Trading Now
                                </button>
                            </motion.div>
                        </div>
                    </section>

                    {/* Calendar Features */}
                    <section className="py-20">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Calendar Features
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Comprehensive tools to track and analyze market-moving economic events
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {calendarFeatures.map((feature, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                    >
                                        <div className="flex items-center mb-6">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                                <feature.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                                {feature.title}
                                            </h3>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                                            {feature.description}
                                        </p>
                                        <ul className="space-y-2">
                                            {feature.features.map((item, itemIndex) => (
                                                <li key={itemIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                    <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                    {item}
                                                </li>
                                            ))}
                                        </ul>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Upcoming Events */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Upcoming Events
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Key economic events that could impact your trading decisions
                                </p>
                            </motion.div>

                            <div className="overflow-x-auto">
                                <table className="w-full bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden">
                                    <thead className="bg-gray-100 dark:bg-gray-600">
                                        <tr>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Date</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Time</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Currency</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Event</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Impact</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Forecast</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Previous</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {upcomingEvents.map((event, index) => (
                                            <motion.tr
                                                key={index}
                                                initial={{ opacity: 0, y: 20 }}
                                                whileInView={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.4, delay: index * 0.1 }}
                                                className="border-b border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
                                            >
                                                <td className="px-6 py-4 text-gray-600 dark:text-gray-300 font-medium">
                                                    {event.date}
                                                </td>
                                                <td className="px-6 py-4 text-gray-600 dark:text-gray-300 font-mono">
                                                    {event.time}
                                                </td>
                                                <td className="px-6 py-4">
                                                    <div className="flex items-center">
                                                        <span className="text-2xl mr-2">{currencyFlags[event.currency] || '💱'}</span>
                                                        <span className="font-semibold text-gray-900 dark:text-white">
                                                            {event.currency}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 text-gray-900 dark:text-white font-medium">
                                                    {event.event}
                                                </td>
                                                <td className="px-6 py-4">
                                                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${impactColors[event.impact] || 'bg-gray-500 text-white'}`}>
                                                        {event.impact}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 text-gray-600 dark:text-gray-300 font-mono">
                                                    {event.forecast}
                                                </td>
                                                <td className="px-6 py-4 text-gray-500 dark:text-gray-400 font-mono">
                                                    {event.previous}
                                                </td>
                                            </motion.tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </section>

                    {/* Trading Tips */}
                    <section className="py-20">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="bg-gradient-to-r from-[#EA5455] to-[#d63384] rounded-2xl p-8 md:p-12 text-white"
                            >
                                <div className="text-center mb-8">
                                    <h2 className="text-3xl md:text-4xl font-bold mb-4">
                                        Trading Around Economic Events
                                    </h2>
                                    <p className="text-xl text-white/90">
                                        Essential tips for trading during high-impact economic releases
                                    </p>
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="text-center">
                                        <TbAlertTriangle className="w-12 h-12 mx-auto mb-4 text-white/80" />
                                        <h3 className="text-xl font-bold mb-2">Before the Event</h3>
                                        <p className="text-white/80">
                                            Review forecasts, set alerts, and prepare your trading strategy
                                        </p>
                                    </div>
                                    <div className="text-center">
                                        <TbChartLine className="w-12 h-12 mx-auto mb-4 text-white/80" />
                                        <h3 className="text-xl font-bold mb-2">During the Event</h3>
                                        <p className="text-white/80">
                                            Monitor price action and be ready to act on volatility spikes
                                        </p>
                                    </div>
                                    <div className="text-center">
                                        <TbTarget className="w-12 h-12 mx-auto mb-4 text-white/80" />
                                        <h3 className="text-xl font-bold mb-2">After the Event</h3>
                                        <p className="text-white/80">
                                            Analyze market reaction and adjust positions accordingly
                                        </p>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-[#EA5455]">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Trade with Market Intelligence
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                    Use our economic calendar to make informed trading decisions
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                                >
                                    Open Trading Account
                                </button>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
        </div>
    )
}

export default TradingCalendarPage
