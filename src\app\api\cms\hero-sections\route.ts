import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'
import cache, { CacheKeys, CacheTTL, invalidateCache } from '@/utils/cache'

// GET /api/cms/hero-sections - Get all hero sections
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const active = searchParams.get('active')

        // Check cache first
        const cacheKey = CacheKeys.heroSections(active === 'true')
        const cachedData = cache.get(cacheKey)

        if (cachedData) {
            return NextResponse.json({
                success: true,
                data: cachedData,
                cached: true
            })
        }

        let query = 'SELECT * FROM hero_sections'
        const params: any[] = []

        if (active) {
            query += ' WHERE is_active = ?'
            params.push(active === 'true')
        }

        query += ' ORDER BY created_at DESC'

        const [rows] = await pool.execute<RowDataPacket[]>(query, params)

        // Cache the result for 15 minutes
        cache.set(cacheKey, rows, CacheTTL.LONG)

        return NextResponse.json({
            success: true,
            data: rows
        })
    } catch (error) {
        console.error('Error fetching hero sections:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch hero sections' },
            { status: 500 }
        )
    }
}

// POST /api/cms/hero-sections - Create new hero section
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            title, 
            subtitle, 
            video_url, 
            cta_link, 
            cta_text, 
            image, 
            background_type = 'image', 
            is_active = true 
        } = body
        
        if (!title) {
            return NextResponse.json(
                { success: false, error: 'Title is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO hero_sections (title, subtitle, video_url, cta_link, cta_text, image, background_type, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [title, subtitle, video_url, cta_link, cta_text, image, background_type, is_active]
        )

        // Invalidate hero sections cache
        invalidateCache('hero-sections')

        return NextResponse.json({
            success: true,
            data: { id: result.insertId, ...body }
        }, { status: 201 })
    } catch (error) {
        console.error('Error creating hero section:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to create hero section' },
            { status: 500 }
        )
    }
}

// PUT /api/cms/hero-sections - Update hero section
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            id,
            title, 
            subtitle, 
            video_url, 
            cta_link, 
            cta_text, 
            image, 
            background_type, 
            is_active 
        } = body
        
        if (!id || !title) {
            return NextResponse.json(
                { success: false, error: 'ID and title are required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE hero_sections SET title = ?, subtitle = ?, video_url = ?, cta_link = ?, cta_text = ?, image = ?, background_type = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [title, subtitle, video_url, cta_link, cta_text, image, background_type, is_active, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Hero section not found' },
                { status: 404 }
            )
        }

        // Invalidate hero sections cache
        invalidateCache('hero-sections')

        return NextResponse.json({
            success: true,
            data: body
        })
    } catch (error) {
        console.error('Error updating hero section:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to update hero section' },
            { status: 500 }
        )
    }
}

// DELETE /api/cms/hero-sections - Delete hero section
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM hero_sections WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Hero section not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Hero section deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting hero section:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete hero section' },
            { status: 500 }
        )
    }
}
