const mysql = require('mysql2/promise')

async function setupDefaultSettings() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'mybrokerforex'
    })

    console.log('Connected to database')

    try {
        // Check if settings table exists
        const [tables] = await connection.execute(
            "SHOW TABLES LIKE 'settings'"
        )

        if (tables.length === 0) {
            console.log('Settings table does not exist. Creating...')
            await connection.execute(`
                CREATE TABLE settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(255) UNIQUE NOT NULL,
                    setting_value TEXT,
                    setting_type ENUM('text', 'json', 'boolean', 'number') DEFAULT 'text',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            `)
            console.log('Settings table created successfully')
        }

        // Default SEO settings
        const seoSettings = [
            {
                key: 'seo_site_title',
                value: 'MyBrokerForex - Professional Forex Trading Platform',
                type: 'text',
                description: 'Main site title for SEO'
            },
            {
                key: 'seo_site_description',
                value: 'Professional forex trading platform with competitive spreads, advanced tools, and expert support. Start trading with confidence.',
                type: 'text',
                description: 'Site meta description'
            },
            {
                key: 'seo_meta_keywords',
                value: 'forex, trading, broker, currency, investment, financial markets',
                type: 'text',
                description: 'Meta keywords for SEO'
            },
            {
                key: 'seo_google_analytics_id',
                value: '',
                type: 'text',
                description: 'Google Analytics tracking ID'
            },
            {
                key: 'seo_google_search_console',
                value: '',
                type: 'text',
                description: 'Google Search Console verification code'
            },
            {
                key: 'seo_robots_txt',
                value: 'User-agent: *\nAllow: /',
                type: 'text',
                description: 'Robots.txt content'
            },
            {
                key: 'seo_sitemap_enabled',
                value: 'true',
                type: 'boolean',
                description: 'Enable XML sitemap generation'
            }
        ]

        // Default Email settings
        const emailSettings = [
            {
                key: 'email_smtp_host',
                value: '',
                type: 'text',
                description: 'SMTP server host'
            },
            {
                key: 'email_smtp_port',
                value: '587',
                type: 'text',
                description: 'SMTP server port'
            },
            {
                key: 'email_smtp_username',
                value: '',
                type: 'text',
                description: 'SMTP username'
            },
            {
                key: 'email_smtp_password',
                value: '',
                type: 'text',
                description: 'SMTP password'
            },
            {
                key: 'email_smtp_encryption',
                value: 'tls',
                type: 'text',
                description: 'SMTP encryption method'
            },
            {
                key: 'email_from_email',
                value: '<EMAIL>',
                type: 'text',
                description: 'Default from email address'
            },
            {
                key: 'email_from_name',
                value: 'MyBrokerForex',
                type: 'text',
                description: 'Default from name'
            },
            {
                key: 'email_test_email',
                value: '',
                type: 'text',
                description: 'Test email address'
            }
        ]

        // Default Security settings
        const securitySettings = [
            {
                key: 'security_two_factor_enabled',
                value: 'false',
                type: 'boolean',
                description: 'Enable two-factor authentication'
            },
            {
                key: 'security_session_timeout',
                value: '30',
                type: 'text',
                description: 'Session timeout in minutes'
            },
            {
                key: 'security_max_login_attempts',
                value: '5',
                type: 'text',
                description: 'Maximum login attempts before lockout'
            },
            {
                key: 'security_password_min_length',
                value: '8',
                type: 'text',
                description: 'Minimum password length'
            },
            {
                key: 'security_require_special_chars',
                value: 'true',
                type: 'boolean',
                description: 'Require special characters in passwords'
            },
            {
                key: 'security_ip_whitelist',
                value: '',
                type: 'text',
                description: 'IP whitelist for admin access'
            },
            {
                key: 'security_maintenance_mode',
                value: 'false',
                type: 'boolean',
                description: 'Enable maintenance mode'
            }
        ]

        // Combine all settings
        const allSettings = [...seoSettings, ...emailSettings, ...securitySettings]

        // Insert settings (ignore duplicates)
        for (const setting of allSettings) {
            try {
                await connection.execute(
                    'INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)',
                    [setting.key, setting.value, setting.type, setting.description]
                )
                console.log(`✓ Added setting: ${setting.key}`)
            } catch (error) {
                console.log(`⚠ Setting ${setting.key} already exists or error:`, error.message)
            }
        }

        console.log('\n✅ Default settings setup completed successfully!')
        console.log(`📊 Total settings processed: ${allSettings.length}`)

    } catch (error) {
        console.error('❌ Error setting up default settings:', error)
    } finally {
        await connection.end()
        console.log('Database connection closed')
    }
}

setupDefaultSettings()
