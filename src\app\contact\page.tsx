'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import { TbMail, TbPhone, TbMapPin, TbClock, TbMessageCircle, TbBrandWhatsapp, TbBrandTwitter, TbBrandLinkedin, TbBrandFacebook } from 'react-icons/tb'
import PageLayout from '@/components/layout/PageLayout'

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })

  const contactInfo = [
    {
      icon: TbMail,
      title: "Email Us",
      details: "<EMAIL>",
      description: "Send us an email and we'll respond within 2 hours",
      action: "mailto:<EMAIL>",
      color: "text-[#EA5455]",
      bgColor: "bg-[#EA5455]/10"
    },
    {
      icon: TbPhone,
      title: "Call Us",
      details: "+****************",
      description: "Speak with our experts Mon-Fri 9AM-6PM GMT",
      action: "tel:+15551234567",
      color: "text-blue-500",
      bgColor: "bg-blue-500/10"
    },
    {
      icon: TbBrandWhatsapp,
      title: "WhatsApp",
      details: "+****************",
      description: "Chat with us instantly on WhatsApp",
      action: "https://wa.me/15551234567",
      color: "text-green-500",
      bgColor: "bg-green-500/10"
    },
    {
      icon: TbMessageCircle,
      title: "Live Chat",
      details: "Available 24/7",
      description: "Get instant help from our support team",
      action: "#",
      color: "text-purple-500",
      bgColor: "bg-purple-500/10"
    }
  ]

  const offices = [
    {
      city: "London",
      address: "123 Financial District, London EC2V 8RF, UK",
      phone: "+44 20 7123 4567",
      email: "<EMAIL>"
    },
    {
      city: "New York",
      address: "456 Wall Street, New York, NY 10005, USA",
      phone: "****** 123 4567",
      email: "<EMAIL>"
    },
    {
      city: "Singapore",
      address: "789 Raffles Place, Singapore 048623",
      phone: "+65 6123 4567",
      email: "<EMAIL>"
    }
  ]

  const socialLinks = [
    { icon: TbBrandFacebook, name: "Facebook", url: "https://facebook.com/mybrokerforex", color: "text-blue-600" },
    { icon: TbBrandTwitter, name: "Twitter", url: "https://twitter.com/mybrokerforex", color: "text-blue-400" },
    { icon: TbBrandLinkedin, name: "LinkedIn", url: "https://linkedin.com/company/mybrokerforex", color: "text-blue-700" },
    { icon: TbBrandWhatsapp, name: "WhatsApp", url: "https://wa.me/15551234567", color: "text-green-500" }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
    // Reset form
    setFormData({ name: '', email: '', phone: '', subject: '', message: '' })
  }

  return (
    <div className="mt-20">
      <PageLayout>
        {/* Hero Section */}
        <section className="relative py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Contact <span className="text-[#EA5455]">Us</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Have questions about trading or need support? Our expert team is here to help you
                succeed in your forex trading journey.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Contact Methods */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Get In Touch</h2>
              <p className="text-xl text-muted-foreground">
                Choose your preferred way to reach out to our team.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {contactInfo.map((info, index) => {
                const IconComponent = info.icon
                return (
                  <motion.a
                    key={index}
                    href={info.action}
                    target={info.action.startsWith('http') ? '_blank' : '_self'}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white dark:bg-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 text-center group"
                  >
                    <div className={`w-16 h-16 ${info.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className={`h-8 w-8 ${info.color}`} />
                    </div>
                    <h3 className="text-xl font-bold mb-2">{info.title}</h3>
                    <p className={`font-semibold mb-2 ${info.color}`}>{info.details}</p>
                    <p className="text-sm text-muted-foreground">{info.description}</p>
                  </motion.a>
                )
              })}
            </div>
          </div>
        </section>

        {/* Contact Form */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Send Us a Message</h2>
              <p className="text-xl text-muted-foreground">
                Fill out the form below and we'll get back to you within 2 hours during business hours.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700 shadow-lg"
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-semibold mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700"
                      placeholder="Enter your email address"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-semibold mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700"
                      placeholder="Enter your phone number"
                    />
                  </div>
                  <div>
                    <label htmlFor="subject" className="block text-sm font-semibold mb-2">
                      Subject *
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700"
                      placeholder="What can we help you with?"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-semibold mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 resize-none"
                    placeholder="Please describe your question or message in detail..."
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-300"
                >
                  Send Message
                </button>
              </form>
            </motion.div>
          </div>
        </section>

        {/* Office Locations */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Our Global Offices</h2>
              <p className="text-xl text-muted-foreground">
                Visit us at one of our offices around the world or contact your local team.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {offices.map((office, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-[#EA5455]/10 rounded-full flex items-center justify-center">
                      <TbMapPin className="h-6 w-6 text-[#EA5455]" />
                    </div>
                    <h3 className="text-xl font-bold">{office.city}</h3>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-start gap-2">
                      <TbMapPin className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-muted-foreground">{office.address}</p>
                    </div>

                    <div className="flex items-center gap-2">
                      <TbPhone className="h-5 w-5 text-muted-foreground" />
                      <a href={`tel:${office.phone}`} className="text-sm text-[#EA5455] hover:text-[#EA5455]/80">
                        {office.phone}
                      </a>
                    </div>

                    <div className="flex items-center gap-2">
                      <TbMail className="h-5 w-5 text-muted-foreground" />
                      <a href={`mailto:${office.email}`} className="text-sm text-[#EA5455] hover:text-[#EA5455]/80">
                        {office.email}
                      </a>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Business Hours & Social */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Business Hours */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-12 h-12 bg-[#EA5455]/10 rounded-full flex items-center justify-center">
                    <TbClock className="h-6 w-6 text-[#EA5455]" />
                  </div>
                  <h3 className="text-2xl font-bold">Business Hours</h3>
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Customer Support</span>
                    <span className="text-muted-foreground">24/7</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Phone Support</span>
                    <span className="text-muted-foreground">Mon-Fri 9AM-6PM GMT</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Live Chat</span>
                    <span className="text-muted-foreground">24/7</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Email Response</span>
                    <span className="text-muted-foreground">Within 2 hours</span>
                  </div>
                </div>
              </motion.div>

              {/* Social Media */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700"
              >
                <h3 className="text-2xl font-bold mb-6">Follow Us</h3>
                <p className="text-muted-foreground mb-6">
                  Stay connected with us on social media for the latest updates, market insights, and trading tips.
                </p>

                <div className="grid grid-cols-2 gap-4">
                  {socialLinks.map((social, index) => {
                    const IconComponent = social.icon
                    return (
                      <a
                        key={index}
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-all duration-300 group"
                      >
                        <IconComponent className={`h-6 w-6 ${social.color} group-hover:scale-110 transition-transform duration-300`} />
                        <span className="font-medium">{social.name}</span>
                      </a>
                    )
                  })}
                </div>
              </motion.div>
            </div>
          </div>
        </section>
      </PageLayout>
    </div>
  )
}

export default ContactPage