'use client'

import { useState, useEffect } from 'react'

export interface HeroSection {
    id: number
    title: string
    subtitle?: string
    video_url?: string
    cta_link?: string
    cta_text?: string
    image?: string
    background_type: 'image' | 'video' | 'gradient'
    is_active: boolean
    created_at: string
    updated_at: string
}

interface UseHeroSectionsResult {
    heroSections: HeroSection[]
    loading: boolean
    error: string | null
    refetch: () => void
}

export const useHeroSections = (activeOnly: boolean = true): UseHeroSectionsResult => {
    const [heroSections, setHeroSections] = useState<HeroSection[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    const fetchHeroSections = async () => {
        try {
            setLoading(true)
            setError(null)
            
            const url = activeOnly 
                ? '/api/cms/hero-sections?active=true' 
                : '/api/cms/hero-sections'
            
            const response = await fetch(url)
            const data = await response.json()
            
            if (data.success) {
                setHeroSections(data.data)
            } else {
                setError(data.error || 'Failed to fetch hero sections')
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchHeroSections()
    }, [activeOnly])

    return {
        heroSections,
        loading,
        error,
        refetch: fetchHeroSections
    }
}
