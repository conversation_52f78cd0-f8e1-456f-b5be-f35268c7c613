'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbShield, TbTarget, TbCalculator, TbTrendingDown, TbAlertTriangle, TbChartLine, TbSettings, TbBrain } from 'react-icons/tb'

const RiskManagementPage = () => {
    const riskPrinciples = [
        {
            icon: TbShield,
            title: 'Capital Preservation',
            description: 'Protect your trading capital as the foundation of long-term success.',
            importance: 'Critical',
            techniques: ['Position Sizing', 'Stop Loss Orders', 'Risk Per Trade Limits', 'Account Drawdown Rules']
        },
        {
            icon: TbTarget,
            title: 'Risk-Reward Ratio',
            description: 'Maintain favorable risk-reward ratios to ensure profitability.',
            importance: 'High',
            techniques: ['1:2 Minimum Ratio', 'Profit Target Setting', 'Trade Selection', 'Exit Strategy Planning']
        },
        {
            icon: TbCalculator,
            title: 'Position Sizing',
            description: 'Calculate appropriate trade sizes based on account balance and risk tolerance.',
            importance: 'Critical',
            techniques: ['Fixed Percentage Method', 'Volatility-Based Sizing', 'Kelly Criterion', 'Risk Parity Approach']
        },
        {
            icon: TbTrendingDown,
            title: 'Drawdown Management',
            description: 'Control and limit account drawdowns to preserve trading psychology.',
            importance: 'High',
            techniques: ['Maximum Drawdown Limits', 'Recovery Strategies', 'Position Reduction', 'Trading Breaks']
        }
    ]

    const riskTools = [
        {
            title: 'Stop Loss Orders',
            description: 'Automatically close losing positions at predetermined levels.',
            icon: TbAlertTriangle,
            types: ['Fixed Stop Loss', 'Trailing Stop Loss', 'Volatility-Based Stops', 'Time-Based Stops']
        },
        {
            title: 'Position Size Calculator',
            description: 'Determine optimal trade size based on risk parameters.',
            icon: TbCalculator,
            types: ['Percentage Risk Model', 'Fixed Dollar Amount', 'Volatility Adjustment', 'Correlation Analysis']
        },
        {
            title: 'Risk-Reward Analysis',
            description: 'Evaluate potential profit versus potential loss before trading.',
            icon: TbChartLine,
            types: ['R-Multiple Analysis', 'Expectancy Calculation', 'Win Rate Assessment', 'Profit Factor Analysis']
        },
        {
            title: 'Portfolio Diversification',
            description: 'Spread risk across multiple currency pairs and strategies.',
            icon: TbSettings,
            types: ['Currency Correlation', 'Strategy Diversification', 'Time Frame Mixing', 'Market Condition Adaptation']
        }
    ]

    const psychologyFactors = [
        {
            title: 'Emotional Control',
            description: 'Maintain discipline and avoid emotional trading decisions.',
            impact: 'Critical'
        },
        {
            title: 'Fear of Missing Out (FOMO)',
            description: 'Resist the urge to chase trades or over-leverage positions.',
            impact: 'High'
        },
        {
            title: 'Overconfidence Bias',
            description: 'Avoid increasing risk after a series of winning trades.',
            impact: 'High'
        },
        {
            title: 'Loss Aversion',
            description: 'Accept losses as part of trading and stick to your plan.',
            impact: 'Medium'
        },
        {
            title: 'Revenge Trading',
            description: 'Never try to recover losses by taking excessive risks.',
            impact: 'Critical'
        },
        {
            title: 'Analysis Paralysis',
            description: 'Balance thorough analysis with timely decision-making.',
            impact: 'Medium'
        }
    ]

    const riskMetrics = [
        { metric: 'Maximum Risk Per Trade', recommended: '1-2%', description: 'Never risk more than 1-2% of account balance on a single trade' },
        { metric: 'Maximum Daily Loss', recommended: '5%', description: 'Stop trading if daily losses exceed 5% of account balance' },
        { metric: 'Maximum Drawdown', recommended: '10-15%', description: 'Reduce position sizes if account drawdown reaches this level' },
        { metric: 'Risk-Reward Ratio', recommended: '1:2 minimum', description: 'Target at least 2x profit for every 1x risk taken' },
        { metric: 'Win Rate Requirement', recommended: '40%+', description: 'Maintain minimum win rate to ensure profitability with 1:2 RR' },
        { metric: 'Correlation Limit', recommended: '<0.7', description: 'Avoid highly correlated positions to prevent concentrated risk' }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Risk Management
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Protect your trading capital and ensure long-term success with proven risk management strategies
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-colors duration-300"
                                >
                                    Start Learning Now
                                </button>
                            </motion.div>
                        </div>
                    </section>

                    {/* Risk Principles Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Core Risk Management Principles
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Master these fundamental principles to protect your capital and achieve consistent trading results.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {riskPrinciples.map((principle, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <principle.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between mb-3">
                                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                                                        {principle.title}
                                                    </h3>
                                                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                                        principle.importance === 'Critical' 
                                                            ? 'bg-[#EA5455]/10 text-[#EA5455]' 
                                                            : 'bg-[#28C76F]/10 text-[#28C76F]'
                                                    }`}>
                                                        {principle.importance}
                                                    </span>
                                                </div>
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {principle.description}
                                                </p>
                                                <div className="space-y-2">
                                                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Key Techniques:</h4>
                                                    <div className="flex flex-wrap gap-2">
                                                        {principle.techniques.map((technique, techIndex) => (
                                                            <span key={techIndex} className="px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-xs">
                                                                {technique}
                                                            </span>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Risk Tools Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Essential Risk Management Tools
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Utilize these powerful tools to implement effective risk management in your trading.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {riskTools.map((tool, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#28C76F]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <tool.icon className="w-6 h-6 text-[#28C76F]" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                                    {tool.title}
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {tool.description}
                                                </p>
                                                <div className="space-y-2">
                                                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Types:</h4>
                                                    <div className="grid grid-cols-2 gap-2">
                                                        {tool.types.map((type, typeIndex) => (
                                                            <span key={typeIndex} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs">
                                                                {type}
                                                            </span>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Risk Metrics Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Key Risk Metrics & Guidelines
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Follow these industry-standard risk metrics to maintain disciplined trading practices.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {riskMetrics.map((metric, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                                            {metric.metric}
                                        </h3>
                                        <div className="text-2xl font-bold text-[#EA5455] mb-3">
                                            {metric.recommended}
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {metric.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Psychology Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Trading Psychology & Risk
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Understand and overcome psychological biases that can lead to poor risk management decisions.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {psychologyFactors.map((factor, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start justify-between mb-4">
                                            <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                                                {factor.title}
                                            </h3>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                factor.impact === 'Critical'
                                                    ? 'bg-[#EA5455]/10 text-[#EA5455]'
                                                    : factor.impact === 'High'
                                                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                                                    : 'bg-[#28C76F]/10 text-[#28C76F]'
                                            }`}>
                                                {factor.impact}
                                            </span>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {factor.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#28C76F] to-[#28C76F]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Practice Risk Management Today
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Apply these risk management principles in a safe environment before risking real capital.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={() => window.open('/education/demo-account', '_blank')}
                                        className="bg-white text-[#28C76F] px-5 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Try Demo Account
                                    </button>
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#28C76F] transition-colors duration-300"
                                    >
                                        Open Live Account
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default RiskManagementPage
