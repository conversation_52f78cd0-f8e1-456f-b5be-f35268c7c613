'use client'

import Navigation from '@/app/landing/components/NavigationBar'
import LandingFooter from '@/app/landing/components/LandingFooter'
import useTheme from '@/utils/hooks/useTheme'
import { MODE_DARK, MODE_LIGHT } from '@/constants/theme.constant'

interface PageLayoutProps {
    children: React.ReactNode
}

const PageLayout = ({ children }: PageLayoutProps) => {
    const { mode, setMode } = useTheme((state) => ({
        mode: state.mode,
        setMode: state.setMode
    }))

    const toggleMode = () => {
        setMode(mode === MODE_LIGHT ? MODE_DARK : MODE_LIGHT)
    }

    return (
        <div className="min-h-screen bg-white dark:bg-gray-900">
            <Navigation toggleMode={toggleMode} mode={mode} />
            <div className="relative">
                <div
                    style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='50' height='50' fill='none' stroke='${mode === MODE_LIGHT ? 'rgb(0 0 0 / 0.04)' : 'rgb(255 255 255 / 0.04)'}'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e")`,
                    }}
                    className="absolute inset-0 z-0"
                />
                <div className="relative z-10">
                    {children}
                </div>
            </div>
            <LandingFooter mode={mode} />
        </div>
    )
}

export default PageLayout
