'use client'

import { Card } from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'
import { TrendingUpIcon, TrendingDownIcon, UsersIcon, MousePointerClickIcon, EyeIcon, ClockIcon } from 'lucide-react'

interface AnalyticsData {
    thisMonth: {
        metrics: {
            visitors: { value: number; growShrink: number }
            conversionRate: { value: number; growShrink: number }
            adCampaignClicks: { value: number; growShrink: number }
        }
        webAnalytic: {
            pageView: { value: number; growShrink: number }
            avgTimeOnPage: { value: string; growShrink: number }
            series: Array<{ name: string; data: number[] }>
            date: string[]
        }
        topChannel: {
            visitors: number
            channels: Array<{
                id: string
                name: string
                img: string
                total: number
                percentage: number
            }>
        }
        deviceSession: {
            labels: string[]
            series: number[]
            percentage: number[]
        }
    }
}

interface AnalyticsDashboardProps {
    data: AnalyticsData
}

const MetricCard = ({ 
    title, 
    value, 
    growShrink, 
    icon: Icon, 
    format = 'number' 
}: { 
    title: string
    value: number | string
    growShrink: number
    icon: any
    format?: 'number' | 'percentage' | 'string'
}) => {
    const formatValue = (val: number | string) => {
        if (format === 'percentage') return `${val}%`
        if (format === 'number' && typeof val === 'number') {
            return val.toLocaleString()
        }
        return val
    }

    return (
        <Card className="p-6">
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm text-muted-foreground">{title}</p>
                    <p className="text-2xl font-bold">{formatValue(value)}</p>
                    <div className="flex items-center mt-2">
                        {growShrink > 0 ? (
                            <TrendingUpIcon className="h-4 w-4 text-green-600 mr-1" />
                        ) : (
                            <TrendingDownIcon className="h-4 w-4 text-red-600 mr-1" />
                        )}
                        <span className={`text-sm ${growShrink > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {Math.abs(growShrink)}%
                        </span>
                    </div>
                </div>
                <div className="p-3 bg-primary/10 rounded-lg">
                    <Icon className="h-6 w-6 text-primary" />
                </div>
            </div>
        </Card>
    )
}

const AnalyticsDashboard = ({ data }: AnalyticsDashboardProps) => {
    const { thisMonth } = data

    return (
        <div className="space-y-6">
            {/* Header */}
            <div>
                <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
                <p className="text-muted-foreground">Monitor your website performance and user engagement</p>
            </div>

            {/* Metrics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <MetricCard
                    title="Total Visitors"
                    value={thisMonth.metrics.visitors.value}
                    growShrink={thisMonth.metrics.visitors.growShrink}
                    icon={UsersIcon}
                />
                <MetricCard
                    title="Conversion Rate"
                    value={thisMonth.metrics.conversionRate.value}
                    growShrink={thisMonth.metrics.conversionRate.growShrink}
                    icon={TrendingUpIcon}
                    format="percentage"
                />
                <MetricCard
                    title="Ad Campaign Clicks"
                    value={thisMonth.metrics.adCampaignClicks.value}
                    growShrink={thisMonth.metrics.adCampaignClicks.growShrink}
                    icon={MousePointerClickIcon}
                />
                <MetricCard
                    title="Page Views"
                    value={thisMonth.webAnalytic.pageView.value}
                    growShrink={thisMonth.webAnalytic.pageView.growShrink}
                    icon={EyeIcon}
                />
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Web Analytics Chart */}
                <Card 
                    header={{ content: 'Web Analytics' }}
                    className="p-6"
                >
                    <Chart
                        type="area"
                        height={300}
                        series={thisMonth.webAnalytic.series}
                        xAxis={thisMonth.webAnalytic.date}
                    />
                </Card>

                {/* Device Sessions */}
                <Card 
                    header={{ content: 'Device Sessions' }}
                    className="p-6"
                >
                    <Chart
                        type="donut"
                        height={300}
                        series={thisMonth.deviceSession.series}
                        customOptions={{
                            labels: thisMonth.deviceSession.labels,
                            legend: {
                                position: 'bottom'
                            }
                        }}
                    />
                </Card>
            </div>

            {/* Top Channels */}
            <Card 
                header={{ content: 'Top Traffic Channels' }}
                className="p-6"
            >
                <div className="space-y-4">
                    {thisMonth.topChannel.channels.map((channel) => (
                        <div key={channel.id} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="flex items-center gap-3">
                                <img src={channel.img} alt={channel.name} className="w-8 h-8 rounded" />
                                <span className="font-medium">{channel.name}</span>
                            </div>
                            <div className="text-right">
                                <p className="font-semibold">{channel.total.toLocaleString()}</p>
                                <p className="text-sm text-muted-foreground">{channel.percentage}%</p>
                            </div>
                        </div>
                    ))}
                </div>
            </Card>
        </div>
    )
}

export default AnalyticsDashboard
