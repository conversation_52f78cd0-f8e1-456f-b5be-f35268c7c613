'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbNews, TbTrendingUp, TbClock, TbGlobe, TbChartLine, TbTarget, TbAlertTriangle, TbBrain } from 'react-icons/tb'

const MarketNewsPage = () => {
    const newsFeatures = [
        {
            icon: TbNews,
            title: 'Real-Time Updates',
            description: 'Get instant access to breaking market news and analysis.',
            benefits: ['Live news feeds', 'Breaking alerts', 'Market updates', 'Expert analysis']
        },
        {
            icon: TbChartLine,
            title: 'Market Impact Analysis',
            description: 'Understand how news events affect currency movements.',
            benefits: ['Impact assessment', 'Price predictions', 'Volatility analysis', 'Trading opportunities']
        },
        {
            icon: TbGlobe,
            title: 'Global Coverage',
            description: 'Comprehensive coverage of worldwide financial markets.',
            benefits: ['Multi-region news', 'Cross-market analysis', 'Global perspectives', 'Regional insights']
        },
        {
            icon: TbBrain,
            title: 'Expert Commentary',
            description: 'Professional insights and analysis from market experts.',
            benefits: ['Expert opinions', 'Market forecasts', 'Trading strategies', 'Educational content']
        }
    ]

    const newsCategories = [
        {
            title: 'Central Bank News',
            description: 'Policy decisions and statements from major central banks.',
            icon: TbTrendingUp,
            impact: 'High'
        },
        {
            title: 'Economic Data',
            description: 'Key economic indicators and data releases.',
            icon: TbChartLine,
            impact: 'Medium'
        },
        {
            title: 'Geopolitical Events',
            description: 'Political developments affecting financial markets.',
            icon: TbGlobe,
            impact: 'Variable'
        },
        {
            title: 'Market Analysis',
            description: 'Technical and fundamental analysis from experts.',
            icon: TbTarget,
            impact: 'Low'
        }
    ]

    const latestNews = [
        {
            title: 'Federal Reserve Signals Potential Rate Cuts',
            summary: 'Fed officials hint at possible monetary policy easing in upcoming meetings.',
            time: '2 hours ago',
            impact: 'High',
            category: 'Central Bank',
            readTime: '3 min read'
        },
        {
            title: 'EUR/USD Reaches Monthly High on ECB Comments',
            summary: 'European Central Bank dovish stance pushes euro higher against dollar.',
            time: '4 hours ago',
            impact: 'Medium',
            category: 'Currency',
            readTime: '2 min read'
        },
        {
            title: 'Gold Prices Surge on Safe Haven Demand',
            summary: 'Precious metals rally as investors seek safety amid market uncertainty.',
            time: '6 hours ago',
            impact: 'Medium',
            category: 'Commodities',
            readTime: '4 min read'
        },
        {
            title: 'UK GDP Data Shows Stronger Than Expected Growth',
            summary: 'British economy outperforms forecasts, supporting pound strength.',
            time: '8 hours ago',
            impact: 'High',
            category: 'Economic Data',
            readTime: '3 min read'
        }
    ]

    const tradingTips = [
        {
            tip: 'Stay Informed',
            description: 'Follow market news regularly to understand current trends and sentiment.',
            importance: 'Critical'
        },
        {
            tip: 'Verify Sources',
            description: 'Always verify news from multiple reliable sources before making trading decisions.',
            importance: 'High'
        },
        {
            tip: 'Understand Context',
            description: 'Consider the broader market context when interpreting news events.',
            importance: 'High'
        },
        {
            tip: 'React Quickly',
            description: 'Markets can move fast on news, so be prepared to act quickly when opportunities arise.',
            importance: 'Medium'
        },
        {
            tip: 'Manage Risk',
            description: 'Use proper risk management when trading on news events.',
            importance: 'Critical'
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Market News & Analysis
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-100 mb-8 max-w-3xl mx-auto">
                                    Stay informed with real-time market news, expert analysis, and insights that drive trading decisions
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-colors duration-300"
                                    >
                                        Access News Feed
                                    </button>
                                    <button 
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#1E1E1E] transition-colors duration-300"
                                    >
                                        View Latest News
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* News Features Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    News & Analysis Features
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Access comprehensive market coverage with real-time updates and expert insights.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {newsFeatures.map((feature, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <feature.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                                    {feature.title}
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {feature.description}
                                                </p>
                                                <ul className="space-y-2">
                                                    {feature.benefits.map((benefit, benefitIndex) => (
                                                        <li key={benefitIndex} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                            <div className="w-1.5 h-1.5 bg-[#EA5455] rounded-full"></div>
                                                            <span>{benefit}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* News Categories Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    News Categories
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Stay informed across different market sectors and news types.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                {newsCategories.map((category, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="w-12 h-12 bg-[#28C76F]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                                            <category.icon className="w-6 h-6 text-[#28C76F]" />
                                        </div>
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                            {category.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                            {category.description}
                                        </p>
                                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                            category.impact === 'High' 
                                                ? 'bg-[#EA5455]/10 text-[#EA5455]' 
                                                : category.impact === 'Medium'
                                                ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                                                : category.impact === 'Variable'
                                                ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400'
                                                : 'bg-[#28C76F]/10 text-[#28C76F]'
                                        }`}>
                                            {category.impact} Impact
                                        </span>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Latest News Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Latest Market News
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Stay updated with the most recent market developments and analysis.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {latestNews.map((news, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300 cursor-pointer"
                                    >
                                        <div className="flex items-start justify-between mb-4">
                                            <span className="px-3 py-1 bg-[#EA5455]/10 text-[#EA5455] rounded-full text-xs font-medium">
                                                {news.category}
                                            </span>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                news.impact === 'High' 
                                                    ? 'bg-[#EA5455]/10 text-[#EA5455]' 
                                                    : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                                            }`}>
                                                {news.impact}
                                            </span>
                                        </div>
                                        
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                            {news.title}
                                        </h3>
                                        
                                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                                            {news.summary}
                                        </p>
                                        
                                        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                                            <div className="flex items-center space-x-1">
                                                <TbClock className="w-4 h-4" />
                                                <span>{news.time}</span>
                                            </div>
                                            <span>{news.readTime}</span>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#EA5455] to-[#EA5455]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Stay Ahead with Market Intelligence
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Access real-time market news and expert analysis to make informed trading decisions.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-white text-[#EA5455] px-5 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Access News Feed
                                    </button>
                                    <button 
                                        onClick={() => window.open('/education/economic-calendar', '_blank')}
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#EA5455] transition-colors duration-300"
                                    >
                                        View Economic Calendar
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default MarketNewsPage
