'use client'

import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import Navigation from './NavigationBar'
import HeroContent from './HeroContent'
import TradingWidgets from './TradingWidgets'
import HydrationBoundary from '@/components/shared/HydrationBoundary'
import ThemeSafeWrapper from '@/components/shared/ThemeSafeWrapper'
import YouTubeVideoBackground from '@/components/shared/YouTubeVideoBackground'
import useTheme from '@/utils/hooks/useTheme'
import { MODE_DARK, MODE_LIGHT } from '@/constants/theme.constant'

// Dynamic imports for heavy components
const LiveCurrencyTicker = dynamic(() => import('@/components/shared/LiveCurrencyTicker'), {
    ssr: false,
    loading: () => <div className="h-24 bg-gray-100 dark:bg-gray-800 animate-pulse" />
})

const Features = dynamic(() => import('./Features'), {
    loading: () => <div className="h-96 bg-gray-100 dark:bg-gray-800 animate-pulse" />
})

const TestimonialsSection = dynamic(() => import('./TestimonialsSection'), {
    loading: () => <div className="h-96 bg-gray-100 dark:bg-gray-800 animate-pulse" />
})

const PricingSection = dynamic(() => import('./PricingSection'), {
    loading: () => <div className="h-96 bg-gray-100 dark:bg-gray-800 animate-pulse" />
})



const LandingFooter = dynamic(() => import('./LandingFooter'), {
    loading: () => <div className="h-96 bg-gray-100 dark:bg-gray-800 animate-pulse" />
})

const Landing = () => {
    // React 19 hydration-safe state management
    const [isClient, setIsClient] = useState(false)

    const { mode, setMode } = useTheme((state) => ({
        mode: state.mode,
        setMode: state.setMode
    }))

    // Hydration-safe client detection
    useEffect(() => {
        setIsClient(true)
    }, [])

    const toggleMode = () => {
        if (!isClient) return // Prevent server-side execution

        const newMode = mode === MODE_LIGHT ? MODE_DARK : MODE_LIGHT

        // Use React 19's improved state batching
        setMode(newMode)
    }

    return (
        <ThemeSafeWrapper className="px-4 lg:px-0 text-base bg-white dark:bg-gray-900">
            <main suppressHydrationWarning>
                <Navigation toggleMode={toggleMode} mode={mode} />

                <div className="relative">
                    {/* YouTube Video Background */}
                    <YouTubeVideoBackground
                        videoId="zS0459y8ZDs"
                        className="z-0"
                    />

                    {/* Static background pattern overlay - reduced opacity for video visibility */}
                    <div
                        className="absolute inset-0 opacity-[0.02] dark:opacity-[0.02] [mask-image:linear-gradient(to_bottom,white_5%,transparent_70%)] pointer-events-none select-none z-10"
                        style={{
                            backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='50' height='50' fill='none' stroke='currentColor'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e")`,
                        }}
                    />

                    {/* Hero Content with higher z-index */}
                    <div className="relative z-20">
                        <HeroContent mode={mode} />
                    </div>
                </div>

                {/* Trading Widgets positioned after video section with overlap */}
                <TradingWidgets mode={mode} />

                <HydrationBoundary
                    fallback={<div className="h-24 bg-gray-100 dark:bg-gray-800 animate-pulse" />}
                    suppressHydrationWarning
                >
                    <LiveCurrencyTicker />
                </HydrationBoundary>

                <Features
                    mode={mode}
                    onModeChange={(value) => setMode(value ? 'dark' : 'light')}
                />

                <TestimonialsSection mode={mode} />
                <PricingSection mode={mode} />



                <HydrationBoundary
                    fallback={<div className="h-96 bg-gray-100 dark:bg-gray-800 animate-pulse" />}
                    suppressHydrationWarning
                >
                    <LandingFooter mode={mode} />
                </HydrationBoundary>
            </main>
        </ThemeSafeWrapper>
    )
}

export default Landing
