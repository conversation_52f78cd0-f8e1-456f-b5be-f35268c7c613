'use client'

import { motion } from 'framer-motion'
import { TbChartBar, TbDownload, TbCalendar, TbTrendingUp, TbUsers, TbCash } from 'react-icons/tb'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateReportsPage = () => {
    const reportTypes = [
        {
            icon: TbChartBar,
            title: 'Performance Reports',
            description: 'Comprehensive overview of your affiliate performance metrics.',
            features: ['Click-through rates', 'Conversion analytics', 'Revenue tracking', 'ROI calculations']
        },
        {
            icon: TbUsers,
            title: 'Client Reports',
            description: 'Detailed information about your referred clients and their activity.',
            features: ['Client registration data', 'Trading activity', 'Account status', 'Lifetime value']
        },
        {
            icon: TbCash,
            title: 'Commission Reports',
            description: 'Track your earnings and commission payments over time.',
            features: ['Commission breakdown', 'Payment history', 'Pending payments', 'Tax documentation']
        },
        {
            icon: TbTrendingUp,
            title: 'Trend Analysis',
            description: 'Analyze trends and patterns in your affiliate marketing performance.',
            features: ['Monthly trends', 'Seasonal patterns', 'Growth analysis', 'Forecasting']
        }
    ]

    const sampleReports = [
        {
            name: 'Monthly Performance Report',
            period: 'December 2023',
            type: 'Performance',
            status: 'Ready',
            size: '2.3 MB'
        },
        {
            name: 'Client Activity Report',
            period: 'Q4 2023',
            type: 'Client',
            status: 'Ready',
            size: '1.8 MB'
        },
        {
            name: 'Commission Statement',
            period: 'December 2023',
            type: 'Commission',
            status: 'Ready',
            size: '856 KB'
        },
        {
            name: 'Annual Summary Report',
            period: '2023',
            type: 'Summary',
            status: 'Processing',
            size: 'N/A'
        }
    ]

    const keyMetrics = [
        { label: 'Total Referrals', value: '1,247', change: '+18.5%', color: 'text-blue-500' },
        { label: 'Active Clients', value: '892', change: '+12.3%', color: 'text-[#28C76F]' },
        { label: 'Monthly Revenue', value: '$15,750', change: '+25.7%', color: 'text-[#EA5455]' },
        { label: 'Conversion Rate', value: '2.4%', change: '+0.6%', color: 'text-orange-500' }
    ]

    const reportFeatures = [
        {
            icon: TbDownload,
            title: 'Multiple Formats',
            description: 'Download reports in PDF, Excel, or CSV formats for easy analysis.'
        },
        {
            icon: TbCalendar,
            title: 'Custom Date Ranges',
            description: 'Generate reports for any date range to match your business needs.'
        },
        {
            icon: TbChartBar,
            title: 'Visual Analytics',
            description: 'Interactive charts and graphs to visualize your performance data.'
        }
    ]

    return (
        <PageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl text-gray-300 mt-8 md:text-6xl font-bold mb-6">
                                Performance Reports
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Comprehensive reporting and analytics for your affiliate performance
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* Key Metrics */}
                <section className="py-16 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-12"
                        >
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                Your Performance Overview
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                            {keyMetrics.map((metric, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center"
                                >
                                    <div className={`text-3xl font-bold mb-2 ${metric.color}`}>
                                        {metric.value}
                                    </div>
                                    <div className="text-gray-600 dark:text-gray-300 mb-2">
                                        {metric.label}
                                    </div>
                                    <div className="text-[#28C76F] text-sm font-medium">
                                        {metric.change}
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Report Types */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Available Reports
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Detailed reports to help you understand and optimize your affiliate performance
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {reportTypes.map((report, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center mb-6">
                                        <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                            <report.icon className="w-6 h-6 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {report.title}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {report.description}
                                    </p>
                                    <ul className="space-y-2">
                                        {report.features.map((feature, featureIndex) => (
                                            <li key={featureIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                <TbChartBar className="w-4 h-4 text-[#28C76F] mr-2" />
                                                {feature}
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Available Reports */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Recent Reports
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Download your latest performance reports and analytics
                            </p>
                        </motion.div>

                        <div className="overflow-x-auto">
                            <table className="w-full bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden">
                                <thead className="bg-gray-100 dark:bg-gray-600">
                                    <tr>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Report Name</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Period</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Type</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Status</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Size</th>
                                        <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {sampleReports.map((report, index) => (
                                        <motion.tr
                                            key={index}
                                            initial={{ opacity: 0, y: 20 }}
                                            whileInView={{ opacity: 1, y: 0 }}
                                            transition={{ duration: 0.4, delay: index * 0.1 }}
                                            className="border-b border-gray-200 dark:border-gray-600"
                                        >
                                            <td className="px-6 py-4 font-semibold text-gray-900 dark:text-white">
                                                {report.name}
                                            </td>
                                            <td className="px-6 py-4 text-gray-600 dark:text-gray-300">
                                                {report.period}
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                    {report.type}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                                    report.status === 'Ready' 
                                                        ? 'bg-[#28C76F] text-white' 
                                                        : 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                                                }`}>
                                                    {report.status}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 text-gray-600 dark:text-gray-300">
                                                {report.size}
                                            </td>
                                            <td className="px-6 py-4">
                                                {report.status === 'Ready' ? (
                                                    <button className="flex items-center text-[#EA5455] hover:text-[#d63384] transition-colors duration-200">
                                                        <TbDownload className="w-4 h-4 mr-1" />
                                                        Download
                                                    </button>
                                                ) : (
                                                    <span className="text-gray-400">Processing...</span>
                                                )}
                                            </td>
                                        </motion.tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                {/* Report Features */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Report Features
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {reportFeatures.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455]/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <feature.icon className="w-8 h-8 text-[#EA5455]" />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Custom Report Request */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-4xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-gradient-to-r from-[#EA5455] to-[#d63384] rounded-2xl p-8 text-white"
                        >
                            <h2 className="text-3xl font-bold mb-4">
                                Need a Custom Report?
                            </h2>
                            <p className="text-xl text-white/90 mb-8">
                                Contact our team to request custom reports tailored to your specific needs
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button 
                                    onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                                    className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-colors duration-200"
                                >
                                    Request Custom Report
                                </button>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/login', '_blank')}
                                    className="bg-white/20 text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/30 transition-colors duration-200"
                                >
                                    Access Dashboard
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-[#EA5455]">
                    <div className="max-w-7xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                Start Tracking Your Performance
                            </h2>
                            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                Join our affiliate program and get access to comprehensive reporting tools
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                Join Affiliate Program
                            </button>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PageLayout>
    )
}

export default AffiliateReportsPage
