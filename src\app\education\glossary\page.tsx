'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbBook, TbSearch, TbChevronDown, TbChevronUp } from 'react-icons/tb'

const GlossaryPage = () => {
    const [searchTerm, setSearchTerm] = useState('')
    const [selectedCategory, setSelectedCategory] = useState('All')
    const [expandedTerm, setExpandedTerm] = useState<string | null>(null)

    const categories = ['All', 'Basic Terms', 'Technical Analysis', 'Fundamental Analysis', 'Risk Management', 'Orders & Execution', 'Market Structure']

    const glossaryTerms = [
        {
            term: 'Ask Price',
            definition: 'The price at which a currency pair is offered for sale. Also known as the offer price.',
            category: 'Basic Terms',
            example: 'If EUR/USD has an ask price of 1.1850, you can buy euros at this rate.'
        },
        {
            term: 'Bid Price',
            definition: 'The price at which a currency pair can be sold. The highest price a buyer is willing to pay.',
            category: 'Basic Terms',
            example: 'If EUR/USD has a bid price of 1.1848, you can sell euros at this rate.'
        },
        {
            term: 'Spread',
            definition: 'The difference between the bid and ask price of a currency pair.',
            category: 'Basic Terms',
            example: 'If EUR/USD bid is 1.1848 and ask is 1.1850, the spread is 2 pips.'
        },
        {
            term: 'Pip',
            definition: 'The smallest price move in a currency pair, typically the fourth decimal place.',
            category: 'Basic Terms',
            example: 'If EUR/USD moves from 1.1850 to 1.1851, it has moved 1 pip.'
        },
        {
            term: 'Leverage',
            definition: 'The ability to control a large position with a relatively small amount of capital.',
            category: 'Risk Management',
            example: 'With 1:100 leverage, you can control $100,000 with just $1,000 margin.'
        },
        {
            term: 'Margin',
            definition: 'The required deposit to open a leveraged trading position.',
            category: 'Risk Management',
            example: 'To open a $100,000 position with 1:100 leverage requires $1,000 margin.'
        },
        {
            term: 'Stop Loss',
            definition: 'An order to close a position at a predetermined loss level to limit risk.',
            category: 'Orders & Execution',
            example: 'Setting a stop loss at 1.1800 when buying EUR/USD at 1.1850 limits loss to 50 pips.'
        },
        {
            term: 'Take Profit',
            definition: 'An order to close a position at a predetermined profit level.',
            category: 'Orders & Execution',
            example: 'Setting take profit at 1.1900 when buying EUR/USD at 1.1850 targets 50 pips profit.'
        },
        {
            term: 'Support',
            definition: 'A price level where buying interest is strong enough to prevent further decline.',
            category: 'Technical Analysis',
            example: 'EUR/USD has strong support at 1.1800, where it has bounced multiple times.'
        },
        {
            term: 'Resistance',
            definition: 'A price level where selling interest is strong enough to prevent further advance.',
            category: 'Technical Analysis',
            example: 'EUR/USD faces resistance at 1.1900, struggling to break above this level.'
        },
        {
            term: 'Bull Market',
            definition: 'A market characterized by rising prices and optimistic sentiment.',
            category: 'Market Structure',
            example: 'The USD has been in a bull market against the EUR for several months.'
        },
        {
            term: 'Bear Market',
            definition: 'A market characterized by falling prices and pessimistic sentiment.',
            category: 'Market Structure',
            example: 'Gold entered a bear market after reaching its peak last year.'
        },
        {
            term: 'Lot',
            definition: 'The standard trading unit in forex, typically 100,000 units of the base currency.',
            category: 'Basic Terms',
            example: 'Trading 1 lot of EUR/USD means controlling 100,000 euros.'
        },
        {
            term: 'Currency Pair',
            definition: 'Two currencies quoted against each other, showing the exchange rate.',
            category: 'Basic Terms',
            example: 'EUR/USD is a currency pair showing how many US dollars equal one euro.'
        },
        {
            term: 'Base Currency',
            definition: 'The first currency in a currency pair, which is being bought or sold.',
            category: 'Basic Terms',
            example: 'In EUR/USD, EUR is the base currency.'
        },
        {
            term: 'Quote Currency',
            definition: 'The second currency in a currency pair, used to quote the price.',
            category: 'Basic Terms',
            example: 'In EUR/USD, USD is the quote currency.'
        },
        {
            term: 'Moving Average',
            definition: 'A technical indicator that smooths price data by creating a constantly updated average price.',
            category: 'Technical Analysis',
            example: 'The 50-day moving average shows the average closing price over the last 50 days.'
        },
        {
            term: 'RSI',
            definition: 'Relative Strength Index - a momentum oscillator measuring speed and change of price movements.',
            category: 'Technical Analysis',
            example: 'RSI above 70 suggests overbought conditions, below 30 suggests oversold.'
        },
        {
            term: 'GDP',
            definition: 'Gross Domestic Product - the total value of goods and services produced by a country.',
            category: 'Fundamental Analysis',
            example: 'Strong GDP growth typically strengthens a country\'s currency.'
        },
        {
            term: 'Interest Rate',
            definition: 'The cost of borrowing money, set by central banks to control monetary policy.',
            category: 'Fundamental Analysis',
            example: 'Higher interest rates typically attract foreign investment and strengthen currency.'
        },
        {
            term: 'Slippage',
            definition: 'The difference between expected and actual execution price of a trade.',
            category: 'Orders & Execution',
            example: 'During high volatility, you might experience slippage when your order fills at a different price.'
        },
        {
            term: 'Drawdown',
            definition: 'The peak-to-trough decline in account value during a specific period.',
            category: 'Risk Management',
            example: 'A 10% drawdown means your account fell 10% from its highest point.'
        }
    ]

    const filteredTerms = glossaryTerms.filter(term => {
        const matchesSearch = term.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            term.definition.toLowerCase().includes(searchTerm.toLowerCase())
        const matchesCategory = selectedCategory === 'All' || term.category === selectedCategory
        return matchesSearch && matchesCategory
    })

    const toggleExpanded = (term: string) => {
        setExpandedTerm(expandedTerm === term ? null : term)
    }

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Trading Glossary
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-100 mb-8 max-w-3xl mx-auto">
                                    Comprehensive dictionary of forex trading terms and definitions
                                </p>
                                <div className="max-w-md mx-auto">
                                    <div className="relative">
                                        <TbSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                        <input
                                            type="text"
                                            placeholder="Search terms..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="w-full pl-10 pr-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-[#EA5455]"
                                        />
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Filter Section */}
                    <section className="py-8 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                        <div className="max-w-7xl mx-auto px-6">
                            <div className="flex flex-wrap gap-2 justify-center">
                                {categories.map((category) => (
                                    <button
                                        key={category}
                                        onClick={() => setSelectedCategory(category)}
                                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                                            selectedCategory === category
                                                ? 'bg-[#EA5455] text-white'
                                                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                        }`}
                                    >
                                        {category}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Glossary Terms Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="mb-8"
                            >
                                <p className="text-center text-gray-600 dark:text-gray-400">
                                    Showing {filteredTerms.length} terms
                                    {selectedCategory !== 'All' && ` in ${selectedCategory}`}
                                    {searchTerm && ` matching "${searchTerm}"`}
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {filteredTerms.map((item, index) => (
                                    <motion.div
                                        key={item.term}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.05 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start justify-between mb-3">
                                            <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                                                {item.term}
                                            </h3>
                                            <span className="px-2 py-1 bg-[#EA5455]/10 text-[#EA5455] rounded text-xs font-medium">
                                                {item.category}
                                            </span>
                                        </div>
                                        
                                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                                            {item.definition}
                                        </p>
                                        
                                        <button
                                            onClick={() => toggleExpanded(item.term)}
                                            className="flex items-center space-x-2 text-[#EA5455] hover:text-[#EA5455]/80 transition-colors duration-200"
                                        >
                                            <span className="text-sm font-medium">
                                                {expandedTerm === item.term ? 'Hide Example' : 'Show Example'}
                                            </span>
                                            {expandedTerm === item.term ? 
                                                <TbChevronUp className="w-4 h-4" /> : 
                                                <TbChevronDown className="w-4 h-4" />
                                            }
                                        </button>
                                        
                                        {expandedTerm === item.term && (
                                            <motion.div
                                                initial={{ opacity: 0, height: 0 }}
                                                animate={{ opacity: 1, height: 'auto' }}
                                                exit={{ opacity: 0, height: 0 }}
                                                className="mt-3 p-3 bg-[#28C76F]/5 border-l-4 border-[#28C76F] rounded"
                                            >
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    <strong>Example:</strong> {item.example}
                                                </p>
                                            </motion.div>
                                        )}
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#EA5455] to-[#EA5455]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Ready to Apply Your Knowledge?
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Now that you understand the terminology, start your trading journey with confidence.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={() => window.open('/education/demo-account', '_blank')}
                                        className="bg-white text-[#EA5455] px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Try Demo Account
                                    </button>
                                    <button
                                        onClick={() => window.open('/education/trading-basics', '_blank')}
                                        className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-[#EA5455] transition-colors duration-300"
                                    >
                                        Learn Trading Basics
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default GlossaryPage
