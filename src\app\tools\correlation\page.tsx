'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { HiArrowLeft, HiSquares2X2 } from 'react-icons/hi2'
import { useRouter } from 'next/navigation'

interface CorrelationData {
    pair1: string
    pair2: string
    correlation: number
    strength: 'Very Strong' | 'Strong' | 'Moderate' | 'Weak' | 'Very Weak'
    direction: 'Positive' | 'Negative'
}

const CorrelationMatrixPage = () => {
    const router = useRouter()
    const [selectedTimeframe, setSelectedTimeframe] = useState('daily')
    const [correlationMatrix, setCorrelationMatrix] = useState<number[][]>([])
    const [currencyPairs] = useState(['EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 'AUD/USD', 'USD/CAD', 'NZD/USD', 'EUR/GBP'])
    const [isLoading, setIsLoading] = useState(true)

    const timeframes = [
        { id: 'daily', name: 'Daily', description: '24-hour correlation' },
        { id: 'weekly', name: 'Weekly', description: '7-day correlation' },
        { id: 'monthly', name: 'Monthly', description: '30-day correlation' }
    ]

    // Mock correlation data - in real app, this would come from an API
    const mockCorrelationData: Record<string, number[][]> = {
        daily: [
            [1.00, 0.85, -0.72, -0.89, 0.78, -0.65, 0.82, 0.45],
            [0.85, 1.00, -0.68, -0.82, 0.72, -0.58, 0.75, 0.38],
            [-0.72, -0.68, 1.00, 0.75, -0.65, 0.82, -0.58, -0.42],
            [-0.89, -0.82, 0.75, 1.00, -0.72, 0.68, -0.75, -0.48],
            [0.78, 0.72, -0.65, -0.72, 1.00, -0.55, 0.88, 0.35],
            [-0.65, -0.58, 0.82, 0.68, -0.55, 1.00, -0.52, -0.38],
            [0.82, 0.75, -0.58, -0.75, 0.88, -0.52, 1.00, 0.42],
            [0.45, 0.38, -0.42, -0.48, 0.35, -0.38, 0.42, 1.00]
        ],
        weekly: [
            [1.00, 0.82, -0.75, -0.85, 0.75, -0.68, 0.78, 0.42],
            [0.82, 1.00, -0.65, -0.78, 0.68, -0.55, 0.72, 0.35],
            [-0.75, -0.65, 1.00, 0.72, -0.62, 0.78, -0.55, -0.38],
            [-0.85, -0.78, 0.72, 1.00, -0.68, 0.65, -0.72, -0.45],
            [0.75, 0.68, -0.62, -0.68, 1.00, -0.52, 0.85, 0.32],
            [-0.68, -0.55, 0.78, 0.65, -0.52, 1.00, -0.48, -0.35],
            [0.78, 0.72, -0.55, -0.72, 0.85, -0.48, 1.00, 0.38],
            [0.42, 0.35, -0.38, -0.45, 0.32, -0.35, 0.38, 1.00]
        ],
        monthly: [
            [1.00, 0.78, -0.78, -0.82, 0.72, -0.72, 0.75, 0.38],
            [0.78, 1.00, -0.62, -0.75, 0.65, -0.52, 0.68, 0.32],
            [-0.78, -0.62, 1.00, 0.68, -0.58, 0.75, -0.52, -0.35],
            [-0.82, -0.75, 0.68, 1.00, -0.65, 0.62, -0.68, -0.42],
            [0.72, 0.65, -0.58, -0.65, 1.00, -0.48, 0.82, 0.28],
            [-0.72, -0.52, 0.75, 0.62, -0.48, 1.00, -0.45, -0.32],
            [0.75, 0.68, -0.52, -0.68, 0.82, -0.45, 1.00, 0.35],
            [0.38, 0.32, -0.35, -0.42, 0.28, -0.32, 0.35, 1.00]
        ]
    }

    useEffect(() => {
        setIsLoading(true)
        const timer = setTimeout(() => {
            setCorrelationMatrix(mockCorrelationData[selectedTimeframe])
            setIsLoading(false)
        }, 500)

        return () => clearTimeout(timer)
    }, [selectedTimeframe])

    const handleBack = () => {
        router.push('/tools')
    }

    const getCorrelationColor = (correlation: number) => {
        const abs = Math.abs(correlation)
        if (abs >= 0.8) return correlation > 0 ? 'bg-[#28C76F]' : 'bg-[#EA5455]'
        if (abs >= 0.6) return correlation > 0 ? 'bg-[#28C76F]/80' : 'bg-[#EA5455]/80'
        if (abs >= 0.4) return correlation > 0 ? 'bg-[#28C76F]/60' : 'bg-[#EA5455]/60'
        if (abs >= 0.2) return correlation > 0 ? 'bg-[#28C76F]/40' : 'bg-[#EA5455]/40'
        return 'bg-gray-200 dark:bg-gray-600'
    }

    const getCorrelationStrength = (correlation: number) => {
        const abs = Math.abs(correlation)
        if (abs >= 0.8) return 'Very Strong'
        if (abs >= 0.6) return 'Strong'
        if (abs >= 0.4) return 'Moderate'
        if (abs >= 0.2) return 'Weak'
        return 'Very Weak'
    }

    const getTextColor = (correlation: number) => {
        const abs = Math.abs(correlation)
        return abs >= 0.4 ? 'text-white' : 'text-gray-900 dark:text-white'
    }

    return (
        <PublicPageLayout>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black py-20 overflow-hidden">
                <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        
                        <h1 className="text-5xl text-white font-bold mb-6 mt-8">
                            Currency Correlation Matrix
                        </h1>
                        <p className="text-xl text-gray-100 mb-8 max-w-3xl mx-auto">
                            Analyze correlations between major currency pairs to optimize your trading strategy. 
                            Understand how different pairs move in relation to each other.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Correlation Matrix Section */}
            <section className="py-16 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-6">
                    {/* Timeframe Selector */}
                    <div className="flex justify-center gap-4 mb-8">
                        {timeframes.map((timeframe) => (
                            <button
                                key={timeframe.id}
                                onClick={() => setSelectedTimeframe(timeframe.id)}
                                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                                    selectedTimeframe === timeframe.id
                                        ? 'bg-[#EA5455] text-white shadow-lg'
                                        : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                                }`}
                            >
                                <div className="text-center">
                                    <div className="font-semibold">{timeframe.name}</div>
                                    <div className="text-xs opacity-75">{timeframe.description}</div>
                                </div>
                            </button>
                        ))}
                    </div>

                    {/* Correlation Matrix */}
                    <motion.div
                        key={selectedTimeframe}
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                        className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden"
                    >
                        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <HiSquares2X2 className="w-6 h-6 text-[#EA5455]" />
                                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                                    Currency Pair Correlation Matrix
                                </h2>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {timeframes.find(t => t.id === selectedTimeframe)?.description}
                            </p>
                        </div>

                        {isLoading ? (
                            <div className="p-12 text-center">
                                <div className="animate-spin w-8 h-8 border-2 border-[#EA5455] border-t-transparent rounded-full mx-auto mb-4"></div>
                                <p className="text-gray-500 dark:text-gray-400">Loading correlation data...</p>
                            </div>
                        ) : (
                            <div className="p-6 overflow-x-auto">
                                <div className="min-w-[600px]">
                                    <table className="w-full">
                                        <thead>
                                            <tr>
                                                <th className="p-2"></th>
                                                {currencyPairs.map((pair) => (
                                                    <th key={pair} className="p-2 text-xs font-medium text-gray-600 dark:text-gray-400 text-center">
                                                        {pair}
                                                    </th>
                                                ))}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {currencyPairs.map((pair1, i) => (
                                                <tr key={pair1}>
                                                    <td className="p-2 text-xs font-medium text-gray-600 dark:text-gray-400 text-right">
                                                        {pair1}
                                                    </td>
                                                    {currencyPairs.map((pair2, j) => {
                                                        const correlation = correlationMatrix[i]?.[j] || 0
                                                        return (
                                                            <td key={pair2} className="p-1">
                                                                <div
                                                                    className={`w-12 h-12 rounded-lg flex items-center justify-center text-xs font-bold transition-all duration-200 hover:scale-110 cursor-pointer ${getCorrelationColor(correlation)} ${getTextColor(correlation)}`}
                                                                    title={`${pair1} vs ${pair2}: ${correlation.toFixed(2)} (${getCorrelationStrength(correlation)})`}
                                                                >
                                                                    {correlation.toFixed(2)}
                                                                </div>
                                                            </td>
                                                        )
                                                    })}
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}
                    </motion.div>

                    {/* Correlation Legend */}
                    <div className="mt-8 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl">
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-center">
                            Correlation Strength Guide
                        </h3>
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                            <div className="text-center">
                                <div className="w-16 h-16 bg-[#28C76F] rounded-lg mx-auto mb-2 flex items-center justify-center text-white font-bold">
                                    +1.0
                                </div>
                                <div className="text-xs font-medium text-gray-900 dark:text-white">Perfect Positive</div>
                                <div className="text-xs text-gray-600 dark:text-gray-400">Move together</div>
                            </div>
                            <div className="text-center">
                                <div className="w-16 h-16 bg-[#28C76F] rounded-lg mx-auto mb-2 flex items-center justify-center text-white font-bold">
                                    +0.7
                                </div>
                                <div className="text-xs font-medium text-gray-900 dark:text-white">Strong Positive</div>
                                <div className="text-xs text-gray-600 dark:text-gray-400">Usually together</div>
                            </div>
                            <div className="text-center">
                                <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-lg mx-auto mb-2 flex items-center justify-center text-gray-900 dark:text-white font-bold">
                                    0.0
                                </div>
                                <div className="text-xs font-medium text-gray-900 dark:text-white">No Correlation</div>
                                <div className="text-xs text-gray-600 dark:text-gray-400">Independent</div>
                            </div>
                            <div className="text-center">
                                <div className="w-16 h-16 bg-[#EA5455] rounded-lg mx-auto mb-2 flex items-center justify-center text-white font-bold">
                                    -0.7
                                </div>
                                <div className="text-xs font-medium text-gray-900 dark:text-white">Strong Negative</div>
                                <div className="text-xs text-gray-600 dark:text-gray-400">Usually opposite</div>
                            </div>
                            <div className="text-center">
                                <div className="w-16 h-16 bg-[#EA5455] rounded-lg mx-auto mb-2 flex items-center justify-center text-white font-bold">
                                    -1.0
                                </div>
                                <div className="text-xs font-medium text-gray-900 dark:text-white">Perfect Negative</div>
                                <div className="text-xs text-gray-600 dark:text-gray-400">Move opposite</div>
                            </div>
                        </div>
                    </div>

                    {/* Trading Tips */}
                    <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-[#28C76F]/10 dark:bg-[#28C76F]/20 border border-[#28C76F]/30 dark:border-[#28C76F]/40 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-[#28C76F] dark:text-[#28C76F] mb-3">
                                Using Correlations in Trading
                            </h3>
                            <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                                <li>• <strong>Diversification:</strong> Avoid highly correlated pairs</li>
                                <li>• <strong>Hedging:</strong> Use negative correlations to offset risk</li>
                                <li>• <strong>Confirmation:</strong> Strong correlations can confirm signals</li>
                                <li>• <strong>Risk Management:</strong> Monitor correlation changes</li>
                            </ul>
                        </div>

                        <div className="bg-[#EA5455]/10 dark:bg-[#EA5455]/20 border border-[#EA5455]/30 dark:border-[#EA5455]/40 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-[#EA5455] dark:text-[#EA5455] mb-3">
                                Important Notes
                            </h3>
                            <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                                <li>• <strong>Dynamic:</strong> Correlations change over time</li>
                                <li>• <strong>Market Events:</strong> Can break normal correlations</li>
                                <li>• <strong>Timeframes:</strong> Different timeframes show different correlations</li>
                                <li>• <strong>Economic Factors:</strong> Interest rates affect correlations</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </PublicPageLayout>
    )
}

export default CorrelationMatrixPage
