<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyBrokerForex Website - Updated Sitemap</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        @media print {
            body { font-size: 12px; }
            .print-break { page-break-inside: avoid; }
        }
        .section-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .subsection-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .page-row:nth-child(even) { 
            background-color: #f8fafc; 
        }
        .page-row:nth-child(odd) { 
            background-color: #ffffff; 
        }
        .icon-cell {
            width: 60px;
            text-align: center;
        }
        .path-cell {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #4a5568;
        }
        .total-count {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gray-50 p-4">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">
                <i class="fas fa-sitemap text-blue-600 mr-3"></i>
                MyBrokerForex Website Sitemap
            </h1>
            <p class="text-lg text-gray-600">Complete website structure based on actual navigation</p>
            <div class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <p class="text-blue-800 font-medium">
                    <i class="fas fa-info-circle mr-2"></i>
                    This sitemap reflects the actual website navigation structure with organized sections and subsections
                </p>
            </div>
        </div>

        <!-- Main Table -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <table class="w-full border-collapse">
                <thead>
                    <tr class="section-header">
                        <th class="px-4 py-3 text-left font-semibold text-sm uppercase tracking-wider border-b-2 border-gray-200">
                            <i class="fas fa-layer-group mr-2"></i>Section
                        </th>
                        <th class="px-4 py-3 text-left font-semibold text-sm uppercase tracking-wider border-b-2 border-gray-200">
                            <i class="fas fa-link mr-2"></i>Page Path
                        </th>
                        <th class="px-4 py-3 text-left font-semibold text-sm uppercase tracking-wider border-b-2 border-gray-200">
                            <i class="fas fa-file-alt mr-2"></i>Description
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Home -->
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-home text-blue-500 mr-2"></i>Home</td>
                        <td class="px-4 py-3 path-cell">/</td>
                        <td class="px-4 py-3 text-gray-600">Main landing page with overview of services and features</td>
                    </tr>

                    <!-- About -->
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-info-circle text-green-500 mr-2"></i>About</td>
                        <td class="px-4 py-3 path-cell">/about</td>
                        <td class="px-4 py-3 text-gray-600">Company information, mission, values, and team details</td>
                    </tr>

                    <!-- Trading Section -->
                    <tr class="subsection-header">
                        <td colspan="3" class="px-4 py-3 text-center font-bold text-sm uppercase tracking-wider">
                            <i class="fas fa-chart-line mr-2"></i>Trading Section
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-chart-line text-indigo-500 mr-2"></i>Trading Overview</td>
                        <td class="px-4 py-3 path-cell">/trading</td>
                        <td class="px-4 py-3 text-gray-600">Main trading section overview</td>
                    </tr>

                    <!-- Trading Platforms -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-desktop text-purple-500 mr-2"></i>Trading Platforms
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Account Types</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/account-types</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Different trading account options and features</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Web Platform</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/web-platform</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Browser-based trading platform</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Desktop Platform</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/desktop</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Advanced desktop trading software</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Mobile Trading</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/mobile</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Mobile trading application</td>
                    </tr>

                    <!-- Markets -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-globe text-blue-500 mr-2"></i>Markets
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Forex</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/forex</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Major, minor and exotic currency pairs</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Commodities</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/commodities</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Gold, silver, oil and more</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Indices</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/indices</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Global stock market indices</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Cryptocurrencies</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/crypto</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Bitcoin, Ethereum and altcoins</td>
                    </tr>

                    <!-- Trading Tools -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-tools text-orange-500 mr-2"></i>Trading Tools
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Market Analysis</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/analysis</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Technical and fundamental analysis</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Trading Signals</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/signals</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Expert trading recommendations</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Economic Calendar</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/calendar</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Stay updated with market events</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Trading APIs</td>
                        <td class="px-4 py-2 path-cell text-sm">/trading/apis</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Integrate with our trading APIs</td>
                    </tr>

                    <!-- Tools Section -->
                    <tr class="subsection-header">
                        <td colspan="3" class="px-4 py-3 text-center font-bold text-sm uppercase tracking-wider">
                            <i class="fas fa-calculator mr-2"></i>Tools Section
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-calculator text-purple-500 mr-2"></i>Tools Overview</td>
                        <td class="px-4 py-3 path-cell">/tools</td>
                        <td class="px-4 py-3 text-gray-600">Main tools section overview</td>
                    </tr>

                    <!-- Calculators -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-calculator text-green-500 mr-2"></i>Calculators
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Pip Calculator</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/pip-calculator</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Calculate pip values for your trades</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Margin Calculator</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/margin-calculator</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Determine required margin</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Profit Calculator</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/profit-calculator</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Calculate potential profits</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Risk Calculator</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/risk-calculator</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Manage your trading risk</td>
                    </tr>

                    <!-- Market Data -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-chart-bar text-blue-500 mr-2"></i>Market Data
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Live Rates</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/live-rates</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Real-time currency rates</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Market Hours</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/market-hours</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Global market trading hours</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Currency Converter</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/currency-converter</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Convert between currencies</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Volatility Tracker</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/volatility</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Monitor market volatility</td>
                    </tr>

                    <!-- Analysis Tools -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-chart-line text-red-500 mr-2"></i>Analysis Tools
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Correlation Matrix</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/correlation</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Currency pair correlations</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Fibonacci Calculator</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/fibonacci</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Calculate Fibonacci levels</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Position Size Calculator</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/position-size</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Optimize position sizing</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Swap Calculator</td>
                        <td class="px-4 py-2 path-cell text-sm">/tools/swap-calculator</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Calculate overnight fees</td>
                    </tr>

                    <!-- Affiliate Section -->
                    <tr class="subsection-header">
                        <td colspan="3" class="px-4 py-3 text-center font-bold text-sm uppercase tracking-wider">
                            <i class="fas fa-handshake mr-2"></i>Affiliate Section
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-handshake text-yellow-500 mr-2"></i>Affiliate Overview</td>
                        <td class="px-4 py-3 path-cell">/affiliate</td>
                        <td class="px-4 py-3 text-gray-600">Main affiliate program page</td>
                    </tr>

                    <!-- Get Started -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-rocket text-green-500 mr-2"></i>Get Started
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">How It Works</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/how-it-works</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Learn about our affiliate program</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Getting Started</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/getting-started</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Start earning commissions today</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Commission Structure</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/commissions</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Competitive commission rates</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Payment Methods</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/payments</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Flexible payment options</td>
                    </tr>

                    <!-- Resources -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-folder-open text-blue-500 mr-2"></i>Resources
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Marketing Materials</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/materials</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Banners, links and promotional content</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Tracking Tools</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/tracking</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Monitor your referrals and earnings</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Reports & Analytics</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/reports</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Detailed performance reports</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">API Integration</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/api</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Integrate with our affiliate API</td>
                    </tr>

                    <!-- Support -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-headset text-purple-500 mr-2"></i>Support
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Affiliate Portal</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/portal</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Access your affiliate dashboard</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">FAQ</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/faq</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Frequently asked questions</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Terms & Conditions</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/terms</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Affiliate program terms</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Support Center</td>
                        <td class="px-4 py-2 path-cell text-sm">/affiliate/support</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Get help from our team</td>
                    </tr>

                    <!-- Promotion -->
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-gift text-pink-500 mr-2"></i>Promotion</td>
                        <td class="px-4 py-3 path-cell">/promotion</td>
                        <td class="px-4 py-3 text-gray-600">Current promotional offers and bonuses</td>
                    </tr>

                    <!-- Education Section -->
                    <tr class="subsection-header">
                        <td colspan="3" class="px-4 py-3 text-center font-bold text-sm uppercase tracking-wider">
                            <i class="fas fa-graduation-cap mr-2"></i>Education Section
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-graduation-cap text-blue-500 mr-2"></i>Education Overview</td>
                        <td class="px-4 py-3 path-cell">/education</td>
                        <td class="px-4 py-3 text-gray-600">Main education center page</td>
                    </tr>

                    <!-- Learning Resources -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-book text-green-500 mr-2"></i>Learning Resources
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Trading Basics</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/trading-basics</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Learn the fundamentals of forex trading</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Advanced Strategies</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/advanced-strategies</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Master advanced trading techniques</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Market Analysis</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/market-analysis</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Technical and fundamental analysis guides</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Risk Management</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/risk-management</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Protect your trading capital</td>
                    </tr>

                    <!-- Educational Content -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-play-circle text-red-500 mr-2"></i>Educational Content
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Video Tutorials</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/video-tutorials</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Step-by-step video lessons</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Trading Webinars</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/trading-webinars</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Live and recorded webinars</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">E-books & Guides</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/ebooks-guides</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Comprehensive trading guides</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Market News</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/market-news</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Latest market updates and insights</td>
                    </tr>

                    <!-- Trading Tools -->
                    <tr class="bg-gray-100 border-b border-gray-200">
                        <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-700">
                            <i class="fas fa-wrench text-orange-500 mr-2"></i>Trading Tools
                        </td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Demo Account</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/demo-account</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Practice trading risk-free</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Trading Simulator</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/trading-simulator</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Virtual trading environment</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Economic Calendar</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/economic-calendar</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Track important market events</td>
                    </tr>
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-8 py-2 text-sm text-gray-700">Glossary</td>
                        <td class="px-4 py-2 path-cell text-sm">/education/glossary</td>
                        <td class="px-4 py-2 text-sm text-gray-600">Trading terms and definitions</td>
                    </tr>

                    <!-- Support -->
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-headset text-teal-500 mr-2"></i>Support</td>
                        <td class="px-4 py-3 path-cell">/support</td>
                        <td class="px-4 py-3 text-gray-600">Customer support and assistance</td>
                    </tr>

                    <!-- Contact -->
                    <tr class="page-row border-b border-gray-200">
                        <td class="px-4 py-3 font-medium text-gray-800"><i class="fas fa-envelope text-red-500 mr-2"></i>Contact</td>
                        <td class="px-4 py-3 path-cell">/contact</td>
                        <td class="px-4 py-3 text-gray-600">Contact information and forms</td>
                    </tr>

                    <!-- Total Count -->
                    <tr class="total-count">
                        <td class="px-4 py-4 text-center font-bold text-lg" colspan="3">
                            <i class="fas fa-list-ol mr-2"></i>
                            Total Pages: 56 Unique Pages
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Footer Summary -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-blue-50 p-6 rounded-lg border border-blue-200">
                <h3 class="text-lg font-semibold text-blue-800 mb-3">
                    <i class="fas fa-chart-line mr-2"></i>Trading Section
                </h3>
                <p class="text-blue-700">12 pages covering platforms, markets, and trading tools</p>
            </div>
            <div class="bg-green-50 p-6 rounded-lg border border-green-200">
                <h3 class="text-lg font-semibold text-green-800 mb-3">
                    <i class="fas fa-calculator mr-2"></i>Tools Section
                </h3>
                <p class="text-green-700">13 pages with calculators, market data, and analysis tools</p>
            </div>
            <div class="bg-purple-50 p-6 rounded-lg border border-purple-200">
                <h3 class="text-lg font-semibold text-purple-800 mb-3">
                    <i class="fas fa-handshake mr-2"></i>Affiliate Program
                </h3>
                <p class="text-purple-700">13 pages for comprehensive affiliate support</p>
            </div>
        </div>

        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-orange-50 p-6 rounded-lg border border-orange-200">
                <h3 class="text-lg font-semibold text-orange-800 mb-3">
                    <i class="fas fa-graduation-cap mr-2"></i>Education Center
                </h3>
                <p class="text-orange-700">13 pages with comprehensive learning resources</p>
            </div>
            <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">
                    <i class="fas fa-home mr-2"></i>Core Pages
                </h3>
                <p class="text-gray-700">5 essential pages including home, about, support, contact, and promotion</p>
            </div>
        </div>
    </div>
</body>
</html>