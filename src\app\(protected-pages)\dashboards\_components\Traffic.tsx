'use client'

import { Card } from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'
import type { TrafficData } from '../types'

interface TrafficProps {
    data: {
        webAnalytic: {
            series: Array<{
                name: string
                data: number[]
            }>
            date: string[]
        }
    }
}

const Traffic = ({ data }: TrafficProps) => {
    return (
        <Card 
            header={{ content: 'Website Traffic' }}
            className="p-6"
        >
            <div className="mb-4">
                <p className="text-sm text-muted-foreground">
                    Track your website traffic sources and trends over time
                </p>
            </div>
            
            <Chart
                type="area"
                height={350}
                series={data.webAnalytic.series}
                xAxis={data.webAnalytic.date}
                customOptions={{
                    chart: {
                        stacked: false,
                        toolbar: {
                            show: true,
                            tools: {
                                download: true,
                                selection: true,
                                zoom: true,
                                zoomin: true,
                                zoomout: true,
                                pan: true,
                                reset: true
                            }
                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth',
                        width: 2
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0.1,
                            stops: [0, 90, 100]
                        }
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: 'left'
                    },
                    grid: {
                        borderColor: '#e0e6ed',
                        strokeDashArray: 5,
                        xaxis: {
                            lines: {
                                show: true
                            }
                        },
                        yaxis: {
                            lines: {
                                show: true
                            }
                        }
                    },
                    tooltip: {
                        theme: 'dark',
                        y: {
                            formatter: function (val: number) {
                                return val.toLocaleString() + ' visits'
                            }
                        }
                    }
                }}
            />
        </Card>
    )
}

export default Traffic
