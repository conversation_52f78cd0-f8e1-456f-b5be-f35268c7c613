'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { HiCalculator } from 'react-icons/hi2'
import { useRouter } from 'next/navigation'

const MarginCalculatorPage = () => {
    const router = useRouter()
    const [accountCurrency, setAccountCurrency] = useState('USD')
    const [instrument, setInstrument] = useState('EUR/USD')
    const [tradeSize, setTradeSize] = useState('100000')
    const [leverage, setLeverage] = useState('100')
    const [accountBalance, setAccountBalance] = useState('10000')
    const [marginRequired, setMarginRequired] = useState(0)
    const [marginPercentage, setMarginPercentage] = useState(0)
    const [freeMargin, setFreeMargin] = useState(0)

    const instruments = [
        { value: 'EUR/USD', label: 'EUR/USD', rate: 1.0850 },
        { value: 'GBP/USD', label: 'GBP/USD', rate: 1.2650 },
        { value: 'USD/JPY', label: 'USD/JPY', rate: 149.50 },
        { value: 'AUD/USD', label: 'AUD/USD', rate: 0.6750 },
        { value: 'USD/CAD', label: 'USD/CAD', rate: 1.3450 },
        { value: 'USD/CHF', label: 'USD/CHF', rate: 0.8950 },
        { value: 'NZD/USD', label: 'NZD/USD', rate: 0.6150 },
        { value: 'EUR/GBP', label: 'EUR/GBP', rate: 0.8580 }
    ]

    const leverageOptions = [
        { value: '50', label: '1:50' },
        { value: '100', label: '1:100' },
        { value: '200', label: '1:200' },
        { value: '400', label: '1:400' },
        { value: '500', label: '1:500' }
    ]

    useEffect(() => {
        calculateMargin()
    }, [accountCurrency, instrument, tradeSize, leverage, accountBalance])

    const calculateMargin = () => {
        const selectedInstrument = instruments.find(inst => inst.value === instrument)
        if (!selectedInstrument) return

        const tradeSizeNum = parseFloat(tradeSize) || 0
        const leverageNum = parseFloat(leverage) || 1
        const balanceNum = parseFloat(accountBalance) || 0

        // Calculate margin required
        const margin = (tradeSizeNum * selectedInstrument.rate) / leverageNum
        const marginPercent = (margin / balanceNum) * 100
        const freeMarg = balanceNum - margin

        setMarginRequired(margin)
        setMarginPercentage(marginPercent)
        setFreeMargin(freeMarg)
    }



    return (
        <PublicPageLayout>
                {/* Hero Section */}
                <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black py-20 overflow-hidden">
                    <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >

                            <h1 className="text-5xl text-white mt-8 font-bold mb-6">
                                Margin Calculator
                            </h1>
                            <p className="text-xl text-gray-100 mb-8 max-w-3xl mx-auto">
                                Calculate the required margin for your trades based on instrument, trade size, 
                                and leverage. Essential for proper risk management and position sizing.
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* Calculator Section */}
                <section className="py-16 bg-gray-50 dark:bg-gray-900">
                    <div className="max-w-7xl mx-auto px-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                            {/* Calculator Form */}
                            <motion.div
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8 }}
                                className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl"
                            >
                                <div className="flex items-center gap-3 mb-8">
                                    <div className="p-3 bg-[#EA5455] bg-opacity-10 rounded-xl">
                                        <HiCalculator className="w-6 h-6 text-[#EA5455]" />
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                                        Margin Calculator
                                    </h2>
                                </div>

                                <div className="space-y-6">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Account Currency
                                        </label>
                                        <select
                                            value={accountCurrency}
                                            onChange={(e) => setAccountCurrency(e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        >
                                            <option value="USD">USD</option>
                                            <option value="EUR">EUR</option>
                                            <option value="GBP">GBP</option>
                                            <option value="JPY">JPY</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Trading Instrument
                                        </label>
                                        <select
                                            value={instrument}
                                            onChange={(e) => setInstrument(e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        >
                                            {instruments.map((inst) => (
                                                <option key={inst.value} value={inst.value}>
                                                    {inst.label} ({inst.rate})
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Trade Size (Units)
                                        </label>
                                        <input
                                            type="number"
                                            value={tradeSize}
                                            onChange={(e) => setTradeSize(e.target.value)}
                                            placeholder="100000"
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Leverage
                                        </label>
                                        <select
                                            value={leverage}
                                            onChange={(e) => setLeverage(e.target.value)}
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        >
                                            {leverageOptions.map((option) => (
                                                <option key={option.value} value={option.value}>
                                                    {option.label}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Account Balance ({accountCurrency})
                                        </label>
                                        <input
                                            type="number"
                                            value={accountBalance}
                                            onChange={(e) => setAccountBalance(e.target.value)}
                                            placeholder="10000"
                                            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                        />
                                    </div>
                                </div>
                            </motion.div>

                            {/* Results */}
                            <motion.div
                                initial={{ opacity: 0, x: 30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="space-y-6"
                            >
                                {marginRequired > 0 && (
                                    <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            Margin Calculation Result
                                        </h3>
                                        <div className="space-y-4">
                                            <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                                <span className="text-gray-600 dark:text-gray-400">Required Margin:</span>
                                                <span className="font-semibold text-gray-900 dark:text-white">
                                                    {marginRequired.toFixed(2)} {accountCurrency}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                                <span className="text-gray-600 dark:text-gray-400">Margin Percentage:</span>
                                                <span className="font-semibold text-gray-900 dark:text-white">
                                                    {marginPercentage.toFixed(2)}%
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center py-2">
                                                <span className="text-gray-600 dark:text-gray-400">Free Margin:</span>
                                                <span className={`font-semibold ${freeMargin >= 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'}`}>
                                                    {freeMargin.toFixed(2)} {accountCurrency}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Educational Content */}
                                <div className="bg-[#28C76F]/10 dark:bg-[#28C76F]/20 border border-[#28C76F]/30 dark:border-[#28C76F]/40 rounded-xl p-6">
                                    <h3 className="text-lg font-semibold text-[#28C76F] mb-3">
                                        Understanding Margin
                                    </h3>
                                    <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                                        <li>• <strong>Required Margin:</strong> The amount needed to open a position</li>
                                        <li>• <strong>Free Margin:</strong> Available funds for new positions</li>
                                        <li>• <strong>Margin Level:</strong> (Equity/Used Margin) × 100</li>
                                        <li>• <strong>Margin Call:</strong> Usually occurs at 100% margin level</li>
                                    </ul>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </section>
            </PublicPageLayout>
    )
}

export default MarginCalculatorPage
