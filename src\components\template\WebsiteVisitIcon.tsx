'use client'

import withHeaderItem from '@/utils/hoc/withHeaderItem'
import { TbExternalLink } from 'react-icons/tb'
import type { CommonProps } from '@/@types/common'

const _WebsiteVisitIcon = ({ className }: CommonProps) => {
    const handleVisitWebsite = () => {
        if (typeof window !== 'undefined') {
            window.open('/', '_blank', 'noopener,noreferrer')
        }
    }

    return (
        <div
            className={`text-2xl cursor-pointer hover:text-primary transition-colors ${className}`}
            onClick={handleVisitWebsite}
            title="Visit Website"
        >
            <TbExternalLink />
        </div>
    )
}

const WebsiteVisitIcon = withHeaderItem(_WebsiteVisitIcon)

export default WebsiteVisitIcon
