'use client'

import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import Loading from '@/components/shared/Loading'
import type { AnalyticData } from './types'

// Dynamically import the dashboard client page to prevent SSR issues
const DashboardClientPage = dynamic(() => import('./DashboardClientPage'), {
    ssr: false,
    loading: () => <Loading loading={true} />
})

export default function Page() {
    const [data, setData] = useState<AnalyticData | null>(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        // Fetch data on client side
        const fetchData = async () => {
            try {
                const response = await fetch('/api/dashboards/analytics')
                const result = await response.json()
                setData(result)
            } catch (error) {
                console.error('Failed to fetch dashboard data:', error)
                // Fallback to mock data
                setData({
                    thisMonth: {
                        metrics: {
                            visitors: { value: 12345, growShrink: 12.5 },
                            conversionRate: { value: 2.6, growShrink: 0.2 },
                            adCampaignClicks: { value: 4567, growShrink: 2.4 }
                        },
                        webAnalytic: {
                            pageView: { value: 12345, growShrink: 12.5 },
                            avgTimeOnPage: { value: '2m 30s', growShrink: 0.2 },
                            series: [
                                { name: 'Visitors', data: [12345, 12340, 12342, 12341, 12344, 12343, 12346] },
                                { name: 'Conversion Rate', data: [2.6, 2.5, 2.7, 2.4, 2.8, 2.6, 2.5] },
                                { name: 'Ad Campaign Clicks', data: [4567, 4565, 4568, 4566, 4569, 4567, 4568] }
                            ],
                            date: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                        },
                        topChannel: {
                            visitors: 12345,
                            channels: [
                                { id: '1', name: 'Direct', img: '/img/thumbs/facebook.png', total: 1234, percentage: 45 },
                                { id: '2', name: 'Organic Search', img: '/img/thumbs/google-drive.png', total: 987, percentage: 35 },
                                { id: '3', name: 'Social Media', img: '/img/thumbs/instagram.png', total: 543, percentage: 20 }
                            ]
                        },
                        deviceSession: {
                            labels: ['Desktop', 'Mobile', 'Tablet'],
                            series: [60, 30, 10],
                            percentage: [60, 30, 10]
                        },
                        topPerformingPages: [
                            { page: '/dashboard', views: 1234, uniqueViews: 987, bounceRate: 2.5 },
                            { page: '/analytics', views: 987, uniqueViews: 765, bounceRate: 3.2 },
                            { page: '/reports', views: 765, uniqueViews: 543, bounceRate: 4.1 }
                        ]
                    }
                })
            } finally {
                setLoading(false)
            }
        }

        fetchData()
    }, [])

    if (loading || !data) {
        return <Loading loading={true} />
    }

    return <div className="mt-20"><DashboardClientPage initialData={data} /></div>
}
