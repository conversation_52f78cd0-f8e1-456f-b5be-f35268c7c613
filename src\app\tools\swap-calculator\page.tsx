'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { HiArrowPath } from 'react-icons/hi2'
import { useRouter } from 'next/navigation'

const SwapCalculatorPage = () => {
    const router = useRouter()
    const [instrument, setInstrument] = useState('EUR/USD')
    const [tradeSize, setTradeSize] = useState('100000')
    const [tradeDirection, setTradeDirection] = useState('buy')
    const [accountCurrency, setAccountCurrency] = useState('USD')
    const [swapLong, setSwapLong] = useState(0)
    const [swapShort, setSwapShort] = useState(0)
    const [dailySwap, setDailySwap] = useState(0)
    const [weeklySwap, setWeeklySwap] = useState(0)
    const [monthlySwap, setMonthlySwap] = useState(0)

    const instruments = [
        { value: 'EUR/USD', label: 'EUR/USD', swapLong: -0.65, swapShort: -0.35 },
        { value: 'GBP/USD', label: 'GBP/USD', swapLong: -0.85, swapShort: -0.45 },
        { value: 'USD/JPY', label: 'USD/JPY', swapLong: 0.25, swapShort: -0.95 },
        { value: 'AUD/USD', label: 'AUD/USD', swapLong: -0.55, swapShort: -0.25 },
        { value: 'USD/CAD', label: 'USD/CAD', swapLong: 0.15, swapShort: -0.75 },
        { value: 'USD/CHF', label: 'USD/CHF', swapLong: 0.35, swapShort: -1.05 },
        { value: 'NZD/USD', label: 'NZD/USD', swapLong: -0.45, swapShort: -0.15 },
        { value: 'EUR/GBP', label: 'EUR/GBP', swapLong: -0.25, swapShort: -0.55 }
    ]

    useEffect(() => {
        calculateSwap()
    }, [instrument, tradeSize, tradeDirection, accountCurrency])

    const calculateSwap = () => {
        const selectedInstrument = instruments.find(inst => inst.value === instrument)
        if (!selectedInstrument) return

        const tradeSizeNum = parseFloat(tradeSize) || 0
        const lots = tradeSizeNum / 100000 // Convert to lots

        const longSwap = selectedInstrument.swapLong
        const shortSwap = selectedInstrument.swapShort

        setSwapLong(longSwap)
        setSwapShort(shortSwap)

        // Calculate daily swap based on trade direction
        const currentSwap = tradeDirection === 'buy' ? longSwap : shortSwap
        const daily = currentSwap * lots
        const weekly = daily * 7
        const monthly = daily * 30

        setDailySwap(daily)
        setWeeklySwap(weekly)
        setMonthlySwap(monthly)
    }



    return (
        <div className="mt-0">
            <PublicPageLayout>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black py-20 overflow-hidden">
                <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >

                        <h1 className="text-5xl text-gray-300 mt-8 font-bold mb-6">
                            Swap Calculator
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Calculate overnight swap rates and rollover costs for your forex positions. 
                            Essential for understanding the cost of holding positions overnight.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Calculator Section */}
            <section className="py-16 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        {/* Calculator Form */}
                        <motion.div
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl"
                        >
                            <div className="flex items-center gap-3 mb-8">
                                <div className="p-3 bg-[#EA5455] bg-opacity-10 rounded-xl">
                                    <HiArrowPath className="w-6 h-6 text-[#EA5455]" />
                                </div>
                                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                                    Swap Calculator
                                </h2>
                            </div>

                            <div className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Trading Instrument
                                    </label>
                                    <select
                                        value={instrument}
                                        onChange={(e) => setInstrument(e.target.value)}
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    >
                                        {instruments.map((inst) => (
                                            <option key={inst.value} value={inst.value}>
                                                {inst.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Trade Size (Units)
                                    </label>
                                    <input
                                        type="number"
                                        value={tradeSize}
                                        onChange={(e) => setTradeSize(e.target.value)}
                                        placeholder="100000"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Trade Direction
                                    </label>
                                    <div className="grid grid-cols-2 gap-3">
                                        <button
                                            onClick={() => setTradeDirection('buy')}
                                            className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                                                tradeDirection === 'buy'
                                                    ? 'bg-[#28C76F] text-white'
                                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                            }`}
                                        >
                                            Buy (Long)
                                        </button>
                                        <button
                                            onClick={() => setTradeDirection('sell')}
                                            className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                                                tradeDirection === 'sell'
                                                    ? 'bg-[#EA5455] text-white'
                                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                            }`}
                                        >
                                            Sell (Short)
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Account Currency
                                    </label>
                                    <select
                                        value={accountCurrency}
                                        onChange={(e) => setAccountCurrency(e.target.value)}
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    >
                                        <option value="USD">USD</option>
                                        <option value="EUR">EUR</option>
                                        <option value="GBP">GBP</option>
                                        <option value="JPY">JPY</option>
                                    </select>
                                </div>
                            </div>
                        </motion.div>

                        {/* Results */}
                        <motion.div
                            initial={{ opacity: 0, x: 30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            className="space-y-6"
                        >
                            {/* Swap Rates */}
                            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                    Current Swap Rates ({instrument})
                                </h3>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="text-center p-4 bg-[#28C76F]/10 dark:bg-[#28C76F]/20 rounded-xl">
                                        <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Long Position</div>
                                        <div className={`text-lg font-bold ${swapLong >= 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'}`}>
                                            {swapLong > 0 ? '+' : ''}{swapLong.toFixed(2)}
                                        </div>
                                    </div>
                                    <div className="text-center p-4 bg-[#EA5455]/10 dark:bg-[#EA5455]/20 rounded-xl">
                                        <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Short Position</div>
                                        <div className={`text-lg font-bold ${swapShort >= 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'}`}>
                                            {swapShort > 0 ? '+' : ''}{swapShort.toFixed(2)}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Swap Calculations */}
                            {dailySwap !== 0 && (
                                <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                        Swap Cost Calculation
                                    </h3>
                                    <div className="space-y-3">
                                        <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                            <span className="text-gray-600 dark:text-gray-400">Daily Swap:</span>
                                            <span className={`font-semibold ${dailySwap >= 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'}`}>
                                                {dailySwap > 0 ? '+' : ''}{dailySwap.toFixed(2)} {accountCurrency}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                            <span className="text-gray-600 dark:text-gray-400">Weekly Swap:</span>
                                            <span className={`font-semibold ${weeklySwap >= 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'}`}>
                                                {weeklySwap > 0 ? '+' : ''}{weeklySwap.toFixed(2)} {accountCurrency}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center py-2">
                                            <span className="text-gray-600 dark:text-gray-400">Monthly Swap:</span>
                                            <span className={`font-semibold ${monthlySwap >= 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'}`}>
                                                {monthlySwap > 0 ? '+' : ''}{monthlySwap.toFixed(2)} {accountCurrency}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Educational Content */}
                            <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                                    Understanding Swap Rates
                                </h3>
                                <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                                    <li>• <strong>Swap:</strong> Interest rate differential between currencies</li>
                                    <li>• <strong>Positive Swap:</strong> You earn money holding the position</li>
                                    <li>• <strong>Negative Swap:</strong> You pay to hold the position</li>
                                    <li>• <strong>Triple Swap:</strong> Wednesday positions incur 3x swap</li>
                                </ul>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </section>
                    </PublicPageLayout>
        </div>
    )
}

export default SwapCalculatorPage
