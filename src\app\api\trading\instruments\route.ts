import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

// GET /api/trading/instruments - Get all trading instruments
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const category = searchParams.get('category')
        const active = searchParams.get('active')
        
        let query = 'SELECT * FROM instruments WHERE 1=1'
        const params: any[] = []
        
        if (category) {
            query += ' AND category = ?'
            params.push(category)
        }
        
        if (active) {
            query += ' AND is_active = ?'
            params.push(active === 'true')
        }
        
        query += ' ORDER BY category, name'
        
        const [rows] = await pool.execute<RowDataPacket[]>(query, params)
        
        return NextResponse.json({
            success: true,
            data: rows
        })
    } catch (error) {
        console.error('Error fetching instruments:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch instruments' },
            { status: 500 }
        )
    }
}

// POST /api/trading/instruments - Create new instrument
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            name, 
            symbol, 
            category, 
            bid = 0.00000, 
            ask = 0.00000, 
            spread = 0.00, 
            leverage, 
            trading_hours, 
            is_active = true 
        } = body
        
        if (!name || !symbol || !category) {
            return NextResponse.json(
                { success: false, error: 'Name, symbol, and category are required' },
                { status: 400 }
            )
        }
        
        // Validate category
        const validCategories = ['forex', 'commodities', 'crypto', 'stocks', 'indices']
        if (!validCategories.includes(category)) {
            return NextResponse.json(
                { success: false, error: 'Invalid category' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO instruments (name, symbol, category, bid, ask, spread, leverage, trading_hours, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [name, symbol, category, bid, ask, spread, leverage, trading_hours, is_active]
        )
        
        return NextResponse.json({
            success: true,
            data: { 
                id: result.insertId, 
                name, 
                symbol, 
                category, 
                bid, 
                ask, 
                spread, 
                leverage, 
                trading_hours, 
                is_active 
            }
        }, { status: 201 })
    } catch (error: any) {
        console.error('Error creating instrument:', error)
        
        if (error.code === 'ER_DUP_ENTRY') {
            return NextResponse.json(
                { success: false, error: 'Instrument with this symbol already exists' },
                { status: 409 }
            )
        }
        
        return NextResponse.json(
            { success: false, error: 'Failed to create instrument' },
            { status: 500 }
        )
    }
}

// PUT /api/trading/instruments - Update instrument
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            id, 
            name, 
            symbol, 
            category, 
            bid, 
            ask, 
            spread, 
            leverage, 
            trading_hours, 
            is_active 
        } = body
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Instrument ID is required' },
                { status: 400 }
            )
        }
        
        // Validate category if provided
        if (category) {
            const validCategories = ['forex', 'commodities', 'crypto', 'stocks', 'indices']
            if (!validCategories.includes(category)) {
                return NextResponse.json(
                    { success: false, error: 'Invalid category' },
                    { status: 400 }
                )
            }
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE instruments SET name = ?, symbol = ?, category = ?, bid = ?, ask = ?, spread = ?, leverage = ?, trading_hours = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [name, symbol, category, bid, ask, spread, leverage, trading_hours, is_active, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Instrument not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { id, name, symbol, category, bid, ask, spread, leverage, trading_hours, is_active }
        })
    } catch (error: any) {
        console.error('Error updating instrument:', error)
        
        if (error.code === 'ER_DUP_ENTRY') {
            return NextResponse.json(
                { success: false, error: 'Instrument with this symbol already exists' },
                { status: 409 }
            )
        }
        
        return NextResponse.json(
            { success: false, error: 'Failed to update instrument' },
            { status: 500 }
        )
    }
}

// DELETE /api/trading/instruments - Delete instrument
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Instrument ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM instruments WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Instrument not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Instrument deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting instrument:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete instrument' },
            { status: 500 }
        )
    }
}
