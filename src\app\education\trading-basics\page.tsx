'use client'

import { motion } from 'framer-motion'
import { TbB<PERSON>, TbTarget, TbChartLine, TbShield, TbT<PERSON>dingUp, Tb<PERSON>lock } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

const TradingBasicsPage = () => {
    const basicsTopics = [
        {
            icon: TbBook,
            title: 'What is Forex Trading?',
            description: 'Learn the fundamentals of foreign exchange trading and how currency markets work.',
            topics: ['Currency Pairs', 'Market Participants', 'Trading Sessions', 'Market Structure']
        },
        {
            icon: TbTarget,
            title: 'Getting Started',
            description: 'Essential steps to begin your trading journey with proper preparation.',
            topics: ['Account Types', 'Platform Setup', 'Risk Management', 'Trading Plan']
        },
        {
            icon: TbChartLine,
            title: 'Basic Analysis',
            description: 'Introduction to fundamental and technical analysis methods.',
            topics: ['Economic Indicators', 'Chart Patterns', 'Support & Resistance', 'Trend Analysis']
        },
        {
            icon: TbShield,
            title: 'Risk Management',
            description: 'Protect your capital with proper risk management strategies.',
            topics: ['Position Sizing', 'Stop Losses', 'Take Profits', 'Risk-Reward Ratios']
        }
    ]

    const learningPath = [
        {
            step: '01',
            title: 'Understanding Forex Markets',
            description: 'Learn how currency markets operate and what drives price movements.',
            duration: '2-3 hours',
            topics: ['Market Structure', 'Currency Pairs', 'Trading Sessions', 'Market Participants']
        },
        {
            step: '02',
            title: 'Trading Platform Basics',
            description: 'Master your trading platform and understand order types.',
            duration: '1-2 hours',
            topics: ['Platform Navigation', 'Order Types', 'Chart Reading', 'Basic Tools']
        },
        {
            step: '03',
            title: 'Fundamental Analysis',
            description: 'Understand how economic events impact currency prices.',
            duration: '3-4 hours',
            topics: ['Economic Indicators', 'Central Banks', 'News Events', 'Market Sentiment']
        },
        {
            step: '04',
            title: 'Technical Analysis',
            description: 'Learn to read charts and identify trading opportunities.',
            duration: '4-5 hours',
            topics: ['Chart Patterns', 'Indicators', 'Support/Resistance', 'Trend Analysis']
        },
        {
            step: '05',
            title: 'Risk Management',
            description: 'Develop strategies to protect your trading capital.',
            duration: '2-3 hours',
            topics: ['Position Sizing', 'Stop Losses', 'Risk-Reward', 'Money Management']
        },
        {
            step: '06',
            title: 'Trading Psychology',
            description: 'Master the mental aspects of successful trading.',
            duration: '2-3 hours',
            topics: ['Emotional Control', 'Discipline', 'Patience', 'Confidence Building']
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Trading Basics
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Master the fundamentals of forex trading with our comprehensive beginner's guide
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-[#EA5455] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#d63384] transition-colors duration-200"
                                >
                                    Start Learning
                                </button>
                            </motion.div>
                        </div>
                    </section>

                    {/* Learning Topics */}
                    <section className="py-20">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    What You'll Learn
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Comprehensive coverage of essential trading concepts for beginners
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {basicsTopics.map((topic, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                    >
                                        <div className="flex items-center mb-6">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                                <topic.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                                {topic.title}
                                            </h3>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                                            {topic.description}
                                        </p>
                                        <ul className="space-y-2">
                                            {topic.topics.map((item, itemIndex) => (
                                                <li key={itemIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                    <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                    {item}
                                                </li>
                                            ))}
                                        </ul>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Learning Path */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Your Learning Path
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Follow our structured learning path to build a solid foundation
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {learningPath.map((step, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-center justify-between mb-4">
                                            <span className="text-2xl font-bold text-[#EA5455]">{step.step}</span>
                                            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                                <TbClock className="w-4 h-4 mr-1" />
                                                {step.duration}
                                            </div>
                                        </div>
                                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                            {step.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                                            {step.description}
                                        </p>
                                        <ul className="space-y-1">
                                            {step.topics.map((topic, topicIndex) => (
                                                <li key={topicIndex} className="text-sm text-gray-500 dark:text-gray-400">
                                                    • {topic}
                                                </li>
                                            ))}
                                        </ul>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-[#EA5455]">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Ready to Start Your Trading Journey?
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                    Join thousands of traders who started with our educational resources
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                                >
                                    Open Trading Account
                                </button>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default TradingBasicsPage 