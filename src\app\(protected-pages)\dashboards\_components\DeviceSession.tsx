'use client'

import { Card } from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'
import { MonitorIcon, SmartphoneIcon, TabletIcon } from 'lucide-react'
import type { DeviceSessionData } from '../types'

interface DeviceSessionProps {
    data: {
        deviceSession: DeviceSessionData
    }
}

const DeviceSession = ({ data }: DeviceSessionProps) => {
    const getDeviceIcon = (device: string) => {
        switch (device.toLowerCase()) {
            case 'desktop':
                return <MonitorIcon className="h-5 w-5" />
            case 'mobile':
                return <SmartphoneIcon className="h-5 w-5" />
            case 'tablet':
                return <TabletIcon className="h-5 w-5" />
            default:
                return <MonitorIcon className="h-5 w-5" />
        }
    }

    const getDeviceColor = (index: number) => {
        const colors = ['#EA5455', '#28C76F', '#2a85ff']
        return colors[index] || '#6c757d'
    }

    return (
        <Card 
            header={{ content: 'Device Sessions' }}
            className="p-6"
        >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Chart */}
                <div>
                    <Chart
                        type="donut"
                        height={250}
                        series={data.deviceSession.series}
                        customOptions={{
                            labels: data.deviceSession.labels,
                            colors: ['#EA5455', '#28C76F', '#2a85ff'],
                            legend: {
                                show: false
                            },
                            plotOptions: {
                                pie: {
                                    donut: {
                                        size: '70%',
                                        labels: {
                                            show: true,
                                            name: {
                                                show: true,
                                                fontSize: '16px',
                                                fontWeight: 600
                                            },
                                            value: {
                                                show: true,
                                                fontSize: '14px',
                                                formatter: function (val: string) {
                                                    return parseInt(val).toLocaleString()
                                                }
                                            },
                                            total: {
                                                show: true,
                                                label: 'Total Sessions',
                                                fontSize: '14px',
                                                formatter: function (w: any) {
                                                    return w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0).toLocaleString()
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            dataLabels: {
                                enabled: false
                            },
                            tooltip: {
                                y: {
                                    formatter: function (val: number) {
                                        return val.toLocaleString() + ' sessions'
                                    }
                                }
                            }
                        }}
                    />
                </div>

                {/* Device Stats */}
                <div className="space-y-4">
                    {data.deviceSession.labels.map((device, index) => (
                        <div key={device} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="flex items-center gap-3">
                                <div 
                                    className="p-2 rounded-lg"
                                    style={{ backgroundColor: `${getDeviceColor(index)}20`, color: getDeviceColor(index) }}
                                >
                                    {getDeviceIcon(device)}
                                </div>
                                <div>
                                    <p className="font-medium">{device}</p>
                                    <p className="text-sm text-muted-foreground">
                                        {data.deviceSession.percentage[index]}% of total
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="font-semibold">{data.deviceSession.series[index].toLocaleString()}</p>
                                <p className="text-sm text-muted-foreground">sessions</p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </Card>
    )
}

export default DeviceSession
