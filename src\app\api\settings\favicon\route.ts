import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import pool from '@/server/db'

export async function POST(request: NextRequest) {
    try {
        const formData = await request.formData()
        const file = formData.get('favicon') as File
        
        if (!file) {
            return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
        }

        // Validate file type
        const allowedTypes = ['image/x-icon', 'image/png', 'image/vnd.microsoft.icon']
        if (!allowedTypes.includes(file.type)) {
            return NextResponse.json({ 
                error: 'Invalid file type. Only ICO and PNG files are allowed.' 
            }, { status: 400 })
        }

        // Validate file size (max 1MB)
        if (file.size > 1 * 1024 * 1024) {
            return NextResponse.json({ 
                error: 'File too large. Maximum size is 1MB.' 
            }, { status: 400 })
        }

        const bytes = await file.arrayBuffer()
        const buffer = Buffer.from(bytes)

        // Create uploads directory if it doesn't exist
        const uploadsDir = join(process.cwd(), 'public', 'uploads', 'favicons')
        await mkdir(uploadsDir, { recursive: true })

        // Generate unique filename
        const timestamp = Date.now()
        const extension = file.name.split('.').pop() || 'ico'
        const filename = `favicon-${timestamp}.${extension}`
        const filepath = join(uploadsDir, filename)

        // Save file
        await writeFile(filepath, buffer)

        // Update database with new favicon path
        const faviconPath = `/uploads/favicons/${filename}`
        await pool.query(
            "INSERT INTO settings (setting_key, setting_value) VALUES ('site_favicon', ?) ON DUPLICATE KEY UPDATE setting_value = ?",
            [faviconPath, faviconPath]
        )

        return NextResponse.json({ 
            success: true, 
            faviconPath,
            message: 'Favicon uploaded successfully' 
        })

    } catch (error) {
        console.error('Favicon upload error:', error)
        return NextResponse.json({ 
            error: 'Failed to upload favicon' 
        }, { status: 500 })
    }
}

export async function GET() {
    try {
        const [rows] = await pool.query(
            "SELECT setting_value FROM settings WHERE setting_key = 'site_favicon'"
        )
        
        const faviconPath = Array.isArray(rows) && rows.length > 0 ? (rows as any[])[0].setting_value : '/uploads/favicons/favicon-1751614487791.png'
        
        return NextResponse.json({ 
            success: true, 
            faviconPath 
        })
    } catch (error) {
        console.error('Get favicon error:', error)
        return NextResponse.json({ 
            success: true, 
            faviconPath: '/uploads/favicons/favicon-1751614487791.png' // fallback
        })
    }
}
