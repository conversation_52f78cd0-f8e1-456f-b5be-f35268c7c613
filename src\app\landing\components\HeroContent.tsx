import Button from '@/components/ui/Button'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useHeroSections } from '@/utils/hooks/useHeroSections'
import { TbArrowRight, TbInfoCircle } from 'react-icons/tb'
import type { Mode } from '@/@types/theme'

const HeroContent = ({ mode }: { mode: Mode }) => {
    const router = useRouter()
    const { heroSections } = useHeroSections(true)

    // Get the first active hero section or use default
    const heroData = heroSections.length > 0 ? heroSections[0] : {
        title: 'Say goodbye to waiting times! Experience 24/7 instant deposit and withdrawal processing.',
        subtitle: 'Perform your transactions swiftly and conveniently. Whether you prefer bank transfers, credit/debit cards, e-wallets, or cryptocurrencies, we support a wide range of deposit methods to cater to your needs',
        cta_text: 'Start Trading Now',
        cta_link: '/sign-up'
    }

    const handleStartTrading = () => {
        // Always redirect to external register page
        if (typeof window !== 'undefined') {
            window.open('https://mbf.mybrokerforex.com/user/register', '_blank')
        }
    }

    const handleLearnMore = () => {
        router.push('/about')
    }

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 flex h-screen flex-col items-center justify-center" suppressHydrationWarning>
            <div className="flex flex-col items-center justify-center text-center relative overflow-hidden" suppressHydrationWarning>
                <div suppressHydrationWarning>
                    {/* Small centered title */}
                    <motion.div
                        initial={{ opacity: 0, translateY: 20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ duration: 0.6, delay: 0.1 }}
                        className="text-center mb-4"
                    >
                        {/* <span className="text-lg md:text-xl text-white/80 font-medium">Experience 24/7</span> */}
                    </motion.div>

                    {/* Large main title */}
                    <motion.h1
                        initial={{ opacity: 0, translateY: 40 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                        className="text-2xl sm:text-3xl md:text-5xl lg:text-7xl font-bold max-w-7xl mx-auto text-center relative z-10 text-white drop-shadow-lg"
                    >
                        Say goodbye to waiting times!
                    </motion.h1>

                    {/* Small subtitle */}
                    <motion.div
                        initial={{ opacity: 0, translateY: 20 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                        className="text-center mt-4 mb-6"
                    >
                        <span className="text-lg md:text-xl text-[#EA5455] font-semibold drop-shadow-lg">Instant deposit and withdrawal processing.</span>
                    </motion.div>
                    <motion.p
                        initial={{ opacity: 0, translateY: 40 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ duration: 0.3, delay: 0.5 }}
                        className="text-center mt-6 text-sm sm:text-base md:text-xl text-white/90 max-w-5xl mx-auto relative z-10 font-normal drop-shadow-md px-4"
                    >
                        Perform your transactions swiftly and conveniently. Whether you prefer bank transfers, credit/debit cards, e-wallets, or cryptocurrencies, we support a wide range of deposit methods to cater to your needs
                    </motion.p>
                    <motion.div
                        initial={{ opacity: 0, translateY: 40 }}
                        animate={{ opacity: 1, translateY: 0 }}
                        transition={{ duration: 0.3, delay: 0.6 }}
                        className="flex flex-col sm:flex-row items-center gap-4 justify-center mt-10 relative z-10 px-4"
                        suppressHydrationWarning
                    >
                        <Button
                            variant="solid"
                            size="sm"
                            onClick={handleStartTrading}
                            className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-6 py-2 text-base font-semibold"
                            icon={<TbArrowRight />}
                            iconAlignment="end"
                        >
                            <span>{heroData.cta_text || 'Start Trading Now'}</span>
                        </Button>
                        <Button
                            variant="plain"
                            size="sm"
                            onClick={handleLearnMore}
                            className="border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white px-6 py-2 text-base font-semibold"
                            icon={<TbInfoCircle />}
                            iconAlignment="start"
                        >
                            <span>Learn More</span>
                        </Button>
                    </motion.div>
                </div>

            </div>
        </div>
    )
}

export default HeroContent
