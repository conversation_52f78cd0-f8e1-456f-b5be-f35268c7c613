'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import Button from '@/components/ui/Button'

interface Props {
    children: ReactNode
    fallback?: ReactNode
    onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
    hasError: boolean
    error?: Error
}

class ErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props)
        this.state = { hasError: false }
    }

    static getDerivedStateFromError(error: Error): State {
        return { hasError: true, error }
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error('ErrorBoundary caught an error:', error, errorInfo)
        
        // Call the onError callback if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo)
        }
    }

    handleRetry = () => {
        this.setState({ hasError: false, error: undefined })
    }

    render() {
        if (this.state.hasError) {
            // Custom fallback UI
            if (this.props.fallback) {
                return this.props.fallback
            }

            // Default error UI
            return (
                <div className="flex flex-col items-center justify-center min-h-[200px] p-8 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
                        <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        Something went wrong
                    </h3>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-6 max-w-md">
                        We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.
                    </p>
                    
                    {process.env.NODE_ENV === 'development' && this.state.error && (
                        <details className="mb-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-xs font-mono max-w-full overflow-auto">
                            <summary className="cursor-pointer text-gray-700 dark:text-gray-300 mb-2">
                                Error Details (Development Only)
                            </summary>
                            <pre className="whitespace-pre-wrap text-red-600 dark:text-red-400">
                                {this.state.error.stack}
                            </pre>
                        </details>
                    )}
                    
                    <Button 
                        onClick={this.handleRetry}
                        className="flex items-center gap-2"
                        variant="plain"
                    >
                        <RefreshCw className="w-4 h-4" />
                        Try Again
                    </Button>
                </div>
            )
        }

        return this.props.children
    }
}

export default ErrorBoundary

// Higher-order component for easier usage
export function withErrorBoundary<P extends object>(
    Component: React.ComponentType<P>,
    fallback?: ReactNode,
    onError?: (error: Error, errorInfo: ErrorInfo) => void
) {
    const WrappedComponent = (props: P) => (
        <ErrorBoundary fallback={fallback} onError={onError}>
            <Component {...props} />
        </ErrorBoundary>
    )
    
    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
    
    return WrappedComponent
}

// Async error boundary for handling async errors
export class AsyncErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props)
        this.state = { hasError: false }
    }

    static getDerivedStateFromError(error: Error): State {
        return { hasError: true, error }
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error('AsyncErrorBoundary caught an error:', error, errorInfo)
        
        if (this.props.onError) {
            this.props.onError(error, errorInfo)
        }
    }

    componentDidMount() {
        // Listen for unhandled promise rejections
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection)
    }

    componentWillUnmount() {
        window.removeEventListener('unhandledrejection', this.handleUnhandledRejection)
    }

    handleUnhandledRejection = (event: PromiseRejectionEvent) => {
        console.error('Unhandled promise rejection:', event.reason)
        this.setState({ 
            hasError: true, 
            error: new Error(event.reason?.message || 'Unhandled promise rejection') 
        })
    }

    handleRetry = () => {
        this.setState({ hasError: false, error: undefined })
    }

    render() {
        if (this.state.hasError) {
            if (this.props.fallback) {
                return this.props.fallback
            }

            return (
                <div className="flex flex-col items-center justify-center min-h-[200px] p-8 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
                        <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        Async Operation Failed
                    </h3>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-6 max-w-md">
                        An asynchronous operation failed. This might be due to network issues or server problems.
                    </p>
                    
                    <Button 
                        onClick={this.handleRetry}
                        className="flex items-center gap-2"
                        variant="plain"
                    >
                        <RefreshCw className="w-4 h-4" />
                        Retry
                    </Button>
                </div>
            )
        }

        return this.props.children
    }
}
