'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { CalendarIcon, DownloadIcon, RefreshCwIcon } from 'lucide-react'

interface AnalyticHeaderProps {
    onRefresh?: () => void
    onExport?: () => void
}

const AnalyticHeader = ({ onRefresh, onExport }: AnalyticHeaderProps) => {
    const [liveVisitors, setLiveVisitors] = useState(0)
    const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
    const [isClient, setIsClient] = useState(false)

    useEffect(() => {
        setIsClient(true)

        // Simulate real-time visitor tracking
        const updateVisitors = () => {
            // Simulate visitor count between 15-45
            const newCount = Math.floor(Math.random() * 30) + 15
            setLiveVisitors(newCount)
            setLastUpdate(new Date())
        }

        // Initial load
        updateVisitors()

        // Update every 30 seconds
        const interval = setInterval(updateVisitors, 30000)

        return () => clearInterval(interval)
    }, [])

    const handleRefresh = () => {
        if (onRefresh) {
            onRefresh()
        }
        // Update live visitors immediately
        const newCount = Math.floor(Math.random() * 30) + 15
        setLiveVisitors(newCount)
        setLastUpdate(new Date())
    }

    return (
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-6">
            <div>
                <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
                <p className="text-muted-foreground">Monitor your website performance and user engagement</p>
            </div>
            
            <div className="flex items-center gap-4">
                {/* Live Visitors */}
                <Card className="p-1">
                    <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="text-sm font-medium">Live Visitors</span>
                        </div>
                        <span className="text-2xl font-bold text-green-600">{liveVisitors}</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1" suppressHydrationWarning>
                        Last updated: {isClient && lastUpdate ? lastUpdate.toLocaleTimeString() : 'Loading...'}
                    </p>
                </Card>

                {/* Action Buttons */}
                <div className="flex items-center gap-2">
                    <Button
                        variant="plain"
                        size="sm"
                        onClick={handleRefresh}
                        className="flex items-center gap-2"
                    >
                        <RefreshCwIcon className="h-4 w-4" />
                        Refresh
                    </Button>
                    
                    <Button
                        variant="plain"
                        size="sm"
                        onClick={onExport}
                        className="flex items-center gap-2"
                    >
                        <DownloadIcon className="h-4 w-4" />
                        Export
                    </Button>
                    
                    <Button
                        variant="plain"
                        size="sm"
                        className="flex items-center gap-2"
                    >
                        <CalendarIcon className="h-4 w-4" />
                        Date Range
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default AnalyticHeader
