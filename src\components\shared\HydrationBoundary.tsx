'use client'

import { useEffect, useState, ReactNode } from 'react'

interface HydrationBoundaryProps {
    children: ReactNode
    fallback?: ReactNode
    suppressHydrationWarning?: boolean
}

/**
 * React 19 compatible hydration boundary that prevents hydration mismatches
 * by ensuring server and client render identical content initially
 */
const HydrationBoundary = ({ 
    children, 
    fallback = null,
    suppressHydrationWarning = false 
}: HydrationBoundaryProps) => {
    const [isHydrated, setIsHydrated] = useState(false)

    useEffect(() => {
        // Mark as hydrated after client-side mount
        setIsHydrated(true)
    }, [])

    // During SSR and initial client render, show fallback or nothing
    if (!isHydrated) {
        return (
            <div suppressHydrationWarning={suppressHydrationWarning}>
                {fallback}
            </div>
        )
    }

    // After hydration, show actual content
    return <>{children}</>
}

export default HydrationBoundary
