'use client'

import { motion } from 'framer-motion'
import Button from '@/components/ui/Button'
import { TbArrowRight, TbChartLine } from 'react-icons/tb'
import HydrationBoundary from '@/components/shared/HydrationBoundary'

interface CompactCTAProps {
    title?: string
    description?: string
    primaryButtonText?: string
    secondaryButtonText?: string
    showSecondaryButton?: boolean
    className?: string
    variant?: 'default' | 'gradient' | 'minimal'
}

const CompactCTA = ({
    title = "Ready to Start Trading?",
    description = "Join thousands of successful traders and start trading with competitive spreads.",
    primaryButtonText = "Open Trading Account",
    secondaryButtonText = "Try Demo Account",
    showSecondaryButton = true,
    className = "",
    variant = 'default'
}: CompactCTAProps) => {

    const handleStartTrading = () => {
        if (typeof window !== 'undefined') {
            window.open('https://mbf.mybrokerforex.com/user/register', '_blank')
        }
    }

    const handleDemoAccount = () => {
        if (typeof window !== 'undefined') {
            window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')
        }
    }

    const getVariantClasses = () => {
        switch (variant) {
            case 'gradient':
                return 'bg-gradient-to-br from-[#EA5455] to-[#d63384] text-white'
            case 'minimal':
                return 'bg-gray-50 dark:bg-gray-800'
            default:
                return 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
        }
    }

    return (
        <HydrationBoundary
            fallback={<div className="h-48 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-2xl" />}
            suppressHydrationWarning
        >
            <section className={`py-12 ${className}`}>
                <div className="container mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className={`${getVariantClasses()} rounded-2xl p-8 text-center max-w-4xl mx-auto`}
                    >
                        <h2 className={`text-2xl md:text-3xl font-bold mb-4 ${
                            variant === 'gradient' ? 'text-white' : 'text-gray-900 dark:text-white'
                        }`}>
                            {title}
                        </h2>
                        <p className={`text-lg mb-8 max-w-2xl mx-auto ${
                            variant === 'gradient' 
                                ? 'text-white/90' 
                                : 'text-gray-600 dark:text-gray-300'
                        }`}>
                            {description}
                        </p>
                        
                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <Button
                                variant="solid"
                                size="lg"
                                onClick={handleStartTrading}
                                className={`${
                                    variant === 'gradient'
                                        ? 'bg-white text-[#EA5455] hover:bg-gray-100'
                                        : 'bg-[#EA5455] hover:bg-[#EA5455]/90 text-white'
                                } px-8 py-3`}
                                icon={<TbArrowRight />}
                                iconAlignment="end"
                            >
                                <span>{primaryButtonText}</span>
                            </Button>
                            
                            {showSecondaryButton && (
                                <Button
                                    variant="plain"
                                    size="lg"
                                    onClick={handleDemoAccount}
                                    className={`${
                                        variant === 'gradient'
                                            ? 'border-2 border-white text-white hover:bg-white hover:text-[#EA5455]'
                                            : 'border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white'
                                    } px-8 py-3`}
                                    icon={<TbChartLine />}
                                    iconAlignment="start"
                                >
                                    <span>{secondaryButtonText}</span>
                                </Button>
                            )}
                        </div>
                    </motion.div>
                </div>
            </section>
        </HydrationBoundary>
    )
}

export default CompactCTA
