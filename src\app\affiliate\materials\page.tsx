'use client'

import { motion } from 'framer-motion'
import { TbDownload, TbCopy, TbEye, TbPhoto, TbVideo, TbMail, TbBrandFacebook, TbBrandTwitter, TbBrandInstagram } from 'react-icons/tb'
import { useState } from 'react'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateMaterialsPage = () => {
  const [copiedText, setCopiedText] = useState('')

  const bannerSizes = [
    { name: 'Leaderboard', size: '728x90', preview: '/images/banners/728x90.jpg' },
    { name: 'Medium Rectangle', size: '300x250', preview: '/images/banners/300x250.jpg' },
    { name: 'Wide Skyscraper', size: '160x600', preview: '/images/banners/160x600.jpg' },
    { name: 'Large Rectangle', size: '336x280', preview: '/images/banners/336x280.jpg' },
    { name: 'Mobile Banner', size: '320x50', preview: '/images/banners/320x50.jpg' },
    { name: 'Square', size: '250x250', preview: '/images/banners/250x250.jpg' }
  ]

  const videoMaterials = [
    { title: 'Platform Overview', duration: '2:30', thumbnail: '/images/videos/platform-overview.jpg' },
    { title: 'Trading Tutorial', duration: '5:45', thumbnail: '/images/videos/trading-tutorial.jpg' },
    { title: 'Account Types', duration: '3:15', thumbnail: '/images/videos/account-types.jpg' },
    { title: 'Mobile Trading', duration: '4:20', thumbnail: '/images/videos/mobile-trading.jpg' }
  ]

  const emailTemplates = [
    { name: 'Welcome Email', subject: 'Start Trading with MyBrokerForex', opens: '24.5%' },
    { name: 'Platform Features', subject: 'Discover Advanced Trading Tools', opens: '18.2%' },
    { name: 'Account Benefits', subject: 'Unlock Premium Trading Benefits', opens: '21.8%' },
    { name: 'Market Analysis', subject: 'Weekly Market Insights & Opportunities', opens: '19.7%' }
  ]

  const socialMediaAssets = [
    { platform: 'Facebook', type: 'Post', size: '1200x630', icon: TbBrandFacebook },
    { platform: 'Twitter', type: 'Header', size: '1500x500', icon: TbBrandTwitter },
    { platform: 'Instagram', type: 'Story', size: '1080x1920', icon: TbBrandInstagram },
    { platform: 'LinkedIn', type: 'Post', size: '1200x627', icon: TbBrandFacebook }
  ]

  const trackingLinks = [
    { name: 'Homepage', url: 'https://mybrokerforex.com/?ref=YOUR_ID', clicks: 1247 },
    { name: 'Account Types', url: 'https://mybrokerforex.com/account-types?ref=YOUR_ID', clicks: 892 },
    { name: 'Trading Platform', url: 'https://mybrokerforex.com/trading?ref=YOUR_ID', clicks: 1156 },
    { name: 'Sign Up', url: 'https://mybrokerforex.com/sign-up?ref=YOUR_ID', clicks: 2341 }
  ]

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopiedText(text)
    setTimeout(() => setCopiedText(''), 2000)
  }

  return (
    <PageLayout>
      <div className="bg-gray-50 dark:bg-gray-900">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center text-white"
            >
              <h1 className="text-4xl text-gray-300 mt-8 md:text-6xl font-bold mb-6">
                Marketing Materials
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Professional marketing assets to help you promote MyBrokerForex effectively
              </p>
            </motion.div>
          </div>
        </section>

        {/* Banner Advertisements */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Banner Advertisements
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                High-converting banner ads in various sizes for your website or campaigns
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {bannerSizes.map((banner, index) => (
                <motion.div
                  key={banner.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden"
                >
                  <div className="aspect-video bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                    <TbPhoto className="w-12 h-12 text-gray-400" />
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {banner.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {banner.size} pixels
                    </p>
                    <div className="flex gap-2">
                      <button className="flex-1 bg-[#EA5455] text-white px-4 py-2 rounded-lg hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center gap-2">
                        <TbDownload className="w-4 h-4" />
                        Download
                      </button>
                      <button className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <TbEye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Video Materials */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Video Content
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Engaging video content to showcase our platform and services
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {videoMaterials.map((video, index) => (
                <motion.div
                  key={video.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gray-50 dark:bg-gray-900 rounded-xl overflow-hidden"
                >
                  <div className="aspect-video bg-gray-200 dark:bg-gray-700 flex items-center justify-center relative">
                    <TbVideo className="w-16 h-16 text-gray-400" />
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                      {video.duration}
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {video.title}
                    </h3>
                    <button className="w-full bg-[#EA5455] text-white px-4 py-2 rounded-lg hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center gap-2">
                      <TbDownload className="w-4 h-4" />
                      Download Video
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Email Templates */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Email Templates
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Ready-to-use email templates with proven conversion rates
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {emailTemplates.map((template, index) => (
                <motion.div
                  key={template.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-[#EA5455] bg-opacity-10 rounded-lg">
                        <TbMail className="w-5 h-5 text-[#EA5455]" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {template.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          Open rate: {template.opens}
                        </p>
                      </div>
                    </div>
                    <button className="text-[#EA5455] hover:text-[#d63384] transition-colors duration-200">
                      <TbDownload className="w-5 h-5" />
                    </button>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Subject: {template.subject}
                  </p>
                  <button className="w-full bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                    Preview Template
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Social Media Assets */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Social Media Assets
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Optimized graphics for all major social media platforms
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {socialMediaAssets.map((asset, index) => (
                <motion.div
                  key={`${asset.platform}-${asset.type}`}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gray-50 dark:bg-gray-900 rounded-xl p-6 text-center"
                >
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-[#EA5455] bg-opacity-10 rounded-xl">
                      <asset.icon className="w-8 h-8 text-[#EA5455]" />
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {asset.platform}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    {asset.type}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    {asset.size}
                  </p>
                  <button className="w-full bg-[#EA5455] text-white px-4 py-2 rounded-lg hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center gap-2">
                    <TbDownload className="w-4 h-4" />
                    Download
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Tracking Links */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Tracking Links
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Pre-configured tracking links for easy campaign management
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto">
              {trackingLinks.map((link, index) => (
                <motion.div
                  key={link.name}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-4"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {link.name}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 font-mono text-sm break-all">
                        {link.url}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                        {link.clicks.toLocaleString()} clicks
                      </p>
                    </div>
                    <button
                      onClick={() => copyToClipboard(link.url)}
                      className="ml-4 p-2 bg-[#EA5455] text-white rounded-lg hover:bg-[#d63384] transition-colors duration-200"
                    >
                      {copiedText === link.url ? (
                        <span className="text-sm">Copied!</span>
                      ) : (
                        <TbCopy className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}

export default AffiliateMaterialsPage
