const currentDate = new Date()

// Forex Analytics Data for Dashboard
export const forexAnalyticsData = {
    statisticData: {
        totalTradingVolume: {
            thisWeek: {
                value: 2182713,
                growShrink: 11.4,
                comparePeriod: 'from last week',
                chartData: {
                    series: [
                        {
                            name: 'Volume',
                            data: [24000, 28000, 21000, 34000, 30000, 44000, 25000],
                        },
                    ],
                    date: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                },
            },
            thisMonth: {
                value: 8237321,
                growShrink: 3.4,
                comparePeriod: 'from last month',
                chartData: {
                    series: [
                        {
                            name: 'Volume',
                            data: [
                                242000, 334000, 297000, 364000, 342000, 431000, 368000, 477000, 398000,
                                489000, 364000, 571000,
                            ],
                        },
                    ],
                    date: [
                        '01 Jun', '02 Jun', '03 Jun', '04 Jun', '05 Jun', '06 Jun',
                        '07 Jun', '08 Jun', '09 Jun', '10 Jun', '11 Jun', '12 Jun',
                    ],
                },
            },
        },
        totalTrades: {
            thisWeek: {
                value: 1782,
                growShrink: 9.2,
                comparePeriod: 'from last week',
                chartData: {
                    series: [
                        {
                            name: 'Trades',
                            data: [140, 180, 120, 240, 200, 270, 160],
                        },
                    ],
                    date: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                },
            },
            thisMonth: {
                value: 7234,
                growShrink: -2.8,
                comparePeriod: 'from last month',
                chartData: {
                    series: [
                        {
                            name: 'Trades',
                            data: [
                                1420, 1680, 1520, 1840, 1720, 1970, 1660, 2140, 1980,
                                2240, 2020, 2370,
                            ],
                        },
                    ],
                    date: [
                        '01 Jun', '02 Jun', '03 Jun', '04 Jun', '05 Jun', '06 Jun',
                        '07 Jun', '08 Jun', '09 Jun', '10 Jun', '11 Jun', '12 Jun',
                    ],
                },
            },
        },
        activeTraders: {
            thisWeek: {
                value: 892,
                growShrink: 15.2,
                comparePeriod: 'from last week',
                chartData: {
                    series: [
                        {
                            name: 'Traders',
                            data: [85, 92, 78, 105, 98, 112, 89],
                        },
                    ],
                    date: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                },
            },
            thisMonth: {
                value: 3456,
                growShrink: 8.7,
                comparePeriod: 'from last month',
                chartData: {
                    series: [
                        {
                            name: 'Traders',
                            data: [
                                320, 345, 298, 378, 356, 389, 367, 412, 398,
                                425, 401, 456,
                            ],
                        },
                    ],
                    date: [
                        '01 Jun', '02 Jun', '03 Jun', '04 Jun', '05 Jun', '06 Jun',
                        '07 Jun', '08 Jun', '09 Jun', '10 Jun', '11 Jun', '12 Jun',
                    ],
                },
            },
        },
    },
}

// Analytics Dashboard Data
export const analyticData = {
    thisMonth: {
        metrics: {
            visitors: {
                value: 34567,
                growShrink: 12.5,
            },
            conversionRate: {
                value: 3.2,
                growShrink: 8.1,
            },
            adCampaignClicks: {
                value: 8934,
                growShrink: -2.3,
            },
        },
        webAnalytic: {
            pageView: {
                value: 156789,
                growShrink: 15.2,
            },
            avgTimeOnPage: {
                value: '2:34',
                growShrink: 8.5,
            },
            series: [
                {
                    name: 'Page Views',
                    data: [12000, 15000, 13000, 18000, 16000, 22000, 19000, 25000, 21000, 28000, 24000, 30000]
                },
                {
                    name: 'Unique Visitors',
                    data: [8000, 10000, 9000, 12000, 11000, 15000, 13000, 17000, 14000, 19000, 16000, 21000]
                }
            ],
            date: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        },
        topPerformingPages: [
            {
                page: '/trading-platform',
                views: 12456,
                uniqueViews: 9876,
                bounceRate: 25.3,
            },
            {
                page: '/account-types',
                views: 9876,
                uniqueViews: 7654,
                bounceRate: 31.2,
            },
            {
                page: '/education-center',
                views: 8765,
                uniqueViews: 6543,
                bounceRate: 28.7,
            },
            {
                page: '/contact',
                views: 6543,
                uniqueViews: 4321,
                bounceRate: 45.1,
            }
        ],
        topChannel: {
            visitors: 34567,
            channels: [
                {
                    id: '1',
                    name: 'Organic Search',
                    img: '/img/avatars/thumb-1.jpg',
                    total: 15634,
                    percentage: 45.2
                },
                {
                    id: '2',
                    name: 'Direct',
                    img: '/img/avatars/thumb-2.jpg',
                    total: 9923,
                    percentage: 28.7
                },
                {
                    id: '3',
                    name: 'Social Media',
                    img: '/img/avatars/thumb-3.jpg',
                    total: 5634,
                    percentage: 16.3
                },
                {
                    id: '4',
                    name: 'Paid Ads',
                    img: '/img/avatars/thumb-4.jpg',
                    total: 3376,
                    percentage: 9.8
                }
            ]
        },
        deviceSession: {
            labels: ['Desktop', 'Mobile', 'Tablet'],
            series: [65, 28, 7],
            percentage: [65, 28, 7]
        }
    }
}

export default analyticData
