import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'

export async function POST(request: NextRequest) {
    try {
        // Create settings table
        await pool.query(`
            CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) UNIQUE NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `)

        // Insert default settings
        const defaultSettings = [
            ['site_name', 'MyBrokerForex'],
            ['site_url', 'https://mybrokerforex.com'],
            ['site_description', 'Your trusted partner in forex trading with competitive spreads and professional support.'],
            ['admin_email', '<EMAIL>'],
            ['support_email', '<EMAIL>'],
            ['site_logo', '/images/logo/logo-dark-full.png'],
            ['user_registration', '1'],
            ['email_verification', '1'],
            ['maintenance_mode', '0'],
            ['analytics_tracking', '1']
        ]

        for (const [key, value] of defaultSettings) {
            await pool.query(
                "INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)",
                [key, value]
            )
        }

        return NextResponse.json({ 
            success: true, 
            message: 'Settings table created and initialized successfully' 
        })

    } catch (error) {
        console.error('Settings table setup error:', error)
        return NextResponse.json({ 
            error: 'Failed to setup settings table',
            details: error instanceof Error ? error.message : String(error) 
        }, { status: 500 })
    }
}
