'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { PlusIcon, EditIcon, TrashIcon, LayoutIcon, CodeIcon, SettingsIcon, ToggleLeftIcon, ToggleRightIcon } from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

interface Widget {
    id: number
    name: string
    type: 'forex' | 'crypto' | 'calendar' | 'chart' | 'calculator'
    api_url: string | null
    options: any
    position: string | null
    is_active: boolean
    created_at: string
    updated_at: string
}

const WidgetsManagement = () => {
    const [widgets, setWidgets] = useState<Widget[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    // Fetch widgets from API
    const fetchWidgets = async () => {
        try {
            setLoading(true)
            const response = await fetch('/api/cms/widgets')
            const data = await response.json()
            
            if (data.success) {
                setWidgets(data.data)
            } else {
                setError(data.error || 'Failed to fetch widgets')
            }
        } catch (err) {
            setError('Failed to fetch widgets')
            console.error('Error fetching widgets:', err)
        } finally {
            setLoading(false)
        }
    }

    // Toggle widget active status
    const toggleActive = async (id: number, currentStatus: boolean) => {
        try {
            const response = await fetch('/api/cms/widgets', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id,
                    is_active: !currentStatus
                })
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Widget {!currentStatus ? 'activated' : 'deactivated'}
                    </Notification>
                )
                fetchWidgets() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to update widget'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    {'Failed to update widget'}
                </Notification>
            )
            console.error('Error updating widget:', err)
        }
    }

    // Delete widget
    const deleteWidget = async (id: number) => {
        if (!confirm('Are you sure you want to delete this widget?')) {
            return
        }

        try {
            const response = await fetch(`/api/cms/widgets?id=${id}`, {
                method: 'DELETE'
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                <Notification type="success" title="Success">
                    {'Widget deleted successfully'}
                </Notification>
            )
                fetchWidgets() // Refresh the list
            } else {
                toast.push(
                <Notification type="danger" title="Error">
                    {data.error || 'Failed to delete widget'}
                </Notification>
            )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    {'Failed to delete widget'}
                </Notification>
            )
            console.error('Error deleting widget:', err)
        }
    }

    // Get widget type icon and color
    const getWidgetTypeInfo = (type: string) => {
        switch (type) {
            case 'forex':
                return { icon: CodeIcon, color: 'bg-blue-100 text-blue-600' }
            case 'crypto':
                return { icon: CodeIcon, color: 'bg-purple-100 text-purple-600' }
            case 'calendar':
                return { icon: LayoutIcon, color: 'bg-green-100 text-green-600' }
            case 'chart':
                return { icon: LayoutIcon, color: 'bg-orange-100 text-orange-600' }
            case 'calculator':
                return { icon: LayoutIcon, color: 'bg-teal-100 text-teal-600' }
            default:
                return { icon: LayoutIcon, color: 'bg-gray-100 text-gray-600' }
        }
    }

    // Get widget type badge color
    const getTypeColor = (type: string) => {
        switch (type) {
            case 'forex':
                return 'bg-blue-100 text-blue-800'
            case 'crypto':
                return 'bg-purple-100 text-purple-800'
            case 'calendar':
                return 'bg-green-100 text-green-800'
            case 'chart':
                return 'bg-orange-100 text-orange-800'
            case 'calculator':
                return 'bg-teal-100 text-teal-800'
            default:
                return 'bg-gray-100 text-gray-800'
        }
    }

    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    }

    useEffect(() => {
        fetchWidgets()
    }, [])

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Widgets</h1>
                        <p className="text-muted-foreground">
                            Manage reusable website components and widgets
                        </p>
                    </div>
                </div>
                <Card>
                    <div className="text-center p-6">Loading widgets...</div>
                </Card>
            </div>
        )
    }

    if (error) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Widgets</h1>
                        <p className="text-muted-foreground">
                            Manage reusable website components and widgets
                        </p>
                    </div>
                </div>
                <Card>
                    <div className="text-center text-red-600 p-6">
                        Error: {error}
                        <br />
                        <Button onClick={fetchWidgets} className="mt-4">
                            Retry
                        </Button>
                    </div>
                </Card>
            </div>
        )
    }

    const activeWidgets = widgets.filter(w => w.is_active).length
    const inactiveWidgets = widgets.filter(w => !w.is_active).length

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Widgets</h1>
                    <p className="text-muted-foreground">
                        Manage reusable website components and widgets
                    </p>
                </div>
                <Button className="flex items-center gap-2">
                    <PlusIcon className="h-4 w-4" />
                    Create New Widget
                </Button>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                    <div className="text-center p-4">
                        <p className="text-2xl font-bold">{widgets.length}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Total Widgets</p>
                    </div>
                </Card>
                <Card>
                    <div className="text-center p-4">
                        <p className="text-2xl font-bold text-green-600">{activeWidgets}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Active Widgets</p>
                    </div>
                </Card>
                <Card>
                    <div className="text-center p-4">
                        <p className="text-2xl font-bold text-red-600">{inactiveWidgets}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Inactive Widgets</p>
                    </div>
                </Card>
            </div>

            <Card
                header={{
                    content: <h3 className="text-lg font-semibold">Available Widgets ({widgets.length})</h3>,
                    bordered: true
                }}
            >
                    {widgets.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-gray-600 dark:text-gray-400">No widgets found</p>
                            <Button className="mt-4">
                                <PlusIcon className="h-4 w-4 mr-2" />
                                Create Your First Widget
                            </Button>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {widgets.map((widget) => {
                                const typeInfo = getWidgetTypeInfo(widget.type)
                                const IconComponent = typeInfo.icon
                                
                                return (
                                    <div key={widget.id} className="border rounded-lg p-4 space-y-3">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <div className={`w-8 h-8 rounded flex items-center justify-center ${typeInfo.color}`}>
                                                    <IconComponent className="h-4 w-4" />
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-sm">{widget.name}</h3>
                                                    <p className="text-xs text-gray-600 dark:text-gray-400">{widget.position || 'No position'}</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Badge className={getTypeColor(widget.type)}>
                                                    {widget.type}
                                                </Badge>
                                                <Badge className={widget.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                                                    {widget.is_active ? 'Active' : 'Inactive'}
                                                </Badge>
                                            </div>
                                        </div>
                                        <p className="text-xs text-gray-600 dark:text-gray-400">
                                            Updated: {formatDate(widget.updated_at)}
                                        </p>
                                        <div className="flex items-center gap-2">
                                            <Button 
                                                variant="plain" 
                                                size="sm" 
                                                className="flex-1"
                                                title={widget.is_active ? 'Deactivate' : 'Activate'}
                                                onClick={() => toggleActive(widget.id, widget.is_active)}
                                            >
                                                {widget.is_active ? (
                                                    <ToggleRightIcon className="h-3 w-3 mr-1" />
                                                ) : (
                                                    <ToggleLeftIcon className="h-3 w-3 mr-1" />
                                                )}
                                                {widget.is_active ? 'Active' : 'Inactive'}
                                            </Button>
                                            <Button variant="plain" size="sm" title="Edit">
                                                <EditIcon className="h-3 w-3" />
                                            </Button>
                                            <Button 
                                                variant="plain" 
                                                size="sm" 
                                                title="Delete"
                                                onClick={() => deleteWidget(widget.id)}
                                            >
                                                <TrashIcon className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    )}
            </Card>
        </div>
    )
}

export default WidgetsManagement
