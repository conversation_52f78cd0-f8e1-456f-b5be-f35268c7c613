'use client'

import { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { TrendingUpIcon, TrendingDownIcon } from 'lucide-react'

interface CryptoPair {
    symbol: string
    name: string
    price: number
    change: number
    changePercent: number
    icon: string
}

interface CryptocurrencyWidgetProps {
    className?: string
}

const CryptocurrencyWidget = ({ className = '' }: CryptocurrencyWidgetProps) => {
    const [cryptoData, setCryptoData] = useState<CryptoPair[]>([
        { symbol: 'BTC/USD', name: 'Bitcoin', price: 43567.89, change: 1067.23, changePercent: 2.51, icon: '₿' },
        { symbol: 'ETH/USD', name: 'Ethereum', price: 2634.56, change: 46.78, changePercent: 1.81, icon: 'Ξ' },
        { symbol: 'XRP/USD', name: 'Ripple', price: 0.6234, change: -0.0055, changePercent: -0.87, icon: '◉' },
        { symbol: 'LTC/USD', name: '<PERSON><PERSON>oi<PERSON>', price: 73.45, change: 0.41, changePercent: 0.56, icon: 'Ł' },
        { symbol: 'ADA/USD', name: '<PERSON>ano', price: 0.4567, change: 0.0142, changePercent: 3.21, icon: '₳' },
        { symbol: 'DOT/USD', name: 'Polkadot', price: 6.78, change: -0.083, changePercent: -1.21, icon: '●' }
    ])

    const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
    const [isClient, setIsClient] = useState(false)

    useEffect(() => {
        setIsClient(true)
    }, [])

    const updatePrices = useCallback(() => {
        setCryptoData(prevData =>
            prevData.map(pair => {
                // Simulate realistic crypto price movements (higher volatility than forex)
                const volatility = 0.02 // 2% max change for crypto
                const randomChange = (Math.random() - 0.5) * 2 * volatility
                const newPrice = pair.price * (1 + randomChange)
                const priceChange = newPrice - pair.price
                const changePercent = (priceChange / pair.price) * 100

                return {
                    ...pair,
                    price: parseFloat(newPrice.toFixed(pair.symbol.includes('USD') && pair.price < 1 ? 4 : 2)),
                    change: parseFloat(priceChange.toFixed(pair.symbol.includes('USD') && pair.price < 1 ? 4 : 2)),
                    changePercent: parseFloat(changePercent.toFixed(2))
                }
            })
        )
        setLastUpdate(new Date())
    }, [])

    useEffect(() => {
        if (!isClient) return

        // Update every 30 seconds
        const interval = setInterval(updatePrices, 30000)
        return () => clearInterval(interval)
    }, [isClient, updatePrices])

    const formatPrice = useCallback((symbol: string, price: number) => {
        if (symbol.includes('USD') && price < 1) {
            return `$${price.toFixed(4)}`
        }
        return `$${price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    }, [])

    // Memoize major cryptos to prevent unnecessary re-renders
    const majorCryptos = useMemo(() => cryptoData.slice(0, 3), [cryptoData])
    const additionalCryptos = useMemo(() => cryptoData.slice(3), [cryptoData])

    return (
        <div className={`bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl p-6 text-gray-900 dark:text-gray-100 ${className}`} suppressHydrationWarning>
            <div className="flex items-center justify-between mb-6" suppressHydrationWarning>
                <div className="flex items-center gap-4" suppressHydrationWarning>
                    <div className="w-8 h-8 bg-gradient-to-r from-[#EA5455] to-[#28C76F] rounded-full flex items-center justify-center" suppressHydrationWarning>
                        <span className="text-white font-bold text-sm">₿</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Cryptocurrency Trading</h3>
                </div>
                <div className="flex items-center gap-2" suppressHydrationWarning>
                    <div className="w-2 h-2 bg-[#28C76F] rounded-full animate-pulse" suppressHydrationWarning></div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">Live Market • Updates every 30s</span>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4" suppressHydrationWarning>
                {/* Major Cryptos */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4" suppressHydrationWarning>
                    <h4 className="text-sm font-semibold mb-3 text-[#28C76F]">Major Cryptos</h4>
                    <div className="space-y-2" suppressHydrationWarning>
                        <div suppressHydrationWarning>
                            {majorCryptos.map((crypto) => (
                            <div key={crypto.symbol} className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                    <span className="text-lg">{crypto.icon}</span>
                                    <span className="text-sm text-gray-700 dark:text-gray-200">{crypto.symbol}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                                        {formatPrice(crypto.symbol, crypto.price)}
                                    </span>
                                    <div className="flex items-center gap-1">
                                        {crypto.changePercent > 0 ? (
                                            <TrendingUpIcon className="h-3 w-3 text-[#28C76F]" />
                                        ) : (
                                            <TrendingDownIcon className="h-3 w-3 text-[#EA5455]" />
                                        )}
                                        <span className={`text-xs font-medium ${
                                            crypto.changePercent > 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'
                                        }`}>
                                            {crypto.changePercent > 0 ? '+' : ''}{crypto.changePercent}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Additional Cryptos */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4" suppressHydrationWarning>
                    <h4 className="text-sm font-semibold mb-3 text-gray-900 dark:text-gray-100">Altcoins</h4>
                    <div className="space-y-2" suppressHydrationWarning>
                        {additionalCryptos.map((crypto) => (
                            <div key={crypto.symbol} className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                    <span className="text-lg">{crypto.icon}</span>
                                    <span className="text-sm text-gray-700 dark:text-gray-200">{crypto.symbol}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                                        {formatPrice(crypto.symbol, crypto.price)}
                                    </span>
                                    <div className="flex items-center gap-1">
                                        {crypto.changePercent > 0 ? (
                                            <TrendingUpIcon className="h-3 w-3 text-[#28C76F]" />
                                        ) : (
                                            <TrendingDownIcon className="h-3 w-3 text-[#EA5455]" />
                                        )}
                                        <span className={`text-xs font-medium ${
                                            crypto.changePercent > 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'
                                        }`}>
                                            {crypto.changePercent > 0 ? '+' : ''}{crypto.changePercent}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Quick Trade */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <h4 className="text-sm font-semibold mb-3 text-gray-900 dark:text-gray-100">Quick Trade</h4>
                    <div className="space-y-3">
                        <select className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-3 py-2 text-sm text-gray-900 dark:text-gray-100">
                            <option>BTC/USD</option>
                            <option>ETH/USD</option>
                            <option>XRP/USD</option>
                        </select>
                        <div className="grid grid-cols-2 gap-2">
                            <button className="bg-[#28C76F] hover:bg-[#28C76F]/80 text-white px-3 py-2 rounded text-sm font-semibold transition-colors">
                                BUY
                            </button>
                            <button className="bg-[#EA5455] hover:bg-[#EA5455]/80 text-white px-3 py-2 rounded text-sm font-semibold transition-colors">
                                SELL
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Last Update */}
            <div className="mt-4 text-center" suppressHydrationWarning>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                    Last updated: {isClient ? lastUpdate.toLocaleTimeString() : '--:--:--'}
                </span>
            </div>
        </div>
    )
}

export default memo(CryptocurrencyWidget)
