'use client'

import { ReactN<PERSON>, HTMLAttributes, ElementType } from 'react'

interface HydrationSafeWrapperProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode
    as?: ElementType
    className?: string
}

/**
 * A wrapper component that suppresses hydration warnings for elements
 * that may be affected by browser extensions adding attributes like bis_skin_checked
 */
const HydrationSafeWrapper = ({ 
    children, 
    as: Component = 'div', 
    className = '', 
    ...props 
}: HydrationSafeWrapperProps) => {
    return (
        <Component 
            className={className} 
            suppressHydrationWarning 
            {...props}
        >
            {children}
        </Component>
    )
}

export default HydrationSafeWrapper
