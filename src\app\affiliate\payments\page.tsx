'use client'

import { motion } from 'framer-motion'
import { TbCreditCard, TbBuildingBank, TbWallet, TbCalendar, TbTarget, TbShield } from 'react-icons/tb'
import PageLayout from '@/components/layout/PageLayout'

const AffiliatePaymentsPage = () => {
    const paymentMethods = [
        {
            icon: TbBuildingBank,
            title: 'Bank Transfer',
            description: 'Direct bank transfers to your account with secure processing.',
            features: ['Secure transfers', 'Global coverage', '2-3 business days', 'No fees for amounts over $500'],
            minAmount: '$100',
            processingTime: '2-3 business days'
        },
        {
            icon: TbCreditCard,
            title: 'Credit/Debit Card',
            description: 'Fast payments directly to your Visa or Mastercard.',
            features: ['Instant processing', 'Visa & Mastercard', 'Secure encryption', 'Available 24/7'],
            minAmount: '$50',
            processingTime: 'Instant'
        },
        {
            icon: TbWallet,
            title: 'Digital Wallets',
            description: 'Popular e-wallet solutions for quick and easy payments.',
            features: ['PayPal', 'Skrill', 'Neteller', 'WebMoney'],
            minAmount: '$25',
            processingTime: '1-2 hours'
        }
    ]

    const paymentSchedule = [
        { period: 'Weekly', description: 'Every Friday for commissions over $500', minAmount: '$500' },
        { period: 'Bi-weekly', description: 'Every 2 weeks for commissions over $250', minAmount: '$250' },
        { period: 'Monthly', description: 'End of month for all commission amounts', minAmount: '$50' }
    ]

    const paymentFeatures = [
        {
            icon: TbShield,
            title: 'Secure Processing',
            description: 'All payments are processed through secure, encrypted channels with fraud protection.'
        },
        {
            icon: TbCalendar,
            title: 'Flexible Schedule',
            description: 'Choose from weekly, bi-weekly, or monthly payment schedules based on your preferences.'
        },
        {
            icon: TbTarget,
            title: 'Low Minimums',
            description: 'Start receiving payments with low minimum thresholds across all payment methods.'
        }
    ]

    return (
        <PageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                Payment Methods
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Flexible and secure payment options for your affiliate commissions
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* Payment Methods */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Available Payment Methods
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Choose from multiple secure payment options that suit your needs
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {paymentMethods.map((method, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="text-center mb-6">
                                        <div className="w-16 h-16 bg-[#EA5455]/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                            <method.icon className="w-8 h-8 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                            {method.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300">
                                            {method.description}
                                        </p>
                                    </div>

                                    <div className="space-y-4 mb-6">
                                        <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                                            <span className="text-gray-600 dark:text-gray-300">Minimum Amount:</span>
                                            <span className="font-semibold text-gray-900 dark:text-white">{method.minAmount}</span>
                                        </div>
                                        <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                                            <span className="text-gray-600 dark:text-gray-300">Processing Time:</span>
                                            <span className="font-semibold text-gray-900 dark:text-white">{method.processingTime}</span>
                                        </div>
                                    </div>

                                    <ul className="space-y-2">
                                        {method.features.map((feature, featureIndex) => (
                                            <li key={featureIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                {feature}
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Payment Schedule */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Payment Schedule
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Flexible payment schedules to match your cash flow needs
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {paymentSchedule.map((schedule, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center"
                                >
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                                        {schedule.period}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        {schedule.description}
                                    </p>
                                    <div className="bg-[#EA5455] text-white px-4 py-2 rounded-lg font-semibold">
                                        Min: {schedule.minAmount}
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Payment Features */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Why Choose Our Payment System
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {paymentFeatures.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455]/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <feature.icon className="w-8 h-8 text-[#EA5455]" />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Payment Process */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                How Payments Work
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                            {[
                                { step: '1', title: 'Earn Commissions', description: 'Generate commissions through successful referrals' },
                                { step: '2', title: 'Reach Minimum', description: 'Accumulate commissions to meet minimum payment threshold' },
                                { step: '3', title: 'Payment Request', description: 'Automatic payment processing based on your schedule' },
                                { step: '4', title: 'Receive Funds', description: 'Get paid through your preferred payment method' }
                            ].map((item, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="text-center"
                                >
                                    <div className="w-12 h-12 bg-[#EA5455] text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                                        {item.step}
                                    </div>
                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                        {item.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {item.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-[#EA5455]">
                    <div className="max-w-7xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                Ready to Start Earning?
                            </h2>
                            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                Join our affiliate program and start receiving regular payments
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                Join Affiliate Program
                            </button>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PageLayout>
    )
}

export default AffiliatePaymentsPage
