'use client'

import { motion } from 'framer-motion'
import { Tb<PERSON><PERSON><PERSON>, Tb<PERSON>ash, TbChartLine, TbGift, Tb<PERSON><PERSON><PERSON><PERSON><PERSON>, TbTarget } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import PageLayout from '@/components/layout/PageLayout'

const AffiliatePage = () => {
  const router = useRouter()

  const programFeatures = [
    {
      id: 1,
      name: "High Commissions",
      description: "Earn up to $1,200 per referred client with our competitive commission structure",
      icon: TbCash,
      features: [
        "Up to $1,200 per client",
        "Revenue sharing options",
        "Performance bonuses",
        "Lifetime commissions",
        "No negative carry-over",
        "Monthly payments"
      ],
      color: "text-[#EA5455]",
      bgColor: "bg-[#EA5455]/10",
      href: "/affiliate/commissions"
    },
    {
      id: 2,
      name: "Marketing Support",
      description: "Professional marketing materials and dedicated support team",
      icon: TbTarget,
      features: [
        "Custom landing pages",
        "Banner advertisements",
        "Email templates",
        "Video content",
        "Social media assets",
        "Dedicated account manager"
      ],
      color: "text-[#28C76F]",
      bgColor: "bg-[#28C76F]/10",
      href: "/affiliate/materials"
    },
    {
      id: 3,
      name: "Real-time Tracking",
      description: "Advanced tracking system with detailed analytics and reporting",
      icon: TbChartLine,
      features: [
        "Real-time statistics",
        "Conversion tracking",
        "Click analytics",
        "Commission reports",
        "Client activity",
        "Performance metrics"
      ],
      color: "text-blue-500",
      bgColor: "bg-blue-500/10",
      href: "/affiliate/tracking"
    },
    {
      id: 4,
      name: "Multiple Programs",
      description: "Choose from various partnership programs that suit your business",
      icon: TbUserCheck,
      features: [
        "CPA (Cost Per Acquisition)",
        "Revenue Share",
        "Hybrid Programs",
        "Sub-affiliate Programs",
        "White Label Solutions",
        "Introducing Broker"
      ],
      color: "text-purple-500",
      bgColor: "bg-purple-500/10",
      href: "/affiliate/programs"
    },
    {
      id: 5,
      name: "Global Reach",
      description: "Promote to clients worldwide with multi-language support",
      icon: TbUsers,
      features: [
        "200+ countries supported",
        "Multi-language materials",
        "Local payment methods",
        "Regional compliance",
        "Cultural adaptation",
        "Time zone support"
      ],
      color: "text-orange-500",
      bgColor: "bg-orange-500/10",
      href: "/affiliate/global"
    },
    {
      id: 6,
      name: "Exclusive Benefits",
      description: "Special perks and bonuses for top-performing affiliates",
      icon: TbGift,
      features: [
        "VIP events access",
        "Higher commission tiers",
        "Priority support",
        "Exclusive promotions",
        "Performance rewards",
        "Recognition programs"
      ],
      color: "text-teal-500",
      bgColor: "bg-teal-500/10",
      href: "/affiliate/benefits"
    }
  ]

  const commissionTiers = [
    {
      tier: "Bronze",
      clients: "1-10 clients",
      commission: "$400",
      color: "text-amber-600",
      bgColor: "bg-amber-50 dark:bg-amber-900/20"
    },
    {
      tier: "Silver", 
      clients: "11-25 clients",
      commission: "$600",
      color: "text-gray-600",
      bgColor: "bg-gray-50 dark:bg-gray-900/20"
    },
    {
      tier: "Gold",
      clients: "26-50 clients", 
      commission: "$800",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50 dark:bg-yellow-900/20"
    },
    {
      tier: "Platinum",
      clients: "51+ clients",
      commission: "$1,200",
      color: "text-[#EA5455]",
      bgColor: "bg-[#EA5455]/10"
    }
  ]

  const handleJoinProgram = () => {
    window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')
  }

  const handleLearnMore = (href: string) => {
    router.push(href)
  }

  return (
      <PageLayout>
        {/* Hero Section */}
        <section className="relative py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div 
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Partner with <span className="text-[#EA5455]">MyBrokerForex</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
                Join our affiliate program and earn competitive commissions by referring clients 
                to our professional trading platform. Start earning today with our industry-leading partnership program.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={handleJoinProgram}
                  className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-all duration-300"
                >
                  Join Affiliate Program
                </button>
                <button
                  onClick={() => router.push('/affiliate/how-it-works')}
                  className="border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white px-5 py-2 rounded-lg font-semibold transition-all duration-300"
                >
                  How It Works
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Commission Tiers */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Commission Structure
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Our tiered commission structure rewards your success with higher payouts as you grow.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {commissionTiers.map((tier, index) => (
                <motion.div
                  key={tier.tier}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`${tier.bgColor} rounded-2xl p-6 text-center border border-gray-200 dark:border-gray-700`}
                >
                  <h3 className={`text-2xl font-bold mb-2 ${tier.color}`}>{tier.tier}</h3>
                  <p className="text-muted-foreground mb-4">{tier.clients}</p>
                  <div className={`text-3xl font-bold ${tier.color}`}>{tier.commission}</div>
                  <p className="text-sm text-muted-foreground mt-2">per qualified client</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Program Features Grid */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Why Choose Our Affiliate Program?
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                We provide everything you need to succeed as our affiliate partner.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {programFeatures.map((feature, index) => {
                const IconComponent = feature.icon
                return (
                  <motion.div
                    key={feature.id}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 group"
                  >
                    <div className={`w-16 h-16 ${feature.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className={`h-8 w-8 ${feature.color}`} />
                    </div>
                    
                    <h3 className="text-2xl font-bold mb-3">{feature.name}</h3>
                    <p className="text-muted-foreground mb-6">{feature.description}</p>
                    
                    <div className="space-y-2 mb-6">
                      {feature.features.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${feature.color.replace('text-', 'bg-')}`}></div>
                          <span className="text-sm text-muted-foreground">{item}</span>
                        </div>
                      ))}
                    </div>
                    
                    <button
                      onClick={() => handleLearnMore(feature.href)}
                      className={`w-full py-2 px-5 rounded-lg font-semibold transition-all duration-300 border-2 ${feature.color.replace('text-', 'border-')} ${feature.color} hover:${feature.color.replace('text-', 'bg-')} hover:text-white`}
                    >
                      Learn More
                    </button>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </section>
      </PageLayout>
  )
}

export default AffiliatePage
