'use client'

import { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { TrendingUpIcon, TrendingDownIcon } from 'lucide-react'

interface CurrencyPair {
    symbol: string
    price: number
    change: number
    changePercent: number
}

interface CurrencyWidgetProps {
    className?: string
}

const CurrencyWidget = ({ className = '' }: CurrencyWidgetProps) => {
    const [currencyData, setCurrencyData] = useState<CurrencyPair[]>([
        { symbol: 'EUR/USD', price: 1.0856, change: 0.0012, changePercent: 0.11 },
        { symbol: 'GBP/USD', price: 1.2634, change: -0.0023, changePercent: -0.18 },
        { symbol: 'USD/JPY', price: 149.82, change: 0.45, changePercent: 0.30 },
        { symbol: 'AUD/USD', price: 0.6789, change: 0.0034, changePercent: 0.50 },
        { symbol: 'USD/CAD', price: 1.3456, change: -0.0012, changePercent: -0.09 },
        { symbol: 'USD/CHF', price: 0.8923, change: 0.0018, changePercent: 0.20 }
    ])

    const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
    const [isClient, setIsClient] = useState(false)

    // Set client-side flag to prevent hydration mismatch
    useEffect(() => {
        setIsClient(true)
        setLastUpdate(new Date())
    }, [])

    const updatePrices = useCallback(() => {
        setCurrencyData(prevData =>
            prevData.map(pair => {
                // Simulate realistic price movements
                const volatility = 0.001 // 0.1% max change
                const randomChange = (Math.random() - 0.5) * 2 * volatility
                const newPrice = pair.price * (1 + randomChange)
                const priceChange = newPrice - pair.price
                const changePercent = (priceChange / pair.price) * 100

                return {
                    ...pair,
                    price: parseFloat(newPrice.toFixed(4)),
                    change: parseFloat(priceChange.toFixed(4)),
                    changePercent: parseFloat(changePercent.toFixed(2))
                }
            })
        )
        setLastUpdate(new Date())
    }, [])

    useEffect(() => {
        if (!isClient) return

        // Update every 30 seconds to reduce performance impact
        const interval = setInterval(updatePrices, 30000)
        return () => clearInterval(interval)
    }, [isClient, updatePrices])

    const formatPrice = useCallback((symbol: string, price: number) => {
        if (symbol.includes('JPY')) {
            return price.toFixed(2)
        }
        return price.toFixed(4)
    }, [])

    // Memoize major pairs to prevent unnecessary re-renders
    const majorPairs = useMemo(() => currencyData.slice(0, 3), [currencyData])
    const additionalPairs = useMemo(() => currencyData.slice(3), [currencyData])

    return (
        <div className={`bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl p-6 text-gray-900 dark:text-gray-100 ${className}`} suppressHydrationWarning>
            <div className="flex items-center justify-between mb-6" suppressHydrationWarning>
                <div className="flex items-center gap-4" suppressHydrationWarning>
                    <div className="w-8 h-8 bg-gradient-to-r from-[#EA5455] to-[#28C76F] rounded-full flex items-center justify-center" suppressHydrationWarning>
                        <span className="text-white font-bold text-sm">MB</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">MyBrokerForex Trading Platform</h3>
                </div>
                <div className="flex items-center gap-2" suppressHydrationWarning>
                    <div className="w-3 h-3 bg-[#28C76F] rounded-full"></div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">Live Market • Updates every 30s</span>
                </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" suppressHydrationWarning>
                {/* Major Pairs */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4" suppressHydrationWarning>
                    <h4 className="text-sm font-semibold mb-3 text-[#28C76F]">Major Pairs</h4>
                    <div className="space-y-2" suppressHydrationWarning>
                        <div suppressHydrationWarning>
                            {majorPairs.map((pair) => (
                            <div key={pair.symbol} className="flex justify-between items-center">
                                <span className="text-sm text-gray-700 dark:text-gray-200">{pair.symbol}</span>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-mono text-gray-900 dark:text-gray-100">
                                        {formatPrice(pair.symbol, pair.price)}
                                    </span>
                                    <div className="flex items-center gap-1">
                                        {pair.changePercent > 0 ? (
                                            <TrendingUpIcon className="h-3 w-3 text-[#28C76F]" />
                                        ) : (
                                            <TrendingDownIcon className="h-3 w-3 text-[#EA5455]" />
                                        )}
                                        <span className={`text-xs ${pair.changePercent > 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'}`}>
                                            {pair.changePercent > 0 ? '+' : ''}{pair.changePercent}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                        </div>
                    </div>
                </div>

                {/* Account Summary */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <h4 className="text-sm font-semibold mb-3 text-[#EA5455]">Account Summary</h4>
                    <div className="space-y-2">
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Balance</span>
                            <span className="text-sm font-mono text-gray-900 dark:text-gray-100">$10,000.00</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Equity</span>
                            <span className="text-sm font-mono text-[#28C76F]">$10,245.50</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">P&L</span>
                            <span className="text-sm font-mono text-[#28C76F]">+$245.50</span>
                        </div>
                    </div>
                </div>

                {/* Quick Trade */}
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <h4 className="text-sm font-semibold mb-3 text-gray-900 dark:text-gray-100">Quick Trade</h4>
                    <div className="space-y-3">
                        <select className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-3 py-2 text-sm text-gray-900 dark:text-gray-100">
                            <option>EUR/USD</option>
                            <option>GBP/USD</option>
                            <option>USD/JPY</option>
                        </select>
                        <div className="grid grid-cols-2 gap-2">
                            <button className="bg-[#28C76F] hover:bg-[#28C76F]/80 text-white px-3 py-2 rounded text-sm font-semibold transition-colors">
                                BUY
                            </button>
                            <button className="bg-[#EA5455] hover:bg-[#EA5455]/80 text-white px-3 py-2 rounded text-sm font-semibold transition-colors">
                                SELL
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Additional Pairs */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div>
                    <div className="grid grid-cols-3 gap-4 text-center">
                        {additionalPairs.map((pair) => (
                        <div key={pair.symbol} className="text-center">
                            <p className="text-xs text-gray-500 dark:text-gray-400">{pair.symbol}</p>
                            <p className="text-sm font-mono text-gray-900 dark:text-gray-100">
                                {formatPrice(pair.symbol, pair.price)}
                            </p>
                            <p className={`text-xs ${pair.changePercent > 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'}`}>
                                {pair.changePercent > 0 ? '+' : ''}{pair.changePercent}%
                            </p>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className="mt-4 text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                    Last updated: {isClient && lastUpdate ? lastUpdate.toLocaleTimeString() : '--:--:--'}
                </p>
            </div>
        </div>
    )
}

export default memo(CurrencyWidget)
