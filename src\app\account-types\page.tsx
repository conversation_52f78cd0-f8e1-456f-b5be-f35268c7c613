'use client'

import { motion } from 'framer-motion'
import { Tb<PERSON><PERSON><PERSON>, TbStar, TbShield, TbTrendingUp } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import PageLayout from '@/components/layout/PageLayout'

const AccountTypesPage = () => {
  const router = useRouter()

  const accountTypes = [
    {
      id: 1,
      name: "Standard",
      description: "Perfect for beginners and casual traders",
      minDeposit: "$100",
      spread: "From 1.2 pips",
      leverage: "1:100",
      commission: "No commission",
      execution: "Market Execution",
      features: [
        "50+ Currency Pairs",
        "Standard Spreads",
        "Basic Market Analysis",
        "Email Support",
        "Educational Resources",
        "Mobile Trading App",
        "Islamic Account Available",
        "Demo Account Included"
      ],
      popular: false,
      color: "border-gray-200 dark:border-gray-700",
      buttonStyle: "border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white"
    },
    {
      id: 2,
      name: "Professional",
      description: "For experienced traders seeking better conditions",
      minDeposit: "$1,000",
      spread: "From 0.8 pips",
      leverage: "1:200",
      commission: "No commission",
      execution: "Market Execution",
      features: [
        "70+ Currency Pairs",
        "Reduced Spreads",
        "Advanced Market Analysis",
        "Priority Support",
        "Trading Signals",
        "Risk Management Tools",
        "Economic Calendar",
        "VPS Hosting",
        "Personal Account Manager",
        "Advanced Charting Tools"
      ],
      popular: true,
      color: "border-[#EA5455] shadow-lg",
      buttonStyle: "bg-[#EA5455] hover:bg-[#EA5455]/90 text-white"
    },
    {
      id: 3,
      name: "VIP",
      description: "For high-volume and institutional traders",
      minDeposit: "$10,000",
      spread: "From 0.1 pips",
      leverage: "1:500",
      commission: "No commission",
      execution: "Market Execution",
      features: [
        "100+ Currency Pairs",
        "Ultra-tight Spreads",
        "Premium Market Analysis",
        "Dedicated Account Manager",
        "Custom Trading Solutions",
        "Institutional Liquidity",
        "Advanced Trading Tools",
        "24/7 Phone Support",
        "Priority Execution",
        "Custom Leverage Options",
        "Exclusive Market Insights",
        "White-label Solutions"
      ],
      popular: false,
      color: "border-gray-200 dark:border-gray-700",
      buttonStyle: "border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white"
    }
  ]

  const handleOpenAccount = (accountType: string) => {
    window.open(`https://mbf.mybrokerforex.com/user/register?account=${accountType.toLowerCase()}`, '_blank')
  }

  return (
    <div className="mt-20">
      <PageLayout>
        {/* Hero Section */}
        <section className="relative py-20">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Choose Your <span className="text-[#EA5455]">Account Type</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Select the trading account that best fits your experience level and trading goals.
                All accounts include our professional trading platform and expert support.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Account Types Grid */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {accountTypes.map((account, index) => (
                <motion.div
                  key={account.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`relative bg-white dark:bg-gray-800 rounded-2xl p-6 border-2 transition-all duration-300 hover:shadow-xl ${account.color}`}
                >
                  {account.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-[#EA5455] text-white px-4 py-1 rounded-full text-sm font-semibold flex items-center gap-1">
                        <TbStar className="h-4 w-4" />
                        Most Popular
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold mb-2">{account.name}</h3>
                    <p className="text-muted-foreground mb-4">{account.description}</p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Min Deposit:</span>
                        <span className="font-semibold text-[#EA5455]">{account.minDeposit}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Spreads:</span>
                        <span className="font-semibold">{account.spread}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Leverage:</span>
                        <span className="font-semibold">{account.leverage}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Commission:</span>
                        <span className="font-semibold">{account.commission}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Execution:</span>
                        <span className="font-semibold">{account.execution}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3 mb-8">
                    {account.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-3">
                        <TbCheck className="h-5 w-5 text-[#28C76F] flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={() => handleOpenAccount(account.name)}
                    className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 ${account.buttonStyle}`}
                  >
                    Open {account.name} Account
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Comparison Table */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Account Comparison</h2>
              <p className="text-xl text-muted-foreground">
                Compare features across all account types to find the perfect fit for your trading needs.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg"
            >
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold">Feature</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold">Standard</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold">Professional</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold">VIP</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium">Minimum Deposit</td>
                      <td className="px-6 py-4 text-sm text-center">$100</td>
                      <td className="px-6 py-4 text-sm text-center">$1,000</td>
                      <td className="px-6 py-4 text-sm text-center">$10,000</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium">Spreads</td>
                      <td className="px-6 py-4 text-sm text-center">From 1.2 pips</td>
                      <td className="px-6 py-4 text-sm text-center">From 0.8 pips</td>
                      <td className="px-6 py-4 text-sm text-center">From 0.1 pips</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium">Maximum Leverage</td>
                      <td className="px-6 py-4 text-sm text-center">1:100</td>
                      <td className="px-6 py-4 text-sm text-center">1:200</td>
                      <td className="px-6 py-4 text-sm text-center">1:500</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium">Currency Pairs</td>
                      <td className="px-6 py-4 text-sm text-center">50+</td>
                      <td className="px-6 py-4 text-sm text-center">70+</td>
                      <td className="px-6 py-4 text-sm text-center">100+</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium">Account Manager</td>
                      <td className="px-6 py-4 text-sm text-center">-</td>
                      <td className="px-6 py-4 text-sm text-center">Personal</td>
                      <td className="px-6 py-4 text-sm text-center">Dedicated</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium">Support</td>
                      <td className="px-6 py-4 text-sm text-center">Email</td>
                      <td className="px-6 py-4 text-sm text-center">Priority</td>
                      <td className="px-6 py-4 text-sm text-center">24/7 Phone</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium">Trading Signals</td>
                      <td className="px-6 py-4 text-sm text-center">-</td>
                      <td className="px-6 py-4 text-sm text-center">✓</td>
                      <td className="px-6 py-4 text-sm text-center">✓</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm font-medium">VPS Hosting</td>
                      <td className="px-6 py-4 text-sm text-center">-</td>
                      <td className="px-6 py-4 text-sm text-center">✓</td>
                      <td className="px-6 py-4 text-sm text-center">✓</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">All Accounts Include</h2>
              <p className="text-xl text-muted-foreground">
                Every account type comes with these essential features and benefits.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TbShield className="h-8 w-8 text-[#EA5455]" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Market News & Updates</h3>
                <p className="text-muted-foreground">
                  Stay ahead of the curve and make well-informed trading decisions with the latest news and insights available at your fingertips.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TbTrendingUp className="h-8 w-8 text-[#EA5455]" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Professional Platform</h3>
                <p className="text-muted-foreground">
                  Advanced trading platform with professional charting tools and technical indicators.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TbCheck className="h-8 w-8 text-[#EA5455]" />
                </div>
                <h3 className="text-xl font-semibold mb-3">No Hidden Fees</h3>
                <p className="text-muted-foreground">
                  Transparent pricing with no hidden fees, commissions, or charges on your trading.
                </p>
              </motion.div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-[#1E1E1E] text-white">
          <div className="container mx-auto px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Open Your Account?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Join thousands of successful traders who trust MyBrokerForex.
                Start with a demo account or open a live account today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                  className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
                >
                  Open Live Account
                </button>
                <button
                  onClick={() => window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')}
                  className="border-2 border-white text-white hover:bg-white hover:text-[#1E1E1E] px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
                >
                  Try Demo Account
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </PageLayout>
    </div>
  )
}

export default AccountTypesPage