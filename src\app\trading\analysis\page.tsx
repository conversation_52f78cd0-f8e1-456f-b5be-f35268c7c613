'use client'

import { motion } from 'framer-motion'
import { TbChartLine, TbTrendingUp, TbAnalyze, TbChartBar, TbChartCandle, TbTarget } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

const TradingAnalysisPage = () => {
    const analysisTypes = [
        {
            icon: TbChartLine,
            title: 'Technical Analysis',
            description: 'Chart patterns, indicators, and price action analysis for informed trading decisions.',
            features: ['Price Action Analysis', 'Chart Patterns', 'Technical Indicators', 'Support & Resistance']
        },
        {
            icon: TbTrendingUp,
            title: 'Fundamental Analysis',
            description: 'Economic data, news events, and market sentiment analysis.',
            features: ['Economic Indicators', 'Central Bank Policies', 'Market Sentiment', 'News Impact']
        },
        {
            icon: TbAnalyze,
            title: 'Market Research',
            description: 'In-depth market research and analysis reports from our expert team.',
            features: ['Daily Market Reports', 'Weekly Outlook', 'Monthly Analysis', 'Sector Reviews']
        },
        {
            icon: TbChartBar,
            title: 'Performance Analytics',
            description: 'Track and analyze your trading performance with detailed metrics.',
            features: ['Trade Analytics', 'Risk Metrics', 'Performance Reports', 'Portfolio Analysis']
        }
    ]

    const marketInsights = [
        {
            title: 'EUR/USD Technical Outlook',
            description: 'The EUR/USD pair shows strong bullish momentum above 1.0850 support level.',
            time: '2 hours ago',
            type: 'Technical'
        },
        {
            title: 'Fed Rate Decision Impact',
            description: 'Federal Reserve maintains rates, USD shows mixed reaction across major pairs.',
            time: '4 hours ago',
            type: 'Fundamental'
        },
        {
            title: 'Gold Price Analysis',
            description: 'Gold continues to find support at $1,950, potential for upside breakout.',
            time: '6 hours ago',
            type: 'Commodities'
        },
        {
            title: 'Weekly Market Wrap-up',
            description: 'Risk-on sentiment dominates as equity markets reach new highs.',
            time: '1 day ago',
            type: 'Market Review'
        }
    ]

    return (
        <div className="mt-0">
            <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl text-gray-300 mt-8 md:text-6xl font-bold mb-6">
                                    Market Analysis
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Professional market analysis and insights to enhance your trading decisions
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-[#EA5455] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#d63384] transition-colors duration-200"
                                >
                                    Start Trading Now
                                </button>
                            </motion.div>
                        </div>
                    </section>

                    {/* Analysis Types */}
                    <section className="py-20">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Analysis Types
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Comprehensive market analysis tools and insights for informed trading
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {analysisTypes.map((type, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                    >
                                        <div className="flex items-center mb-6">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                                <type.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                                {type.title}
                                            </h3>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                                            {type.description}
                                        </p>
                                        <ul className="space-y-2">
                                            {type.features.map((feature, featureIndex) => (
                                                <li key={featureIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                    <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                    {feature}
                                                </li>
                                            ))}
                                        </ul>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Latest Market Insights */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Latest Market Insights
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Stay updated with our latest market analysis and trading insights
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {marketInsights.map((insight, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-center justify-between mb-4">
                                            <span className="bg-[#EA5455] text-white px-3 py-1 rounded-full text-sm font-medium">
                                                {insight.type}
                                            </span>
                                            <span className="text-sm text-gray-500 dark:text-gray-400">
                                                {insight.time}
                                            </span>
                                        </div>
                                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                            {insight.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300">
                                            {insight.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-[#EA5455]">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Ready to Trade with Professional Analysis?
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                    Access our comprehensive market analysis and start trading with confidence
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                                >
                                    Open Trading Account
                                </button>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
        </div>
    )
}

export default TradingAnalysisPage
