'use client'

import { Card } from '@/components/ui/Card'
import { TrendingUpIcon, TrendingDownIcon, UsersIcon, MousePointerClickIcon, EyeIcon, ClockIcon } from 'lucide-react'
import type { MetricCardProps } from '../types'

const MetricCard = ({ 
    title, 
    value, 
    growShrink, 
    icon: Icon, 
    format = 'number' 
}: MetricCardProps) => {
    const formatValue = (val: number | string) => {
        if (format === 'percentage') return `${val}%`
        if (format === 'number' && typeof val === 'number') {
            return val.toLocaleString()
        }
        return val
    }

    return (
        <Card className="p-6">
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm text-muted-foreground">{title}</p>
                    <p className="text-2xl font-bold">{formatValue(value)}</p>
                    <div className="flex items-center mt-2">
                        {growShrink > 0 ? (
                            <TrendingUpIcon className="h-4 w-4 text-green-600 mr-1" />
                        ) : (
                            <TrendingDownIcon className="h-4 w-4 text-red-600 mr-1" />
                        )}
                        <span className={`text-sm ${growShrink > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {Math.abs(growShrink)}%
                        </span>
                    </div>
                </div>
                <div className="p-3 bg-primary/10 rounded-lg">
                    <Icon className="h-6 w-6 text-primary" />
                </div>
            </div>
        </Card>
    )
}

interface MetricsProps {
    data: {
        metrics: {
            visitors: { value: number; growShrink: number }
            conversionRate: { value: number; growShrink: number }
            adCampaignClicks: { value: number; growShrink: number }
        }
        webAnalytic: {
            pageView: { value: number; growShrink: number }
            avgTimeOnPage: { value: string; growShrink: number }
        }
    }
}

const Metrics = ({ data }: MetricsProps) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
                title="Total Visitors"
                value={data.metrics.visitors.value}
                growShrink={data.metrics.visitors.growShrink}
                icon={UsersIcon}
            />
            <MetricCard
                title="Conversion Rate"
                value={data.metrics.conversionRate.value}
                growShrink={data.metrics.conversionRate.growShrink}
                icon={TrendingUpIcon}
                format="percentage"
            />
            <MetricCard
                title="Ad Campaign Clicks"
                value={data.metrics.adCampaignClicks.value}
                growShrink={data.metrics.adCampaignClicks.growShrink}
                icon={MousePointerClickIcon}
            />
            <MetricCard
                title="Page Views"
                value={data.webAnalytic.pageView.value}
                growShrink={data.webAnalytic.pageView.growShrink}
                icon={EyeIcon}
            />
        </div>
    )
}

export default Metrics
