'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { HiArrowLeft, HiChartBar, HiArrowTrendingUp, HiArrowTrendingDown } from 'react-icons/hi2'
import { useRouter } from 'next/navigation'

interface VolatilityData {
    pair: string
    currentVolatility: number
    averageVolatility: number
    volatilityChange: number
    riskLevel: 'Low' | 'Medium' | 'High' | 'Extreme'
    dailyRange: number
    weeklyRange: number
    monthlyRange: number
}

const VolatilityTrackerPage = () => {
    const router = useRouter()
    const [selectedTimeframe, setSelectedTimeframe] = useState('daily')
    const [selectedCategory, setSelectedCategory] = useState('major')
    const [volatilityData, setVolatilityData] = useState<VolatilityData[]>([])
    const [isLoading, setIsLoading] = useState(true)

    const timeframes = [
        { id: 'daily', name: 'Daily', description: '24-hour volatility' },
        { id: 'weekly', name: 'Weekly', description: '7-day average' },
        { id: 'monthly', name: 'Monthly', description: '30-day average' }
    ]

    const categories = [
        { id: 'major', name: 'Major Pairs' },
        { id: 'minor', name: 'Minor Pairs' },
        { id: 'exotic', name: 'Exotic Pairs' },
        { id: 'commodities', name: 'Commodities' }
    ]

    const mockVolatilityData: Record<string, VolatilityData[]> = {
        major: [
            { pair: 'EUR/USD', currentVolatility: 0.65, averageVolatility: 0.72, volatilityChange: -0.07, riskLevel: 'Medium', dailyRange: 0.0085, weeklyRange: 0.0245, monthlyRange: 0.0680 },
            { pair: 'GBP/USD', currentVolatility: 0.89, averageVolatility: 0.85, volatilityChange: 0.04, riskLevel: 'High', dailyRange: 0.0125, weeklyRange: 0.0345, monthlyRange: 0.0890 },
            { pair: 'USD/JPY', currentVolatility: 0.58, averageVolatility: 0.62, volatilityChange: -0.04, riskLevel: 'Medium', dailyRange: 0.0095, weeklyRange: 0.0285, monthlyRange: 0.0620 },
            { pair: 'USD/CHF', currentVolatility: 0.52, averageVolatility: 0.55, volatilityChange: -0.03, riskLevel: 'Low', dailyRange: 0.0075, weeklyRange: 0.0215, monthlyRange: 0.0545 },
            { pair: 'AUD/USD', currentVolatility: 0.78, averageVolatility: 0.75, volatilityChange: 0.03, riskLevel: 'High', dailyRange: 0.0115, weeklyRange: 0.0325, monthlyRange: 0.0785 },
            { pair: 'USD/CAD', currentVolatility: 0.48, averageVolatility: 0.51, volatilityChange: -0.03, riskLevel: 'Low', dailyRange: 0.0065, weeklyRange: 0.0195, monthlyRange: 0.0485 }
        ],
        minor: [
            { pair: 'EUR/GBP', currentVolatility: 0.45, averageVolatility: 0.48, volatilityChange: -0.03, riskLevel: 'Low', dailyRange: 0.0055, weeklyRange: 0.0165, monthlyRange: 0.0445 },
            { pair: 'EUR/JPY', currentVolatility: 0.82, averageVolatility: 0.78, volatilityChange: 0.04, riskLevel: 'High', dailyRange: 0.0135, weeklyRange: 0.0385, monthlyRange: 0.0825 },
            { pair: 'GBP/JPY', currentVolatility: 1.15, averageVolatility: 1.08, volatilityChange: 0.07, riskLevel: 'Extreme', dailyRange: 0.0185, weeklyRange: 0.0525, monthlyRange: 0.1155 },
            { pair: 'AUD/JPY', currentVolatility: 0.95, averageVolatility: 0.92, volatilityChange: 0.03, riskLevel: 'High', dailyRange: 0.0155, weeklyRange: 0.0445, monthlyRange: 0.0955 }
        ],
        exotic: [
            { pair: 'USD/TRY', currentVolatility: 2.45, averageVolatility: 2.15, volatilityChange: 0.30, riskLevel: 'Extreme', dailyRange: 0.0485, weeklyRange: 0.1385, monthlyRange: 0.2455 },
            { pair: 'USD/ZAR', currentVolatility: 1.85, averageVolatility: 1.75, volatilityChange: 0.10, riskLevel: 'Extreme', dailyRange: 0.0365, weeklyRange: 0.1045, monthlyRange: 0.1855 },
            { pair: 'USD/MXN', currentVolatility: 1.25, averageVolatility: 1.18, volatilityChange: 0.07, riskLevel: 'High', dailyRange: 0.0245, weeklyRange: 0.0705, monthlyRange: 0.1255 }
        ],
        commodities: [
            { pair: 'XAU/USD', currentVolatility: 1.35, averageVolatility: 1.28, volatilityChange: 0.07, riskLevel: 'High', dailyRange: 0.0285, weeklyRange: 0.0815, monthlyRange: 0.1355 },
            { pair: 'XAG/USD', currentVolatility: 2.15, averageVolatility: 2.05, volatilityChange: 0.10, riskLevel: 'Extreme', dailyRange: 0.0425, weeklyRange: 0.1215, monthlyRange: 0.2155 },
            { pair: 'WTI/USD', currentVolatility: 1.95, averageVolatility: 1.88, volatilityChange: 0.07, riskLevel: 'Extreme', dailyRange: 0.0385, weeklyRange: 0.1105, monthlyRange: 0.1955 }
        ]
    }

    useEffect(() => {
        setIsLoading(true)
        const timer = setTimeout(() => {
            setVolatilityData(mockVolatilityData[selectedCategory])
            setIsLoading(false)
        }, 500)

        return () => clearTimeout(timer)
    }, [selectedCategory])



    const getRiskColor = (riskLevel: string) => {
        switch (riskLevel) {
            case 'Low': return 'text-[#28C76F] bg-[#28C76F]/10 dark:bg-[#28C76F]/20'
            case 'Medium': return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700'
            case 'High': return 'text-[#EA5455] bg-[#EA5455]/10 dark:bg-[#EA5455]/20'
            case 'Extreme': return 'text-[#EA5455] bg-[#EA5455]/20 dark:bg-[#EA5455]/30'
            default: return 'text-gray-600 bg-gray-50 dark:bg-gray-800'
        }
    }

    const getVolatilityTrend = (change: number) => {
        if (change > 0) return { icon: HiArrowTrendingUp, color: 'text-[#EA5455]', text: 'Increasing' }
        if (change < 0) return { icon: HiArrowTrendingDown, color: 'text-[#28C76F]', text: 'Decreasing' }
        return { icon: HiChartBar, color: 'text-gray-500', text: 'Stable' }
    }

    return (
        <PublicPageLayout>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black py-20 overflow-hidden">
                <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >

                        <h1 className="text-5xl text-gray-300 mt-8 font-bold mb-6">
                            Volatility Tracker
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Monitor market volatility across different currency pairs and timeframes. 
                            Essential for risk assessment and trading strategy optimization.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Volatility Tracker Section */}
            <section className="py-16 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-6">
                    {/* Controls */}
                    <div className="flex flex-wrap gap-4 mb-8">
                        {/* Category Selector */}
                        <div className="flex gap-2">
                            {categories.map((category) => (
                                <button
                                    key={category.id}
                                    onClick={() => setSelectedCategory(category.id)}
                                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                                        selectedCategory === category.id
                                            ? 'bg-[#EA5455] text-white'
                                            : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                                    }`}
                                >
                                    {category.name}
                                </button>
                            ))}
                        </div>

                        {/* Timeframe Selector */}
                        <div className="flex gap-2 ml-auto">
                            {timeframes.map((timeframe) => (
                                <button
                                    key={timeframe.id}
                                    onClick={() => setSelectedTimeframe(timeframe.id)}
                                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                                        selectedTimeframe === timeframe.id
                                            ? 'bg-[#28C76F] text-white'
                                            : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                                    }`}
                                >
                                    {timeframe.name}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Volatility Data */}
                    <motion.div
                        key={selectedCategory}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                        className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden"
                    >
                        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                                {categories.find(c => c.id === selectedCategory)?.name} - Volatility Analysis
                            </h2>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {timeframes.find(t => t.id === selectedTimeframe)?.description}
                            </p>
                        </div>

                        {isLoading ? (
                            <div className="p-12 text-center">
                                <div className="animate-spin w-8 h-8 border-2 border-[#EA5455] border-t-transparent rounded-full mx-auto mb-4"></div>
                                <p className="text-gray-500 dark:text-gray-400">Loading volatility data...</p>
                            </div>
                        ) : (
                            <div className="p-6">
                                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                                    {volatilityData.map((data, index) => {
                                        const trend = getVolatilityTrend(data.volatilityChange)
                                        const TrendIcon = trend.icon
                                        
                                        return (
                                            <motion.div
                                                key={data.pair}
                                                initial={{ opacity: 0, scale: 0.95 }}
                                                animate={{ opacity: 1, scale: 1 }}
                                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                                className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200"
                                            >
                                                <div className="flex items-center justify-between mb-4">
                                                    <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                                                        {data.pair}
                                                    </h3>
                                                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getRiskColor(data.riskLevel)}`}>
                                                        {data.riskLevel}
                                                    </span>
                                                </div>

                                                <div className="space-y-3">
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-sm text-gray-600 dark:text-gray-400">Current:</span>
                                                        <span className="font-semibold text-gray-900 dark:text-white">
                                                            {data.currentVolatility.toFixed(2)}%
                                                        </span>
                                                    </div>
                                                    
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-sm text-gray-600 dark:text-gray-400">Average:</span>
                                                        <span className="text-gray-700 dark:text-gray-300">
                                                            {data.averageVolatility.toFixed(2)}%
                                                        </span>
                                                    </div>

                                                    <div className="flex justify-between items-center">
                                                        <span className="text-sm text-gray-600 dark:text-gray-400">Trend:</span>
                                                        <div className={`flex items-center gap-1 ${trend.color}`}>
                                                            <TrendIcon className="w-4 h-4" />
                                                            <span className="text-sm font-medium">
                                                                {trend.text}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div className="pt-3 border-t border-gray-200 dark:border-gray-600">
                                                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                                                            {selectedTimeframe === 'daily' ? 'Daily Range' : 
                                                             selectedTimeframe === 'weekly' ? 'Weekly Range' : 'Monthly Range'}:
                                                        </div>
                                                        <div className="font-mono text-sm text-gray-900 dark:text-white">
                                                            {selectedTimeframe === 'daily' ? (data.dailyRange * 100).toFixed(2) :
                                                             selectedTimeframe === 'weekly' ? (data.weeklyRange * 100).toFixed(2) :
                                                             (data.monthlyRange * 100).toFixed(2)}%
                                                        </div>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        )
                                    })}
                                </div>
                            </div>
                        )}
                    </motion.div>

                    {/* Volatility Guide */}
                    <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                                Understanding Volatility
                            </h3>
                            <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                                <li>• <strong>Low (0-0.5%):</strong> Stable, predictable price movements</li>
                                <li>• <strong>Medium (0.5-1%):</strong> Moderate price fluctuations</li>
                                <li>• <strong>High (1-2%):</strong> Significant price swings</li>
                                <li>• <strong>Extreme (2%+):</strong> Very high risk, large movements</li>
                            </ul>
                        </div>

                        <div className="bg-[#EA5455]/10 dark:bg-[#EA5455]/20 border border-[#EA5455]/30 dark:border-[#EA5455]/40 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-[#EA5455] dark:text-[#EA5455] mb-3">
                                Trading with Volatility
                            </h3>
                            <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                                <li>• <strong>High Volatility:</strong> Larger stops, smaller position sizes</li>
                                <li>• <strong>Low Volatility:</strong> Tighter stops, larger positions</li>
                                <li>• <strong>Breakouts:</strong> Often occur during low volatility periods</li>
                                <li>• <strong>News Events:</strong> Can cause sudden volatility spikes</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </PublicPageLayout>
    )
}

export default VolatilityTrackerPage
