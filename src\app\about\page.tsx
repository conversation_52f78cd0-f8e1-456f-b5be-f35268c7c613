'use client'

import { motion } from 'framer-motion'
import { TbShield, TbT<PERSON>dingUp, Tb<PERSON>sers, TbAward, TbGlobe, TbHeadphones } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

const AboutPage = () => {
  const stats = [
    { label: "Years of Experience", value: "5+", icon: TbAward },
    { label: "Active Traders", value: "100K+", icon: TbUsers },
    { label: "Countries Served", value: "50+", icon: TbGlobe },
    { label: "Customer Satisfaction", value: "99.9%", icon: TbShield }
  ]

  const values = [
    {
      icon: TbShield,
      title: "Security & Trust",
      description: "We prioritize the security of your funds and personal information with bank-grade encryption and segregated accounts."
    },
    {
      icon: TbTrendingUp,
      title: "Innovation",
      description: "Continuously improving our platform with cutting-edge technology and advanced trading tools."
    },
    {
      icon: TbHeadphones,
      title: "Customer Support",
      description: "Providing exceptional 24/7 support to help you succeed in your trading journey."
    },
    {
      icon: TbUsers,
      title: "Community",
      description: "Building a strong community of traders who share knowledge and support each other."
    }
  ]

  return (
    <div className="mt-0">
      <PublicPageLayout>
        {/* Hero Section */}
        <section className="relative py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-4xl md:text-6xl mt-8 font-bold mb-6">
                Serving Since  <span className="text-[#EA5455]">2019</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                We've been dedicated to creating a responsible, profitable platform 
                that prioritizes our trades, offers exceptional customer support, 
                and provides reliable trading services.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="text-center"
                  >
                    <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-[#EA5455]" />
                    </div>
                    <div className="text-3xl font-bold text-[#EA5455] mb-2">{stat.value}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl md:text-4xl font-bold mb-6">Our Story</h2>
                <p className="text-muted-foreground mb-6">
                  Founded in 2009 by a team of experienced traders and financial technology experts,
                  MyBrokerForex was born from a simple vision: to democratize access to professional
                  forex trading tools and create a platform where traders of all levels could succeed.
                </p>
                <p className="text-muted-foreground mb-6">
                  Over the years, we've grown from a small startup to a globally recognized forex broker,
                  serving over 100,000 active traders across 50+ countries. Our commitment to innovation,
                  security, and customer satisfaction has made us a trusted name in the industry.
                </p>
                <p className="text-muted-foreground">
                  Today, we continue to push the boundaries of what's possible in forex trading,
                  constantly improving our platform and expanding our services to meet the evolving
                  needs of our global trading community.
                </p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-[#1E1E1E] rounded-2xl p-8 text-white"
              >
                <div className="text-2xl font-bold mb-6">Our Mission</div>
                <p className="text-gray-300 mb-6">
                  To empower traders worldwide with cutting-edge technology, competitive pricing,
                  and exceptional support, enabling them to achieve their financial goals through
                  professional forex trading.
                </p>
                <div className="text-2xl font-bold mb-6">Our Vision</div>
                <p className="text-gray-300">
                  To be the world's most trusted and innovative forex broker, setting new standards
                  for transparency, security, and customer experience in the financial markets.
                </p>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Our Values</h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                The principles that guide everything we do and shape our commitment to our traders.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {values.map((value, index) => {
                const IconComponent = value.icon
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white dark:bg-gray-900 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300"
                  >
                    <div className="w-12 h-12 bg-[#EA5455]/10 rounded-full flex items-center justify-center mb-4">
                      <IconComponent className="h-6 w-6 text-[#EA5455]" />
                    </div>
                    <h3 className="text-xl font-semibold mb-3">{value.title}</h3>
                    <p className="text-muted-foreground">{value.description}</p>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </section>

        {/* Regulation Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Your Trusted Partner in Forex Trading</h2>
              <p className="text-xl text-muted-foreground mb-12 max-w-3xl mx-auto">
                Your security is our priority. We are fully regulated and licensed by leading financial authorities.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
                  <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <TbShield className="h-8 w-8 text-[#EA5455]" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">FCA Regulated</h3>
                  <p className="text-muted-foreground">Licensed and regulated by the Financial Conduct Authority (FCA) in the UK.</p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
                  <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <TbShield className="h-8 w-8 text-[#EA5455]" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">Segregated Funds</h3>
                  <p className="text-muted-foreground">Client funds are held in segregated accounts with tier-1 banks for maximum security.</p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
                  <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <TbShield className="h-8 w-8 text-[#EA5455]" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">SSL Encryption</h3>
                  <p className="text-muted-foreground">Bank-grade SSL encryption protects all your personal and financial data.</p>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </PublicPageLayout>
    </div>
  )
}

export default AboutPage