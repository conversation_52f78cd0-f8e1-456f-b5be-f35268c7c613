'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { HiArrowLeft, HiClock, HiGlobeAlt } from 'react-icons/hi2'
import { useRouter } from 'next/navigation'

interface MarketSession {
    name: string
    city: string
    timezone: string
    openTime: string
    closeTime: string
    isOpen: boolean
    opensIn?: string
    closesIn?: string
    overlap?: string[]
}

const MarketHoursPage = () => {
    const router = useRouter()
    const [currentTime, setCurrentTime] = useState(new Date())
    const [selectedTimezone, setSelectedTimezone] = useState('UTC')
    const [sessions, setSessions] = useState<MarketSession[]>([])

    const timezones = [
        { value: 'UTC', label: 'UTC (GMT+0)' },
        { value: 'America/New_York', label: 'New York (EST/EDT)' },
        { value: 'Europe/London', label: 'London (GMT/BST)' },
        { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
        { value: 'Australia/Sydney', label: 'Sydney (AEST/AEDT)' }
    ]

    const marketSessions: MarketSession[] = [
        {
            name: 'Sydney Session',
            city: 'Sydney',
            timezone: 'Australia/Sydney',
            openTime: '07:00',
            closeTime: '16:00',
            isOpen: false,
            overlap: ['Tokyo']
        },
        {
            name: 'Tokyo Session',
            city: 'Tokyo',
            timezone: 'Asia/Tokyo',
            openTime: '09:00',
            closeTime: '18:00',
            isOpen: false,
            overlap: ['London']
        },
        {
            name: 'London Session',
            city: 'London',
            timezone: 'Europe/London',
            openTime: '08:00',
            closeTime: '17:00',
            isOpen: false,
            overlap: ['New York']
        },
        {
            name: 'New York Session',
            city: 'New York',
            timezone: 'America/New_York',
            openTime: '08:00',
            closeTime: '17:00',
            isOpen: false,
            overlap: []
        }
    ]

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date())
        }, 1000)

        return () => clearInterval(timer)
    }, [])

    useEffect(() => {
        updateMarketStatus()
    }, [currentTime, selectedTimezone])

    const updateMarketStatus = () => {
        const updatedSessions = marketSessions.map(session => {
            const now = new Date()
            const sessionOpen = new Date()
            const sessionClose = new Date()
            
            // Set open and close times for today
            const [openHour, openMinute] = session.openTime.split(':').map(Number)
            const [closeHour, closeMinute] = session.closeTime.split(':').map(Number)
            
            sessionOpen.setHours(openHour, openMinute, 0, 0)
            sessionClose.setHours(closeHour, closeMinute, 0, 0)
            
            // Check if market is currently open (simplified logic)
            const isWeekday = now.getDay() >= 1 && now.getDay() <= 5
            const currentHour = now.getHours()
            const isOpen = isWeekday && currentHour >= openHour && currentHour < closeHour
            
            return {
                ...session,
                isOpen
            }
        })
        
        setSessions(updatedSessions)
    }

    

    const formatTime = (time: Date, timezone: string) => {
        return time.toLocaleTimeString('en-US', {
            timeZone: timezone === 'UTC' ? 'UTC' : timezone,
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        })
    }

    const getSessionColor = (isOpen: boolean) => {
        return isOpen ? 'text-[#28C76F]' : 'text-gray-500 dark:text-gray-400'
    }

    const getSessionBg = (isOpen: boolean) => {
        return isOpen 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' 
            : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
    }

    return (
        <div className="mt-0">
            <PublicPageLayout>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black py-20 overflow-hidden">
                <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        <h1 className="text-5xl text-gray-300 mt-8 font-bold mb-6">
                            Global Market Hours
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Track global forex trading sessions and market hours across different time zones. 
                            Know when major financial centers are active for optimal trading opportunities.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Market Hours Section */}
            <section className="py-16 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-6">
                    {/* Current Time Display */}
                    <div className="text-center mb-12">
                        <div className="inline-flex items-center gap-3 bg-white dark:bg-gray-800 rounded-2xl px-8 py-4 shadow-xl">
                            <HiClock className="w-6 h-6 text-[#EA5455]" />
                            <div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">Current Time</div>
                                <div className="text-2xl font-mono font-bold text-gray-900 dark:text-white">
                                    {formatTime(currentTime, selectedTimezone)}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                    {timezones.find(tz => tz.value === selectedTimezone)?.label}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Timezone Selector */}
                    <div className="mb-8">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 text-center">
                            Select Your Timezone
                        </label>
                        <div className="flex justify-center">
                            <select
                                value={selectedTimezone}
                                onChange={(e) => setSelectedTimezone(e.target.value)}
                                className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            >
                                {timezones.map((tz) => (
                                    <option key={tz.value} value={tz.value}>
                                        {tz.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* Market Sessions Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        {sessions.map((session, index) => (
                            <motion.div
                                key={session.name}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                className={`border rounded-2xl p-6 ${getSessionBg(session.isOpen)}`}
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    <HiGlobeAlt className={`w-6 h-6 ${getSessionColor(session.isOpen)}`} />
                                    <div>
                                        <h3 className="font-bold text-gray-900 dark:text-white">
                                            {session.city}
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            {session.name}
                                        </p>
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                                        <span className={`font-semibold ${getSessionColor(session.isOpen)}`}>
                                            {session.isOpen ? 'OPEN' : 'CLOSED'}
                                        </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-gray-600 dark:text-gray-400">Hours:</span>
                                        <span className="text-sm font-mono text-gray-900 dark:text-white">
                                            {session.openTime} - {session.closeTime}
                                        </span>
                                    </div>
                                    {session.overlap && session.overlap.length > 0 && (
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-gray-600 dark:text-gray-400">Overlaps:</span>
                                            <span className="text-sm text-[#EA5455]">
                                                {session.overlap.join(', ')}
                                            </span>
                                        </div>
                                    )}
                                </div>

                                {session.isOpen && (
                                    <div className="mt-4 flex items-center gap-2">
                                        <div className="w-2 h-2 bg-[#28C76F] rounded-full animate-pulse"></div>
                                        <span className="text-xs text-[#28C76F] font-medium">
                                            Trading Active
                                        </span>
                                    </div>
                                )}
                            </motion.div>
                        ))}
                    </div>

                    {/* Market Overlap Information */}
                    <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                            Market Session Overlaps
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center p-6 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                                <h3 className="font-semibold text-blue-900 dark:text-blue-200 mb-2">
                                    Sydney + Tokyo
                                </h3>
                                <p className="text-sm text-blue-800 dark:text-blue-300 mb-2">
                                    02:00 - 04:00 UTC
                                </p>
                                <p className="text-xs text-blue-700 dark:text-blue-400">
                                    Moderate volatility, AUD/JPY active
                                </p>
                            </div>
                            <div className="text-center p-6 bg-green-50 dark:bg-green-900/20 rounded-xl">
                                <h3 className="font-semibold text-green-900 dark:text-green-200 mb-2">
                                    Tokyo + London
                                </h3>
                                <p className="text-sm text-green-800 dark:text-green-300 mb-2">
                                    08:00 - 09:00 UTC
                                </p>
                                <p className="text-xs text-green-700 dark:text-green-400">
                                    High volatility, EUR/JPY, GBP/JPY active
                                </p>
                            </div>
                            <div className="text-center p-6 bg-red-50 dark:bg-red-900/20 rounded-xl">
                                <h3 className="font-semibold text-red-900 dark:text-red-200 mb-2">
                                    London + New York
                                </h3>
                                <p className="text-sm text-red-800 dark:text-red-300 mb-2">
                                    13:00 - 17:00 UTC
                                </p>
                                <p className="text-xs text-red-700 dark:text-red-400">
                                    Highest volatility, all major pairs active
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Trading Tips */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-3">
                                Best Trading Times
                            </h3>
                            <ul className="text-blue-800 dark:text-blue-300 text-sm space-y-2">
                                <li>• <strong>London-NY Overlap:</strong> Highest liquidity and volatility</li>
                                <li>• <strong>Tokyo-London Overlap:</strong> Good for JPY pairs</li>
                                <li>• <strong>Major News Releases:</strong> Increased volatility expected</li>
                                <li>• <strong>Avoid Weekends:</strong> Markets closed, low liquidity</li>
                            </ul>
                        </div>

                        <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-orange-900 dark:text-orange-200 mb-3">
                                Market Characteristics
                            </h3>
                            <ul className="text-orange-800 dark:text-orange-300 text-sm space-y-2">
                                <li>• <strong>Sydney:</strong> Quiet start, AUD/NZD focus</li>
                                <li>• <strong>Tokyo:</strong> JPY pairs, Asian economic data</li>
                                <li>• <strong>London:</strong> EUR/GBP pairs, European data</li>
                                <li>• <strong>New York:</strong> USD pairs, US economic data</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
                    </PublicPageLayout>
        </div>
    )
}

export default MarketHoursPage
