import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

// GET /api/cms/widgets - Get all widgets
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const type = searchParams.get('type')
        const active = searchParams.get('active')
        const position = searchParams.get('position')
        
        let query = 'SELECT * FROM widgets WHERE 1=1'
        const params: any[] = []
        
        if (type) {
            query += ' AND type = ?'
            params.push(type)
        }
        
        if (active) {
            query += ' AND is_active = ?'
            params.push(active === 'true')
        }
        
        if (position) {
            query += ' AND position = ?'
            params.push(position)
        }
        
        query += ' ORDER BY created_at DESC'
        
        const [rows] = await pool.execute<RowDataPacket[]>(query, params)
        
        // Parse JSON options for each widget
        const widgets = rows.map(widget => ({
            ...widget,
            options: widget.options ? JSON.parse(widget.options) : null
        }))
        
        return NextResponse.json({
            success: true,
            data: widgets
        })
    } catch (error) {
        console.error('Error fetching widgets:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch widgets' },
            { status: 500 }
        )
    }
}

// POST /api/cms/widgets - Create new widget
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { name, type, api_url, options, position, is_active = true } = body
        
        if (!name || !type) {
            return NextResponse.json(
                { success: false, error: 'Name and type are required' },
                { status: 400 }
            )
        }
        
        // Validate widget type
        const validTypes = ['forex', 'crypto', 'calendar', 'chart', 'calculator']
        if (!validTypes.includes(type)) {
            return NextResponse.json(
                { success: false, error: 'Invalid widget type' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO widgets (name, type, api_url, options, position, is_active) VALUES (?, ?, ?, ?, ?, ?)',
            [name, type, api_url, JSON.stringify(options), position, is_active]
        )
        
        return NextResponse.json({
            success: true,
            data: { id: result.insertId, name, type, api_url, options, position, is_active }
        }, { status: 201 })
    } catch (error) {
        console.error('Error creating widget:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to create widget' },
            { status: 500 }
        )
    }
}

// PUT /api/cms/widgets - Update widget
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { id, name, type, api_url, options, position, is_active } = body
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Widget ID is required' },
                { status: 400 }
            )
        }
        
        // Validate widget type if provided
        if (type) {
            const validTypes = ['forex', 'crypto', 'calendar', 'chart', 'calculator']
            if (!validTypes.includes(type)) {
                return NextResponse.json(
                    { success: false, error: 'Invalid widget type' },
                    { status: 400 }
                )
            }
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE widgets SET name = ?, type = ?, api_url = ?, options = ?, position = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [name, type, api_url, JSON.stringify(options), position, is_active, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Widget not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { id, name, type, api_url, options, position, is_active }
        })
    } catch (error) {
        console.error('Error updating widget:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to update widget' },
            { status: 500 }
        )
    }
}

// DELETE /api/cms/widgets - Delete widget
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Widget ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM widgets WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Widget not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Widget deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting widget:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete widget' },
            { status: 500 }
        )
    }
}
