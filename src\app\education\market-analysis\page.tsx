'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbChartLine, TbTrendingUp, TbBrain, TbTarget, TbClock, TbBook, TbChartBar, TbAnalyze } from 'react-icons/tb'

const MarketAnalysisPage = () => {
    const analysisTypes = [
        {
            icon: TbChartLine,
            title: 'Technical Analysis',
            description: 'Master chart patterns, indicators, and price action analysis.',
            difficulty: 'Intermediate',
            duration: '3-4 hours',
            topics: ['Chart Patterns', 'Technical Indicators', 'Trend Analysis', 'Support & Resistance']
        },
        {
            icon: TbBrain,
            title: 'Fundamental Analysis',
            description: 'Understand economic factors that drive currency movements.',
            difficulty: 'Advanced',
            duration: '4-5 hours',
            topics: ['Economic Indicators', 'Central Bank Policies', 'Interest Rates', 'GDP & Inflation']
        },
        {
            icon: TbTrendingUp,
            title: 'Sentiment Analysis',
            description: 'Gauge market sentiment and positioning for better timing.',
            difficulty: 'Intermediate',
            duration: '2-3 hours',
            topics: ['COT Reports', 'Market Sentiment', 'Risk Appetite', 'News Impact']
        },
        {
            icon: TbTarget,
            title: 'Multi-Timeframe Analysis',
            description: 'Combine different timeframes for comprehensive market view.',
            difficulty: 'Advanced',
            duration: '3-4 hours',
            topics: ['Timeframe Correlation', 'Top-Down Analysis', 'Entry Timing', 'Confluence Zones']
        }
    ]

    const technicalTools = [
        {
            title: 'Moving Averages',
            description: 'Identify trends and dynamic support/resistance levels.',
            icon: TbTrendingUp
        },
        {
            title: 'RSI & Oscillators',
            description: 'Measure momentum and identify overbought/oversold conditions.',
            icon: TbChartBar
        },
        {
            title: 'Fibonacci Retracements',
            description: 'Find potential reversal levels using mathematical ratios.',
            icon: TbAnalyze
        },
        {
            title: 'Candlestick Patterns',
            description: 'Read price action through Japanese candlestick formations.',
            icon: TbChartLine
        }
    ]

    const fundamentalFactors = [
        {
            title: 'Interest Rate Decisions',
            description: 'Central bank rate changes significantly impact currency values.',
            impact: 'High'
        },
        {
            title: 'Employment Data',
            description: 'Job market health reflects economic strength and policy direction.',
            impact: 'High'
        },
        {
            title: 'GDP Growth',
            description: 'Economic growth rates indicate currency strength potential.',
            impact: 'Medium'
        },
        {
            title: 'Inflation Reports',
            description: 'Price stability measures influence monetary policy decisions.',
            impact: 'High'
        },
        {
            title: 'Political Events',
            description: 'Elections and policy changes create market uncertainty.',
            impact: 'Variable'
        },
        {
            title: 'Trade Balance',
            description: 'Import/export data shows economic competitiveness.',
            impact: 'Medium'
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Market Analysis
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Master technical and fundamental analysis to make informed trading decisions
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-colors duration-300"
                                >
                                    Start Learning Now
                                </button>
                            </motion.div>
                        </div>
                    </section>

                    {/* Analysis Types Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Types of Market Analysis
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Learn different approaches to analyzing financial markets and develop a comprehensive trading strategy.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {analysisTypes.map((type, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <type.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                                    {type.title}
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {type.description}
                                                </p>
                                                <div className="flex flex-wrap gap-2 mb-4">
                                                    <span className="px-3 py-1 bg-[#28C76F]/10 text-[#28C76F] rounded-full text-sm font-medium">
                                                        {type.difficulty}
                                                    </span>
                                                    <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium">
                                                        {type.duration}
                                                    </span>
                                                </div>
                                                <div className="space-y-2">
                                                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Key Topics:</h4>
                                                    <div className="flex flex-wrap gap-2">
                                                        {type.topics.map((topic, topicIndex) => (
                                                            <span key={topicIndex} className="px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-xs">
                                                                {topic}
                                                            </span>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Technical Tools Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Essential Technical Tools
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Master these fundamental technical analysis tools to enhance your trading decisions.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                {technicalTools.map((tool, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="w-12 h-12 bg-[#28C76F]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                                            <tool.icon className="w-6 h-6 text-[#28C76F]" />
                                        </div>
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                            {tool.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {tool.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Fundamental Factors Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Key Fundamental Factors
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Understand the economic events and data releases that drive currency movements.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {fundamentalFactors.map((factor, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start justify-between mb-4">
                                            <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                                                {factor.title}
                                            </h3>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                factor.impact === 'High'
                                                    ? 'bg-[#EA5455]/10 text-[#EA5455]'
                                                    : factor.impact === 'Medium'
                                                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                                                    : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
                                            }`}>
                                                {factor.impact}
                                            </span>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {factor.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#EA5455] to-[#EA5455]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Ready to Apply Your Analysis Skills?
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Put your market analysis knowledge to work with our advanced trading platform and comprehensive tools.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-white text-[#EA5455] px-5 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Open Trading Account
                                    </button>
                                    <button
                                        onClick={() => window.open('/education/demo-account', '_blank')}
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#EA5455] transition-colors duration-300"
                                    >
                                        Try Demo Account
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default MarketAnalysisPage
