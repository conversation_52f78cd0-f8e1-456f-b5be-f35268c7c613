'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { HiArrowLeft, HiArrowTrendingUp, HiArrowTrendingDown } from 'react-icons/hi2'
import { useRouter } from 'next/navigation'

interface CurrencyRate {
    pair: string
    bid: number
    ask: number
    spread: number
    change: number
    changePercent: number
    high: number
    low: number
    lastUpdate: string
}

const LiveRatesPage = () => {
    const router = useRouter()
    const [rates, setRates] = useState<CurrencyRate[]>([])
    const [selectedCategory, setSelectedCategory] = useState('major')
    const [isLoading, setIsLoading] = useState(true)

    const categories = [
        { id: 'major', name: 'Major Pairs', description: 'Most traded currency pairs' },
        { id: 'minor', name: 'Minor Pairs', description: 'Cross currency pairs' },
        { id: 'exotic', name: 'Exotic Pairs', description: 'Emerging market currencies' },
        { id: 'commodities', name: 'Commodities', description: 'Gold, Silver, Oil' }
    ]

    const mockRates: Record<string, CurrencyRate[]> = {
        major: [
            { pair: 'EUR/USD', bid: 1.0845, ask: 1.0847, spread: 0.2, change: 0.0012, changePercent: 0.11, high: 1.0865, low: 1.0820, lastUpdate: new Date().toISOString() },
            { pair: 'GBP/USD', bid: 1.2648, ask: 1.2651, spread: 0.3, change: -0.0023, changePercent: -0.18, high: 1.2680, low: 1.2635, lastUpdate: new Date().toISOString() },
            { pair: 'USD/JPY', bid: 149.45, ask: 149.48, spread: 0.3, change: 0.25, changePercent: 0.17, high: 149.85, low: 149.10, lastUpdate: new Date().toISOString() },
            { pair: 'USD/CHF', bid: 0.8945, ask: 0.8948, spread: 0.3, change: -0.0015, changePercent: -0.17, high: 0.8965, low: 0.8930, lastUpdate: new Date().toISOString() },
            { pair: 'AUD/USD', bid: 0.6748, ask: 0.6751, spread: 0.3, change: 0.0008, changePercent: 0.12, high: 0.6765, low: 0.6735, lastUpdate: new Date().toISOString() },
            { pair: 'USD/CAD', bid: 1.3445, ask: 1.3448, spread: 0.3, change: 0.0012, changePercent: 0.09, high: 1.3465, low: 1.3425, lastUpdate: new Date().toISOString() }
        ],
        minor: [
            { pair: 'EUR/GBP', bid: 0.8575, ask: 0.8578, spread: 0.3, change: 0.0005, changePercent: 0.06, high: 0.8590, low: 0.8565, lastUpdate: new Date().toISOString() },
            { pair: 'EUR/JPY', bid: 162.15, ask: 162.20, spread: 0.5, change: 0.35, changePercent: 0.22, high: 162.45, low: 161.80, lastUpdate: new Date().toISOString() },
            { pair: 'GBP/JPY', bid: 189.05, ask: 189.12, spread: 0.7, change: -0.25, changePercent: -0.13, high: 189.45, low: 188.75, lastUpdate: new Date().toISOString() },
            { pair: 'AUD/JPY', bid: 100.85, ask: 100.90, spread: 0.5, change: 0.15, changePercent: 0.15, high: 101.05, low: 100.65, lastUpdate: new Date().toISOString() }
        ],
        exotic: [
            { pair: 'USD/TRY', bid: 28.45, ask: 28.55, spread: 1.0, change: 0.25, changePercent: 0.88, high: 28.75, low: 28.20, lastUpdate: new Date().toISOString() },
            { pair: 'USD/ZAR', bid: 18.25, ask: 18.30, spread: 0.5, change: -0.15, changePercent: -0.82, high: 18.45, low: 18.15, lastUpdate: new Date().toISOString() },
            { pair: 'USD/MXN', bid: 17.85, ask: 17.90, spread: 0.5, change: 0.08, changePercent: 0.45, high: 17.95, low: 17.75, lastUpdate: new Date().toISOString() }
        ],
        commodities: [
            { pair: 'XAU/USD', bid: 2025.45, ask: 2026.25, spread: 0.8, change: 12.35, changePercent: 0.61, high: 2035.80, low: 2018.20, lastUpdate: new Date().toISOString() },
            { pair: 'XAG/USD', bid: 24.15, ask: 24.20, spread: 0.05, change: -0.25, changePercent: -1.03, high: 24.45, low: 24.05, lastUpdate: new Date().toISOString() },
            { pair: 'WTI/USD', bid: 78.45, ask: 78.55, spread: 0.1, change: 1.25, changePercent: 1.62, high: 79.15, low: 77.85, lastUpdate: new Date().toISOString() }
        ]
    }

    useEffect(() => {
        // Simulate loading and data fetching
        setIsLoading(true)
        const timer = setTimeout(() => {
            setRates(mockRates[selectedCategory])
            setIsLoading(false)
        }, 500)

        return () => clearTimeout(timer)
    }, [selectedCategory])

    // Simulate real-time updates
    useEffect(() => {
        const interval = setInterval(() => {
            setRates(prevRates => 
                prevRates.map(rate => ({
                    ...rate,
                    bid: rate.bid + (Math.random() - 0.5) * 0.001,
                    ask: rate.ask + (Math.random() - 0.5) * 0.001,
                    change: rate.change + (Math.random() - 0.5) * 0.0005,
                    changePercent: rate.changePercent + (Math.random() - 0.5) * 0.05,
                    lastUpdate: new Date().toISOString()
                }))
            )
        }, 3000)

        return () => clearInterval(interval)
    }, [])

    

    const formatPrice = (price: number, pair: string) => {
        if (pair.includes('JPY')) return price.toFixed(2)
        if (pair.includes('XAU') || pair.includes('WTI')) return price.toFixed(2)
        return price.toFixed(4)
    }

    return (
        <PublicPageLayout>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black py-20 overflow-hidden">
                <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        <h1 className="text-5xl text-white mt-8 font-bold mb-6">
                            Live Market Rates
                        </h1>
                        <p className="text-xl text-gray-100 mb-8 max-w-3xl mx-auto">
                            Real-time currency exchange rates, spreads, and market data. 
                            Stay updated with live pricing for major, minor, and exotic currency pairs.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Market Data Section */}
            <section className="py-16 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-6">
                    {/* Category Tabs */}
                    <div className="flex flex-wrap gap-4 mb-8">
                        {categories.map((category) => (
                            <button
                                key={category.id}
                                onClick={() => setSelectedCategory(category.id)}
                                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                                    selectedCategory === category.id
                                        ? 'bg-[#EA5455] text-white shadow-lg'
                                        : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                                }`}
                            >
                                <div className="text-left">
                                    <div className="font-semibold">{category.name}</div>
                                    <div className="text-xs opacity-75">{category.description}</div>
                                </div>
                            </button>
                        ))}
                    </div>

                    {/* Live Rates Table */}
                    <motion.div
                        key={selectedCategory}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                        className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden"
                    >
                        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                            <div className="flex items-center justify-between">
                                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                                    {categories.find(c => c.id === selectedCategory)?.name} - Live Rates
                                </h2>
                                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                                    <div className="w-2 h-2 bg-[#28C76F] rounded-full animate-pulse"></div>
                                    Live Updates
                                </div>
                            </div>
                        </div>

                        {isLoading ? (
                            <div className="p-12 text-center">
                                <div className="animate-spin w-8 h-8 border-2 border-[#EA5455] border-t-transparent rounded-full mx-auto mb-4"></div>
                                <p className="text-gray-500 dark:text-gray-400">Loading market data...</p>
                            </div>
                        ) : (
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Instrument
                                            </th>
                                            <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Bid
                                            </th>
                                            <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Ask
                                            </th>
                                            <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Spread
                                            </th>
                                            <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Change
                                            </th>
                                            <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                High/Low
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                                        {rates.map((rate, index) => (
                                            <motion.tr
                                                key={rate.pair}
                                                initial={{ opacity: 0, x: -20 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                                className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                                            >
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="font-semibold text-gray-900 dark:text-white">
                                                        {rate.pair}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right font-mono text-gray-900 dark:text-white">
                                                    {formatPrice(rate.bid, rate.pair)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right font-mono text-gray-900 dark:text-white">
                                                    {formatPrice(rate.ask, rate.pair)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-gray-600 dark:text-gray-400">
                                                    {rate.spread.toFixed(1)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right">
                                                    <div className={`flex items-center justify-end gap-1 ${
                                                        rate.change >= 0 ? 'text-[#28C76F]' : 'text-[#EA5455]'
                                                    }`}>
                                                        {rate.change >= 0 ? (
                                                            <HiArrowTrendingUp className="w-4 h-4" />
                                                        ) : (
                                                            <HiArrowTrendingDown className="w-4 h-4" />
                                                        )}
                                                        <span className="font-mono">
                                                            {rate.change >= 0 ? '+' : ''}{formatPrice(rate.change, rate.pair)}
                                                        </span>
                                                        <span className="text-xs">
                                                            ({rate.changePercent >= 0 ? '+' : ''}{rate.changePercent.toFixed(2)}%)
                                                        </span>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-600 dark:text-gray-400">
                                                    <div>{formatPrice(rate.high, rate.pair)}</div>
                                                    <div>{formatPrice(rate.low, rate.pair)}</div>
                                                </td>
                                            </motion.tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </motion.div>

                    {/* Market Info */}
                    <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                                Market Hours
                            </h3>
                            <p className="text-gray-700 dark:text-gray-300 text-sm">
                                Forex markets are open 24/7, from Sunday 5 PM EST to Friday 5 PM EST.
                                Major trading sessions: London, New York, Tokyo, Sydney.
                            </p>
                        </div>

                        <div className="bg-[#28C76F]/10 dark:bg-[#28C76F]/20 border border-[#28C76F]/30 dark:border-[#28C76F]/40 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-[#28C76F] dark:text-[#28C76F] mb-3">
                                Spread Information
                            </h3>
                            <p className="text-gray-700 dark:text-gray-300 text-sm">
                                Spreads shown are indicative and may vary based on market conditions,
                                volatility, and liquidity. Lower spreads typically occur during major trading sessions.
                            </p>
                        </div>

                        <div className="bg-[#EA5455]/10 dark:bg-[#EA5455]/20 border border-[#EA5455]/30 dark:border-[#EA5455]/40 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-[#EA5455] dark:text-[#EA5455] mb-3">
                                Risk Warning
                            </h3>
                            <p className="text-gray-700 dark:text-gray-300 text-sm">
                                Forex trading involves significant risk. Prices can change rapidly due to
                                economic events, news, and market sentiment. Always use proper risk management.
                            </p>
                        </div>
                    </div>
                </div>
            </section>
                    </PublicPageLayout>
    )
}

export default LiveRatesPage
