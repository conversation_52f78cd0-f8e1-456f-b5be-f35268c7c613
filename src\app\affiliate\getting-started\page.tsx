'use client'

import { motion } from 'framer-motion'
import { TbUserPlus, TbLink, TbShare, TbCash, TbTarget, TbChartLine } from 'react-icons/tb'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateGettingStartedPage = () => {
    const gettingStartedSteps = [
        {
            step: '1',
            icon: TbUserPlus,
            title: 'Sign Up',
            description: 'Create your affiliate account with MyBrokerForex',
            details: [
                'Complete the registration form',
                'Verify your email address',
                'Submit required documentation',
                'Wait for account approval'
            ]
        },
        {
            step: '2',
            icon: TbLink,
            title: 'Get Your Links',
            description: 'Generate unique tracking links for your campaigns',
            details: [
                'Access your affiliate dashboard',
                'Create custom tracking links',
                'Set up campaign parameters',
                'Download marketing materials'
            ]
        },
        {
            step: '3',
            icon: TbShare,
            title: 'Start Promoting',
            description: 'Share your links and start referring clients',
            details: [
                'Share on social media',
                'Add to your website',
                'Include in email campaigns',
                'Use in paid advertising'
            ]
        },
        {
            step: '4',
            icon: TbCash,
            title: 'Earn Commissions',
            description: 'Get paid for successful referrals',
            details: [
                'Track your performance',
                'Monitor client activity',
                'Receive regular payments',
                'Optimize your campaigns'
            ]
        }
    ]

    const requirements = [
        {
            title: 'Age Requirement',
            description: 'Must be 18 years or older (or legal age in your jurisdiction)'
        },
        {
            title: 'Marketing Channel',
            description: 'Have a legitimate website, blog, or social media presence'
        },
        {
            title: 'Compliance',
            description: 'Agree to follow all marketing guidelines and regulations'
        },
        {
            title: 'Documentation',
            description: 'Provide valid identification and business documentation if applicable'
        }
    ]

    const tips = [
        {
            icon: TbTarget,
            title: 'Know Your Audience',
            description: 'Understand who your audience is and tailor your content to their interests in forex trading.'
        },
        {
            icon: TbChartLine,
            title: 'Provide Value',
            description: 'Create educational content about forex trading to build trust with your audience.'
        },
        {
            icon: TbShare,
            title: 'Use Multiple Channels',
            description: 'Diversify your promotion across different platforms and marketing channels.'
        }
    ]

    return (
        <PageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                Getting Started
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Your complete guide to becoming a successful MyBrokerForex affiliate
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                className="bg-[#EA5455] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#d63384] transition-colors duration-200"
                            >
                                Start Your Journey
                            </button>
                        </motion.div>
                    </div>
                </section>

                {/* Getting Started Steps */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                How to Get Started
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Follow these simple steps to become a MyBrokerForex affiliate
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {gettingStartedSteps.map((step, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center mb-6">
                                        <div className="w-12 h-12 bg-[#EA5455] text-white rounded-full flex items-center justify-center mr-4 font-bold text-lg">
                                            {step.step}
                                        </div>
                                        <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                            <step.icon className="w-6 h-6 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {step.title}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {step.description}
                                    </p>
                                    <ul className="space-y-2">
                                        {step.details.map((detail, detailIndex) => (
                                            <li key={detailIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                {detail}
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Requirements */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Eligibility Requirements
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Make sure you meet these requirements before applying
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {requirements.map((requirement, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6"
                                >
                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                        {requirement.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {requirement.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Success Tips */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Tips for Success
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Proven strategies to maximize your affiliate earnings
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {tips.map((tip, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455]/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <tip.icon className="w-8 h-8 text-[#EA5455]" />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        {tip.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {tip.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Quick Start Checklist */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-4xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Quick Start Checklist
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Everything you need to do to get started as an affiliate
                            </p>
                        </motion.div>

                        <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-8">
                            <div className="space-y-4">
                                {[
                                    'Complete affiliate registration form',
                                    'Verify your email address',
                                    'Submit required identification documents',
                                    'Wait for account approval (usually 24-48 hours)',
                                    'Access your affiliate dashboard',
                                    'Generate your first tracking links',
                                    'Download marketing materials',
                                    'Set up your payment method',
                                    'Start promoting MyBrokerForex',
                                    'Monitor your performance and optimize'
                                ].map((item, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, x: -30 }}
                                        whileInView={{ opacity: 1, x: 0 }}
                                        transition={{ duration: 0.4, delay: index * 0.1 }}
                                        className="flex items-center"
                                    >
                                        <div className="w-6 h-6 bg-[#EA5455] text-white rounded-full flex items-center justify-center mr-4 text-sm font-bold">
                                            {index + 1}
                                        </div>
                                        <span className="text-gray-900 dark:text-white">
                                            {item}
                                        </span>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Resources */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Helpful Resources
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Additional resources to help you succeed as an affiliate
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {[
                                { title: 'Marketing Materials', description: 'Banners, logos, and promotional content', link: '/affiliate/materials' },
                                { title: 'Commission Structure', description: 'Detailed breakdown of earning potential', link: '/affiliate/commissions' },
                                { title: 'Tracking Links', description: 'Generate and manage your affiliate links', link: '/affiliate/tracking' },
                                { title: 'Performance Reports', description: 'Monitor your affiliate performance', link: '/affiliate/reports' }
                            ].map((resource, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                        {resource.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        {resource.description}
                                    </p>
                                    <button 
                                        onClick={() => window.location.href = resource.link}
                                        className="text-[#EA5455] hover:text-[#d63384] font-medium transition-colors duration-200"
                                    >
                                        Learn More →
                                    </button>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-[#EA5455]">
                    <div className="max-w-7xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                Ready to Get Started?
                            </h2>
                            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                Join thousands of successful affiliates earning with MyBrokerForex
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                Join Affiliate Program
                            </button>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PageLayout>
    )
}

export default AffiliateGettingStartedPage
