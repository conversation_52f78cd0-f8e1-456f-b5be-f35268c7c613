'use client'

import { useState, useEffect } from 'react'

export interface Testimonial {
    id: number
    client_name: string
    quote: string
    image?: string
    rating: number
    company?: string
    position?: string
    is_featured: boolean
    is_active: boolean
    created_at: string
}

interface UseTestimonialsResult {
    testimonials: Testimonial[]
    loading: boolean
    error: string | null
    refetch: () => void
}

export const useTestimonials = (
    activeOnly: boolean = true, 
    featuredOnly: boolean = false
): UseTestimonialsResult => {
    const [testimonials, setTestimonials] = useState<Testimonial[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    const fetchTestimonials = async () => {
        try {
            setLoading(true)
            setError(null)
            
            const params = new URLSearchParams()
            if (activeOnly) params.append('active', 'true')
            if (featuredOnly) params.append('featured', 'true')
            
            const url = `/api/cms/testimonials?${params.toString()}`
            
            const response = await fetch(url)
            const data = await response.json()
            
            if (data.success) {
                setTestimonials(data.data)
            } else {
                setError(data.error || 'Failed to fetch testimonials')
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchTestimonials()
    }, [activeOnly, featuredOnly])

    return {
        testimonials,
        loading,
        error,
        refetch: fetchTestimonials
    }
}
