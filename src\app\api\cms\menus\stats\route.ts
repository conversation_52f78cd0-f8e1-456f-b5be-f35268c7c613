import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket } from 'mysql2'

// GET /api/cms/menus/stats - Get menus statistics
export async function GET(request: NextRequest) {
    try {
        // Get total count
        const [totalResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as total FROM menus'
        )
        const total = totalResult[0].total

        // Get active count
        const [activeResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM menus WHERE is_active = 1'
        )
        const active = activeResult[0].count

        // Get inactive count
        const [inactiveResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM menus WHERE is_active = 0'
        )
        const inactive = inactiveResult[0].count

        // Get recent menus
        const [recentMenus] = await pool.execute<RowDataPacket[]>(
            'SELECT id, label as title, is_active as status, created_at FROM menus ORDER BY created_at DESC LIMIT 5'
        )

        return NextResponse.json({
            success: true,
            data: {
                total,
                active,
                inactive,
                recent: recentMenus
            }
        })
    } catch (error) {
        console.error('Error fetching menus stats:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch menus statistics' },
            { status: 500 }
        )
    }
} 