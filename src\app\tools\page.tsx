'use client'

import { motion } from 'framer-motion'
import { TbCalculator, TbCurrency, TbChartLine, TbClock, TbTrendingUp, TbTarget } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import PageLayout from '@/components/layout/PageLayout'

const ToolsPage = () => {
  const router = useRouter()

  const toolCategories = [
    {
      id: 1,
      name: "Trading Calculators",
      description: "Essential calculators for risk management and trade planning",
      icon: TbCalculator,
      tools: [
        "Pip Calculator",
        "Profit Calculator",
        "Margin Calculator",
        "Swap Calculator",
        "Position Size Calculator",
        "Risk Calculator"
      ],
      color: "text-[#EA5455]",
      bgColor: "bg-[#EA5455]/10",
      href: "/tools/calculators"
    },
    {
      id: 2,
      name: "Currency Tools",
      description: "Real-time currency conversion and exchange rate tools",
      icon: TbCurrency,
      tools: [
        "Currency Converter",
        "Live Exchange Rates",
        "Historical Rates",
        "Currency Correlation",
        "Cross Rate Calculator",
        "Currency Strength Meter"
      ],
      color: "text-[#28C76F]",
      bgColor: "bg-[#28C76F]/10",
      href: "/tools/currency-converter"
    },
    {
      id: 3,
      name: "Market Analysis",
      description: "Advanced tools for technical and fundamental analysis",
      icon: TbChartLine,
      tools: [
        "Economic Calendar",
        "Market Sentiment",
        "Volatility Tracker",
        "Fibonacci Calculator",
        "Pivot Point Calculator",
        "Support & Resistance"
      ],
      color: "text-[#28C76F]",
      bgColor: "bg-[#28C76F]/10",
      href: "/tools/analysis"
    },
    {
      id: 4,
      name: "Market Hours",
      description: "Global market sessions and trading hours tracker",
      icon: TbClock,
      tools: [
        "Trading Sessions",
        "Market Overlaps",
        "Holiday Calendar",
        "Session Volatility",
        "Best Trading Times",
        "Market Status"
      ],
      color: "text-purple-500",
      bgColor: "bg-purple-500/10",
      href: "/tools/market-hours"
    },
    {
      id: 5,
      name: "Performance Tools",
      description: "Track and analyze your trading performance",
      icon: TbTrendingUp,
      tools: [
        "Trade Journal",
        "Performance Analytics",
        "Risk Metrics",
        "Drawdown Calculator",
        "Sharpe Ratio Calculator",
        "Win Rate Tracker"
      ],
      color: "text-[#EA5455]",
      bgColor: "bg-[#EA5455]/10",
      href: "/tools/performance"
    },
    {
      id: 6,
      name: "Risk Management",
      description: "Professional risk assessment and management tools",
      icon: TbTarget,
      tools: [
        "Risk/Reward Calculator",
        "Stop Loss Calculator",
        "Portfolio Risk",
        "Correlation Matrix",
        "VaR Calculator",
        "Monte Carlo Simulation"
      ],
      color: "text-teal-500",
      bgColor: "bg-teal-500/10",
      href: "/tools/risk-management"
    }
  ]

  const handleExploreTools = (href: string) => {
    router.push(href)
  }

  return (
    <div className="mt-20">
      <PageLayout>
        {/* Hero Section */}
        <section className="relative py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Professional Trading <span className="text-[#EA5455]">Tools</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Comprehensive suite of trading calculators, market analysis tools, and risk management
                utilities to enhance your trading performance and decision-making.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Tools Categories Grid */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {toolCategories.map((category, index) => {
                const IconComponent = category.icon
                return (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 group"
                  >
                    <div className={`w-16 h-16 ${category.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className={`h-8 w-8 ${category.color}`} />
                    </div>

                    <h3 className="text-2xl font-bold mb-3">{category.name}</h3>
                    <p className="text-muted-foreground mb-6">{category.description}</p>

                    <div className="space-y-2 mb-6">
                      {category.tools.map((tool, toolIndex) => (
                        <div key={toolIndex} className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${category.color.replace('text-', 'bg-')}`}></div>
                          <span className="text-sm text-muted-foreground">{tool}</span>
                        </div>
                      ))}
                    </div>

                    <button
                      onClick={() => handleExploreTools(category.href)}
                      className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 border-2 ${category.color.replace('text-', 'border-')} ${category.color} hover:${category.color.replace('text-', 'bg-')} hover:text-white`}
                    >
                      Explore Tools
                    </button>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </section>

        {/* Featured Tools Section */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Most Popular Tools
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Quick access to our most frequently used trading tools and calculators.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { name: "Pip Calculator", href: "/tools/pip-calculator", icon: "🧮" },
                { name: "Currency Converter", href: "/tools/currency-converter", icon: "💱" },
                { name: "Economic Calendar", href: "/tools/economic-calendar", icon: "📅" },
                { name: "Market Hours", href: "/tools/market-hours", icon: "🕐" }
              ].map((tool, index) => (
                <motion.div
                  key={tool.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-700 rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 cursor-pointer"
                  onClick={() => router.push(tool.href)}
                >
                  <div className="text-3xl mb-3">{tool.icon}</div>
                  <h3 className="font-semibold text-lg">{tool.name}</h3>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Start Using Professional Tools Today
              </h2>
              <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
                All tools are free to use and designed to help you make better trading decisions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => router.push('/tools/pip-calculator')}
                  className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
                >
                  Try Pip Calculator
                </button>
                <button
                  onClick={() => router.push('/tools/currency-converter')}
                  className="border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
                >
                  Currency Converter
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </PageLayout>
    </div>
  )
}

export default ToolsPage