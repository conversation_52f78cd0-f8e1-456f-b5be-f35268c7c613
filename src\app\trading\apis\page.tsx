'use client'

import { motion } from 'framer-motion'
import { T<PERSON><PERSON><PERSON>, TbApi, TbShield, Tb<PERSON>lock, TbChartLine, TbRocket } from 'react-icons/tb'
import { HiArrowRight } from 'react-icons/hi2'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function ApisPage() {
    const apiFeatures = [
        {
            icon: TbCode,
            title: 'RESTful API',
            description: 'Access our comprehensive REST API for account management, trading, and market data.'
        },
        {
            icon: TbApi,
            title: 'WebSocket Feeds',
            description: 'Real-time market data and trade execution through high-performance WebSocket connections.'
        },
        {
            icon: TbShield,
            title: 'Secure Authentication',
            description: 'Industry-standard OAuth 2.0 and API key authentication with rate limiting.'
        },
        {
            icon: TbClock,
            title: 'Low Latency',
            description: 'Ultra-low latency execution with co-located servers for algorithmic trading.'
        }
    ]

    const apiEndpoints = [
        {
            method: 'GET',
            endpoint: '/api/v1/account/balance',
            description: 'Retrieve account balance and equity information'
        },
        {
            method: 'POST',
            endpoint: '/api/v1/orders',
            description: 'Place new trading orders with advanced parameters'
        },
        {
            method: 'GET',
            endpoint: '/api/v1/positions',
            description: 'Get current open positions and their status'
        },
        {
            method: 'GET',
            endpoint: '/api/v1/market/prices',
            description: 'Real-time and historical market price data'
        },
        {
            method: 'DELETE',
            endpoint: '/api/v1/orders/{id}',
            description: 'Cancel existing orders by order ID'
        },
        {
            method: 'GET',
            endpoint: '/api/v1/history/trades',
            description: 'Historical trade data and transaction history'
        }
    ]

    const tradingBenefits = [
        'Algorithmic trading capabilities',
        'Custom trading applications',
        'Portfolio management tools',
        'Risk management systems',
        'Market data integration',
        'Automated trading strategies'
    ]

    const codeExample = `// Example: Place a market order
const order = {
  symbol: "EURUSD",
  side: "buy",
  type: "market",
  quantity: 100000,
  stopLoss: 1.0800,
  takeProfit: 1.0900
};

const response = await fetch('/api/v1/orders', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(order)
});

const result = await response.json();
console.log('Order placed:', result);`

    return (
        <div className="mt-0">
            <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-5xl text-gray-300 mt-8 font-bold mb-6">
                                    Trading APIs
                                </h1>
                                <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Build powerful trading applications with our comprehensive APIs.
                                    Access real-time market data, execute trades, and manage portfolios programmatically.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center"
                                    >
                                        Get API Access
                                        <HiArrowRight className="ml-2 w-5 h-5" />
                                    </button>
                                    <button className="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-gray-900 transition-colors duration-200">
                                        View Documentation
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Features Section */}
                    <section className="py-20">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    API Features
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300">
                                    Everything you need for algorithmic trading
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                                {apiFeatures.map((feature, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.8, delay: 0.1 * index }}
                                        className="text-center"
                                    >
                                        <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                            <feature.icon className="w-8 h-8 text-white" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                            {feature.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300">
                                            {feature.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* API Endpoints Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    API Endpoints
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300">
                                    Comprehensive endpoints for all trading operations
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                {apiEndpoints.map((endpoint, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.8, delay: 0.1 * index }}
                                        className="bg-gray-50 dark:bg-gray-700 p-6 rounded-2xl"
                                    >
                                        <div className="flex items-center mb-4">
                                            <span className={`px-3 py-1 rounded-lg text-sm font-medium mr-4 ${
                                                endpoint.method === 'GET' 
                                                    ? 'bg-[#28C76F] bg-opacity-10 text-[#28C76F]'
                                                    : endpoint.method === 'POST'
                                                    ? 'bg-blue-500 bg-opacity-10 text-blue-500'
                                                    : 'bg-[#EA5455] bg-opacity-10 text-[#EA5455]'
                                            }`}>
                                                {endpoint.method}
                                            </span>
                                            <code className="text-gray-900 dark:text-white font-mono text-sm">
                                                {endpoint.endpoint}
                                            </code>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300">
                                            {endpoint.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Code Example Section */}
                    <section className="py-20">
                        <div className="max-w-7xl mx-auto px-6">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                                <motion.div
                                    initial={{ opacity: 0, x: -30 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8 }}
                                >
                                    <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                        Easy Integration
                                    </h2>
                                    <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                        Get started quickly with our well-documented APIs and code examples. 
                                        Build sophisticated trading applications with just a few lines of code.
                                    </p>
                                    <ul className="space-y-4">
                                        {tradingBenefits.map((benefit, index) => (
                                            <li key={index} className="flex items-center">
                                                <div className="w-2 h-2 bg-[#EA5455] rounded-full mr-4"></div>
                                                <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, x: 30 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 0.2 }}
                                    className="bg-gray-900 p-6 rounded-2xl"
                                >
                                    <div className="flex items-center mb-4">
                                        <div className="flex space-x-2">
                                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                                            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                        </div>
                                        <span className="ml-4 text-gray-400 text-sm">JavaScript Example</span>
                                    </div>
                                    <pre className="text-green-400 text-sm overflow-x-auto">
                                        <code>{codeExample}</code>
                                    </pre>
                                </motion.div>
                            </div>
                        </div>
                    </section>

                    {/* Getting Started Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Getting Started
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300">
                                    Start building with our APIs in minutes
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                <motion.div
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <span className="text-white text-2xl font-bold">1</span>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        Create Account
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        Sign up for a trading account and verify your identity to get started.
                                    </p>
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.2 }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <span className="text-white text-2xl font-bold">2</span>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        Get API Keys
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        Generate your API keys from the client portal with appropriate permissions.
                                    </p>
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.3 }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <span className="text-white text-2xl font-bold">3</span>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        Start Trading
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        Begin making API calls and building your trading applications.
                                    </p>
                                </motion.div>
                            </div>

                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.4 }}
                                className="text-center mt-12"
                            >
                                <div className="bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl inline-block">
                                    <TbRocket className="w-16 h-16 text-[#EA5455] mx-auto mb-6" />
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        Ready to Build?
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        Get access to our trading APIs and start building your applications today.
                                    </p>
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                    >
                                        Get API Access
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
        </div>
    )
}
