'use client'

import { motion } from 'framer-motion'
import Container from './LandingContainer'
import But<PERSON> from '@/components/ui/Button'
import { TbCheck, TbTrendingUp, TbShield, TbHeadphones, TbArrowRight } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import type { Mode } from '@/@types/theme'

type PricingSectionProps = {
    mode: Mode
}

const accountTypes = [
    {
        id: 1,
        name: "Standard",
        description: "Perfect for beginners",
        minDeposit: "$100",
        spread: "From 1.2 pips",
        leverage: "1:100",
        features: [
            "50+ Currency Pairs",
            "Standard Spreads",
            "Basic Market Analysis",
            "Email Support",
            "Educational Resources",
            "Mobile Trading App"
        ],
        popular: false,
        buttonText: "Open Standard Account"
    },
    {
        id: 2,
        name: "Professional",
        description: "For experienced traders",
        minDeposit: "$1,000",
        spread: "From 0.8 pips",
        leverage: "1:200",
        features: [
            "70+ Currency Pairs",
            "Reduced Spreads",
            "Advanced Market Analysis",
            "Priority Support",
            "Trading Signals",
            "Risk Management Tools",
            "Economic Calendar",
            "VPS Hosting"
        ],
        popular: true,
        buttonText: "Open Professional Account"
    },
    {
        id: 3,
        name: "VIP",
        description: "For high-volume traders",
        minDeposit: "$10,000",
        spread: "From 0.1 pips",
        leverage: "1:500",
        features: [
            "100+ Currency Pairs",
            "Ultra-tight Spreads",
            "Premium Market Analysis",
            "Dedicated Account Manager",
            "Custom Trading Solutions",
            "Institutional Liquidity",
            "Advanced Trading Tools",
            "24/7 Phone Support"
        ],
        popular: false,
        buttonText: "Open VIP Account"
    }
]

const PricingSection = ({ mode }: PricingSectionProps) => {
    const router = useRouter()

    const handleAccountOpen = (accountType: string) => {
        // Always redirect to external register page
        if (typeof window !== 'undefined') {
            window.open(`https://mbf.mybrokerforex.com/user/register?account=${accountType.toLowerCase()}`, '_blank')
        }
    }

    return (
        <Container>
            <div className="max-w-7xl mx-auto px-4 py-20">
                <div className="text-center mb-16">
                    <motion.h2 
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="text-3xl md:text-5xl font-bold mb-4"
                    >
                        Choose Your <span className="text-[#EA5455]">Trading Account</span>
                    </motion.h2>
                    <motion.p 
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="text-xl text-muted-foreground max-w-3xl mx-auto"
                    >
                        Select the account type that best fits your trading style and experience level.
                    </motion.p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {accountTypes.map((account, index) => (
                        <motion.div
                            key={account.id}
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            className={`relative bg-white dark:bg-gray-800 rounded-2xl p-6 border-2 transition-all duration-300 hover:shadow-xl ${
                                account.popular 
                                    ? 'border-[#EA5455] shadow-lg' 
                                    : 'border-gray-200 dark:border-gray-700 hover:border-[#EA5455]/50'
                            }`}
                        >
                            {account.popular && (
                                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                    <span className="bg-[#EA5455] text-white px-4 py-1 rounded-full text-sm font-semibold">
                                        Most Popular
                                    </span>
                                </div>
                            )}

                            <div className="text-center mb-6">
                                <h3 className="text-2xl font-bold mb-2">{account.name}</h3>
                                <p className="text-muted-foreground mb-4">{account.description}</p>
                                
                                <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">Min Deposit:</span>
                                        <span className="font-semibold text-[#EA5455]">{account.minDeposit}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">Spreads:</span>
                                        <span className="font-semibold">{account.spread}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground">Leverage:</span>
                                        <span className="font-semibold">{account.leverage}</span>
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-3 mb-8">
                                {account.features.map((feature, featureIndex) => (
                                    <div key={featureIndex} className="flex items-center gap-3">
                                        <TbCheck className="h-5 w-5 text-[#28C76F] flex-shrink-0" />
                                        <span className="text-sm">{feature}</span>
                                    </div>
                                ))}
                            </div>

                            <Button
                                variant={account.popular ? "solid" : "plain"}
                                size="md"
                                onClick={() => handleAccountOpen(account.name)}
                                className={`w-full ${
                                    account.popular
                                        ? 'bg-[#EA5455] hover:bg-[#EA5455]/90 text-white'
                                        : 'border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white'
                                }`}
                                icon={<TbArrowRight />}
                                iconAlignment="end"
                            >
                                <span>{account.buttonText}</span>
                            </Button>
                        </motion.div>
                    ))}
                </div>

                {/* Additional Benefits */}
                <motion.div 
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mt-16 bg-gray-50 dark:bg-gray-800 rounded-2xl p-8"
                >
                    <h3 className="text-2xl font-bold text-center mb-8">All Accounts Include</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbTrendingUp className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">Advanced Platform</h4>
                            <p className="text-sm text-muted-foreground">Professional trading platform with advanced charting and analysis tools</p>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbShield className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">Negative Balance Protection</h4>
                            <p className="text-sm text-muted-foreground">Your investment security is of utmost importance to us, and we strive to provide you with the necessary protection to maintain your financial well-being.</p>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbHeadphones className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">Expert Support</h4>
                            <p className="text-sm text-muted-foreground">Professional support from experienced trading specialists</p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </Container>
    )
}

export default PricingSection
