'use client'

import { motion } from 'framer-motion'
import Container from './LandingContainer'
import Button from '@/components/ui/Button'
import { TbTrendingUp, TbGift, TbUsers, TbCopy, TbTrophy, TbArrowRight, TbShield, TbHeadphones } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import type { Mode } from '@/@types/theme'

type PricingSectionProps = {
    mode: Mode
}

const mbfxOfferings = [
    {
        id: 1,
        icon: TbTrendingUp,
        title: "Volume Based Earning",
        description: "Get rebates for trading activities. The more volume your clients trade, the more you earn—perfect for active network holders and traders looking to maximize their gains.",
        popular: false
    },
    {
        id: 2,
        icon: TbGift,
        title: "Trading Bonus",
        description: "Boost your trading power with exclusive bonuses. Enjoy extra equity on deposits and special campaigns to help you seize more market opportunities.",
        popular: true
    },
    {
        id: 3,
        icon: TbUsers,
        title: "Invite & Earn",
        description: "Grow your income by sharing the opportunity. Refer friends to MBFX and earn commission or bonuses when they start trading.",
        popular: false
    },
    {
        id: 4,
        icon: TbCopy,
        title: "Copy Trading",
        description: "Trade like the pros with ease. Follow top-performing traders and automatically copy their strategies in real time.",
        popular: false
    },
    {
        id: 5,
        icon: TbTrophy,
        title: "Trade & Win",
        description: "Turn your trades into rewards. Join trading contests and promotional campaigns to win cash prizes, gadgets, and more just by trading.",
        popular: false
    }
]

const PricingSection = ({ mode }: PricingSectionProps) => {
    const router = useRouter()

    const handleLearnMore = () => {
        // Redirect to promotions or offerings page
        if (typeof window !== 'undefined') {
            window.open('https://mbf.mybrokerforex.com/user/register', '_blank')
        }
    }

    return (
        <Container>
            <div className="container mx-auto px-6 py-20">
                <div className="text-center mb-16">
                    <motion.h2
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="text-3xl md:text-5xl font-bold mb-4"
                    >
                        Get More and More with <span className="text-[#EA5455]">MBFX Hot Offerings</span>
                    </motion.h2>
                    <motion.p
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="text-xl text-muted-foreground max-w-3xl mx-auto"
                    >
                        Unlock rewards, bonuses, and exclusive features designed to boost your trading success every step of the way.
                    </motion.p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {mbfxOfferings.map((offering, index) => {
                        const IconComponent = offering.icon
                        return (
                        <motion.div
                            key={offering.id}
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            className={`relative bg-white dark:bg-gray-800 rounded-2xl p-6 border-2 transition-all duration-300 hover:shadow-xl ${
                                offering.popular
                                    ? 'border-[#EA5455] shadow-lg'
                                    : 'border-gray-200 dark:border-gray-700 hover:border-[#EA5455]/50'
                            }`}
                        >
                            {offering.popular && (
                                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                    <span className="bg-[#EA5455] text-white px-4 py-1 rounded-full text-sm font-semibold">
                                        Most Popular
                                    </span>
                                </div>
                            )}

                            <div className="text-center mb-6">
                                <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <IconComponent className="h-8 w-8 text-[#EA5455]" />
                                </div>
                                <h3 className="text-xl font-bold mb-3">{offering.title}</h3>
                                <p className="text-muted-foreground text-sm leading-relaxed">{offering.description}</p>
                            </div>

                            <Button
                                variant="plain"
                                size="md"
                                onClick={handleLearnMore}
                                className="w-full border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white"
                                icon={<TbArrowRight />}
                                iconAlignment="end"
                            >
                                <span>Learn More</span>
                            </Button>
                        </motion.div>
                        )
                    })}
                </div>

                {/* Why Choose MBFX */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mt-16 bg-gray-50 dark:bg-gray-800 rounded-2xl p-8"
                >
                    <h3 className="text-2xl font-bold text-center mb-8">Why Choose MBFX</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbTrendingUp className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">Advanced Platform</h4>
                            <p className="text-sm text-muted-foreground">Professional trading platform with advanced charting and analysis tools</p>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbShield className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">Secure Trading</h4>
                            <p className="text-sm text-muted-foreground">Bank-level security with negative balance protection and segregated client funds</p>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbHeadphones className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">24/7 Support</h4>
                            <p className="text-sm text-muted-foreground">Round-the-clock professional support from experienced trading specialists</p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </Container>
    )
}

export default PricingSection
