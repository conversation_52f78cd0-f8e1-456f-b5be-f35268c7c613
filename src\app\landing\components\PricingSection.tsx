'use client'

import { motion } from 'framer-motion'
import Container from './LandingContainer'
import { TbTrendingUp, TbGift, TbUsers, TbCopy, TbTrophy, TbShield, TbHeadphones } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import type { Mode } from '@/@types/theme'

type PricingSectionProps = {
    mode: Mode
}

const mbfxOfferings = [
    {
        id: 1,
        icon: TbTrendingUp,
        title: "Volume Based Earning",
        description: "Get rebates for trading activities. The more volume your clients trade, the more you earn—perfect for active network holders and traders looking to maximize their gains.",
        popular: false
    },
    {
        id: 2,
        icon: TbGift,
        title: "Trading Bonus",
        description: "Boost your trading power with exclusive bonuses. Enjoy extra equity on deposits and special campaigns to help you seize more market opportunities.",
        popular: true
    },
    {
        id: 3,
        icon: TbUsers,
        title: "Invite & Earn",
        description: "Grow your income by sharing the opportunity. Refer friends to MBFX and earn commission or bonuses when they start trading.",
        popular: false
    },
    {
        id: 4,
        icon: TbCopy,
        title: "Copy Trading",
        description: "Trade like the pros with ease. Follow top-performing traders and automatically copy their strategies in real time.",
        popular: false
    },
    {
        id: 5,
        icon: TbTrophy,
        title: "Trade & Win",
        description: "Turn your trades into rewards. Join trading contests and promotional campaigns to win cash prizes, gadgets, and more just by trading.",
        popular: false
    }
]

const PricingSection = ({ mode }: PricingSectionProps) => {
    const router = useRouter()

    const handleLearnMore = () => {
        // Redirect to promotions or offerings page
        if (typeof window !== 'undefined') {
            window.open('https://mbf.mybrokerforex.com/user/register', '_blank')
        }
    }

    return (
        <Container>
            <div className="container mx-auto px-6 py-20">
                <div className="text-center mb-16">
                    <motion.h2
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="text-3xl md:text-5xl font-bold mb-4"
                    >
                        Get More and More with <span className="text-[#EA5455]">MBFX Hot Offerings</span>
                    </motion.h2>
                    <motion.p
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="text-xl text-muted-foreground max-w-3xl mx-auto"
                    >
                        Unlock rewards, bonuses, and exclusive features designed to boost your trading success every step of the way.
                    </motion.p>
                </div>

                {/* Random Grid Layout */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 auto-rows-fr">
                    {mbfxOfferings.map((offering, index) => {
                        const IconComponent = offering.icon
                        // Define different sizes for random grid effect
                        const gridClasses = [
                            'lg:col-span-2 lg:row-span-1', // Wide card
                            'lg:col-span-1 lg:row-span-2', // Tall card
                            'lg:col-span-1 lg:row-span-1', // Normal card
                            'lg:col-span-2 lg:row-span-1', // Wide card
                            'lg:col-span-1 lg:row-span-1', // Normal card
                        ]

                        return (
                        <motion.div
                            key={offering.id}
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            onClick={handleLearnMore}
                            className={`relative overflow-hidden rounded-2xl cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-2xl group ${gridClasses[index]} min-h-[280px]`}
                            style={{
                                backgroundImage: `linear-gradient(135deg, rgba(0, 0, 0, 0.21) 0%, rgba(40, 24, 24, 0.17) 100%), url('https://images.unsplash.com/photo-${
                                    index === 0 ? '1611974789855-9c2a0a7236a3' : // Trading charts
                                    index === 1 ? '1559526324-4b87b5e36e44' : // Money/bonus
                                    index === 2 ? '1521737604893-d14cc237f11d' : // People/networking
                                    index === 3 ? '1460925895917-afdab827c52f' : // Copy trading
                                    '1579952363873-27d3bfad9c0d' // Trophy/winning
                                }?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')`,
                                backgroundSize: 'cover',
                                backgroundPosition: 'center',
                            }}
                        >
                            {offering.popular && (
                                <div className="absolute top-4 right-4 z-10">
                                    <span className="bg-white text-[#EA5455] px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                        Most Popular
                                    </span>
                                </div>
                            )}

                            {/* Content overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>

                            <div className="relative z-10 h-full flex flex-col justify-end p-6 text-white">
                                <div className="mb-4">
                                    <IconComponent className="h-12 w-12 text-white mb-4" />
                                </div>
                                <div className="text-2xl font-bold mb-3 group-hover:text-[#28C76F] transition-colors">{offering.title}</div>
                                <p className="text-white/90 text-sm leading-relaxed line-clamp-3">{offering.description}</p>
                            </div>

                            {/* Hover effect overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-[#EA5455]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </motion.div>
                        )
                    })}
                </div>

                {/* Why Choose MBFX */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mt-16 bg-gray-50 dark:bg-gray-800 rounded-2xl p-8"
                >
                    <h3 className="text-2xl font-bold text-center mb-8">Why Choose MBFX</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbTrendingUp className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">Advanced Platform</h4>
                            <p className="text-sm text-muted-foreground">Professional trading platform with advanced charting and analysis tools</p>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbShield className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">Secure Trading</h4>
                            <p className="text-sm text-muted-foreground">Bank-level security with negative balance protection and segregated client funds</p>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <TbHeadphones className="h-8 w-8 text-[#EA5455]" />
                            </div>
                            <h4 className="font-semibold mb-2">24/7 Support</h4>
                            <p className="text-sm text-muted-foreground">Round-the-clock professional support from experienced trading specialists</p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </Container>
    )
}

export default PricingSection
