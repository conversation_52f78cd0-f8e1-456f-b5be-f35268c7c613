const mysql = require('mysql2/promise');

async function checkTables() {
    let connection;
    
    try {
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'mybrokerforex'
        });

        console.log('Connected to MySQL database');

        // Check hero_sections table
        try {
            const [structure] = await connection.execute("DESCRIBE hero_sections");
            console.log('\nHero sections table structure:');
            structure.forEach(col => {
                console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'} ${col.Key ? `[${col.Key}]` : ''}`);
            });
        } catch (error) {
            console.log('Hero sections table does not exist');
        }

        // Check posts table
        try {
            const [structure] = await connection.execute("DESCRIBE posts");
            console.log('\nPosts table structure:');
            structure.forEach(col => {
                console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'} ${col.Key ? `[${col.Key}]` : ''}`);
            });
        } catch (error) {
            console.log('Posts table does not exist');
        }

        // List all tables
        const [tables] = await connection.execute("SHOW TABLES");
        console.log('\nAll tables:');
        tables.forEach(table => {
            console.log(`- ${Object.values(table)[0]}`);
        });

    } catch (error) {
        console.error('Error:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

checkTables();
