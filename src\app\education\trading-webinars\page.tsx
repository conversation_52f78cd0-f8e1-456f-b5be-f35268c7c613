'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbVideo, TbCalendar, TbClock, TbUsers, TbStar, TbPlayerPlay, TbBroadcast, TbMicrophone } from 'react-icons/tb'

const TradingWebinarsPage = () => {
    const upcomingWebinars = [
        {
            title: 'Market Analysis: EUR/USD Weekly Outlook',
            presenter: '<PERSON>, Senior Market Analyst',
            date: '2024-01-15',
            time: '14:00 GMT',
            duration: '60 minutes',
            attendees: 245,
            description: 'Deep dive into EUR/USD technical and fundamental analysis for the upcoming week.',
            topics: ['Technical Analysis', 'Economic Calendar', 'Trade Setups', 'Risk Management'],
            level: 'Intermediate',
            isLive: true
        },
        {
            title: 'Beginner Trading Bootcamp',
            presenter: '<PERSON>, Head of Education',
            date: '2024-01-18',
            time: '16:00 GMT',
            duration: '90 minutes',
            attendees: 189,
            description: 'Complete introduction to forex trading for absolute beginners.',
            topics: ['Forex Basics', 'Platform Tutorial', 'First Trade', 'Common Mistakes'],
            level: 'Beginner',
            isLive: true
        },
        {
            title: 'Advanced Price Action Strategies',
            presenter: '<PERSON>, Professional Trader',
            date: '2024-01-22',
            time: '13:00 GMT',
            duration: '75 minutes',
            attendees: 156,
            description: 'Master advanced price action techniques for professional trading.',
            topics: ['Market Structure', 'Order Flow', 'Institutional Levels', 'Entry Timing'],
            level: 'Advanced',
            isLive: true
        }
    ]

    const recordedWebinars = [
        {
            title: 'Risk Management Masterclass',
            presenter: 'Emma Thompson, Risk Manager',
            recordedDate: '2024-01-08',
            duration: '85 minutes',
            views: '2.1K',
            rating: 4.9,
            description: 'Comprehensive guide to protecting your trading capital.',
            topics: ['Position Sizing', 'Stop Loss Strategies', 'Portfolio Management', 'Psychology'],
            level: 'Intermediate'
        },
        {
            title: 'Central Bank Policy Impact on Forex',
            presenter: 'James Wilson, Economic Analyst',
            recordedDate: '2024-01-05',
            duration: '70 minutes',
            views: '1.8K',
            rating: 4.8,
            description: 'Understanding how central bank decisions affect currency markets.',
            topics: ['Interest Rates', 'Monetary Policy', 'Market Reactions', 'Trading Opportunities'],
            level: 'Advanced'
        },
        {
            title: 'Technical Indicators Deep Dive',
            presenter: 'Lisa Park, Technical Analyst',
            recordedDate: '2024-01-03',
            duration: '95 minutes',
            views: '2.5K',
            rating: 4.7,
            description: 'Master the most effective technical indicators for forex trading.',
            topics: ['Moving Averages', 'RSI', 'MACD', 'Bollinger Bands'],
            level: 'Intermediate'
        },
        {
            title: 'News Trading Strategies',
            presenter: 'Robert Kim, News Trader',
            recordedDate: '2023-12-28',
            duration: '60 minutes',
            views: '1.9K',
            rating: 4.6,
            description: 'Learn to trade high-impact news events successfully.',
            topics: ['Economic Calendar', 'News Impact', 'Entry Strategies', 'Risk Control'],
            level: 'Advanced'
        }
    ]

    const webinarSeries = [
        {
            title: 'Weekly Market Outlook',
            description: 'Weekly analysis of major currency pairs and market trends',
            frequency: 'Every Monday',
            time: '14:00 GMT',
            duration: '60 minutes',
            episodes: 52,
            avgAttendees: 280
        },
        {
            title: 'Beginner Trading Series',
            description: 'Monthly educational series for new traders',
            frequency: 'First Thursday',
            time: '16:00 GMT',
            duration: '90 minutes',
            episodes: 12,
            avgAttendees: 195
        },
        {
            title: 'Advanced Strategy Sessions',
            description: 'Bi-weekly deep dives into professional trading techniques',
            frequency: 'Every 2 weeks',
            time: '13:00 GMT',
            duration: '75 minutes',
            episodes: 26,
            avgAttendees: 145
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Trading Webinars
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Join live interactive sessions with expert traders and access our library of recorded webinars
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-colors duration-300"
                                    >
                                        Register for Webinars
                                    </button>
                                    <button 
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#1E1E1E] transition-colors duration-300"
                                    >
                                        View Schedule
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Upcoming Webinars Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Upcoming Live Webinars
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Join our expert traders for live interactive sessions and get your questions answered in real-time.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {upcomingWebinars.map((webinar, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-center justify-between mb-4">
                                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                                webinar.level === 'Beginner' 
                                                    ? 'bg-[#28C76F]/10 text-[#28C76F]' 
                                                    : webinar.level === 'Intermediate'
                                                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                                                    : 'bg-[#EA5455]/10 text-[#EA5455]'
                                            }`}>
                                                {webinar.level}
                                            </span>
                                            <div className="flex items-center space-x-1 text-[#EA5455]">
                                                <TbBroadcast className="w-4 h-4" />
                                                <span className="text-xs font-medium">LIVE</span>
                                            </div>
                                        </div>
                                        
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                            {webinar.title}
                                        </h3>
                                        
                                        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                            {webinar.description}
                                        </p>
                                        
                                        <div className="space-y-2 mb-4">
                                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                <TbMicrophone className="w-4 h-4" />
                                                <span>{webinar.presenter}</span>
                                            </div>
                                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                <TbCalendar className="w-4 h-4" />
                                                <span>{webinar.date} at {webinar.time}</span>
                                            </div>
                                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                <TbClock className="w-4 h-4" />
                                                <span>{webinar.duration}</span>
                                            </div>
                                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                <TbUsers className="w-4 h-4" />
                                                <span>{webinar.attendees} registered</span>
                                            </div>
                                        </div>
                                        
                                        <div className="mb-4">
                                            <h4 className="font-semibold text-gray-900 dark:text-white text-sm mb-2">Topics Covered:</h4>
                                            <div className="flex flex-wrap gap-1">
                                                {webinar.topics.map((topic, topicIndex) => (
                                                    <span key={topicIndex} className="px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-xs">
                                                        {topic}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                        
                                        <button className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-3 rounded-lg font-semibold transition-colors duration-300">
                                            Register Now
                                        </button>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Recorded Webinars Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Recorded Webinar Library
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Access our extensive library of recorded webinars and learn at your own pace.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {recordedWebinars.map((webinar, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-center justify-between mb-4">
                                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                                webinar.level === 'Beginner'
                                                    ? 'bg-[#28C76F]/10 text-[#28C76F]'
                                                    : webinar.level === 'Intermediate'
                                                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                                                    : 'bg-[#EA5455]/10 text-[#EA5455]'
                                            }`}>
                                                {webinar.level}
                                            </span>
                                            <div className="flex items-center space-x-1">
                                                <TbStar className="w-4 h-4 text-yellow-400 fill-current" />
                                                <span className="text-sm text-gray-600 dark:text-gray-400">{webinar.rating}</span>
                                            </div>
                                        </div>

                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                            {webinar.title}
                                        </h3>

                                        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                            {webinar.description}
                                        </p>

                                        <div className="space-y-2 mb-4">
                                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                <TbMicrophone className="w-4 h-4" />
                                                <span>{webinar.presenter}</span>
                                            </div>
                                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                <TbCalendar className="w-4 h-4" />
                                                <span>Recorded: {webinar.recordedDate}</span>
                                            </div>
                                            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                                                <div className="flex items-center space-x-2">
                                                    <TbClock className="w-4 h-4" />
                                                    <span>{webinar.duration}</span>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <TbUsers className="w-4 h-4" />
                                                    <span>{webinar.views} views</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="mb-4">
                                            <h4 className="font-semibold text-gray-900 dark:text-white text-sm mb-2">Topics Covered:</h4>
                                            <div className="flex flex-wrap gap-1">
                                                {webinar.topics.map((topic, topicIndex) => (
                                                    <span key={topicIndex} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs">
                                                        {topic}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>

                                        <button className="w-full bg-[#28C76F] hover:bg-[#28C76F]/90 text-white py-3 rounded-lg font-semibold transition-colors duration-300 flex items-center justify-center space-x-2">
                                            <TbPlayerPlay className="w-5 h-5" />
                                            <span>Watch Now</span>
                                        </button>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Webinar Series Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Regular Webinar Series
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Join our recurring webinar series for consistent learning and market updates.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                {webinarSeries.map((series, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 text-center hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                                            {series.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                                            {series.description}
                                        </p>
                                        <div className="space-y-3 mb-6">
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-500 dark:text-gray-400">Schedule:</span>
                                                <span className="font-medium text-gray-900 dark:text-white">{series.frequency}</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-500 dark:text-gray-400">Time:</span>
                                                <span className="font-medium text-gray-900 dark:text-white">{series.time}</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-500 dark:text-gray-400">Duration:</span>
                                                <span className="font-medium text-gray-900 dark:text-white">{series.duration}</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-500 dark:text-gray-400">Episodes:</span>
                                                <span className="font-medium text-gray-900 dark:text-white">{series.episodes}</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-500 dark:text-gray-400">Avg. Attendees:</span>
                                                <span className="font-medium text-gray-900 dark:text-white">{series.avgAttendees}</span>
                                            </div>
                                        </div>
                                        <button className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-3 rounded-lg font-semibold transition-colors duration-300">
                                            Subscribe to Series
                                        </button>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#28C76F] to-[#28C76F]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Never Miss a Learning Opportunity
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Register for our webinars and get access to exclusive trading insights from industry experts.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-white text-[#28C76F] px-5 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Register for Webinars
                                    </button>
                                    <button
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#28C76F] transition-colors duration-300"
                                    >
                                        View Full Schedule
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default TradingWebinarsPage
