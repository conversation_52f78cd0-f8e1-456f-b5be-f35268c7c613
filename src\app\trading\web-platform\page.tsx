'use client'

import { motion } from 'framer-motion'
import { Tb<PERSON>or<PERSON>, TbChartLine, TbBolt, TbShield, TbDevices, TbClock } from 'react-icons/tb'
import { HiArrowRight } from 'react-icons/hi2'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import CompactCTA from '@/components/shared/CompactCTA'

export default function WebPlatformPage() {
    const features = [
        {
            icon: TbWorld,
            title: 'Browser-Based Trading',
            description: 'Access your trading account from any device with an internet connection. No downloads required.'
        },
        {
            icon: TbChartLine,
            title: 'Advanced Charting',
            description: 'Professional-grade charts with 100+ technical indicators and drawing tools for comprehensive market analysis.'
        },
        {
            icon: TbBolt,
            title: 'Lightning Fast Execution',
            description: 'Ultra-low latency order execution with real-time market data and instant trade confirmations.'
        },
        {
            icon: TbShield,
            title: 'Bank-Level Security',
            description: 'SSL encryption and multi-factor authentication to keep your trading account secure.'
        },
        {
            icon: TbDevices,
            title: 'Cross-Device Sync',
            description: 'Seamlessly switch between devices with synchronized watchlists, layouts, and preferences.'
        },
        {
            icon: Tb<PERSON><PERSON>,
            title: '24/7 Market Access',
            description: 'Trade global markets around the clock with our reliable web-based platform.'
        }
    ]

    const benefits = [
        'No software installation required',
        'Compatible with all major browsers',
        'Real-time market data and news',
        'One-click trading functionality',
        'Customizable interface layouts',
        'Advanced risk management tools'
    ]

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
            {/* Hero Section */}
            <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        <h1 className="text-5xl text-gray-300 font-bold mb-6 mt-8">
                            Web Trading Platform
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Experience professional forex trading directly in your browser. Our advanced web platform 
                            combines powerful features with intuitive design for traders of all levels.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button
                                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center"
                            >
                                Start Trading Now
                                <HiArrowRight className="ml-2 w-5 h-5" />
                            </button>
                            <button
                                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')}
                                className="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-gray-900 transition-colors duration-200"
                            >
                                Try Demo Account
                            </button>
                        </div>
                    </motion.div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                        className="text-center mb-16"
                    >
                        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                            Platform Features
                        </h2>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            Our web platform is designed to provide you with everything you need for successful forex trading.
                        </p>
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {features.map((feature, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 * index }}
                                className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                            >
                                <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mb-6">
                                    <feature.icon className="w-8 h-8 text-white" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    {feature.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    {feature.description}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Benefits Section */}
            <section className="py-20 bg-white dark:bg-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <motion.div
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8 }}
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Why Choose Our Web Platform?
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                Trade with confidence using our feature-rich web platform that delivers 
                                institutional-grade trading capabilities directly to your browser.
                            </p>
                            <ul className="space-y-4">
                                {benefits.map((benefit, index) => (
                                    <motion.li
                                        key={index}
                                        initial={{ opacity: 0, x: -20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ duration: 0.6, delay: 0.1 * index }}
                                        className="flex items-center text-gray-700 dark:text-gray-300"
                                    >
                                        <div className="w-6 h-6 bg-[#28C76F] rounded-full flex items-center justify-center mr-4">
                                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                        {benefit}
                                    </motion.li>
                                ))}
                            </ul>
                        </motion.div>

                        <CompactCTA
                            title="Ready to Start Trading?"
                            description="Join thousands of traders who trust our web platform for their forex trading needs."
                            primaryButtonText="Open Trading Account"
                            showSecondaryButton={false}
                            variant="gradient"
                            className="max-w-lg mx-auto"
                        />
                    </div>
                </div>
            </section>
            </div>
        </PublicPageLayout>
    )
}
