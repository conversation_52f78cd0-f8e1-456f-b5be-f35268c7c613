'use client'

import useTheme from '@/utils/hooks/useTheme'
import withHeaderItem from '@/utils/hoc/withHeaderItem'
import { MODE_DARK, MODE_LIGHT } from '@/constants/theme.constant'
import type { CommonProps } from '@/@types/common'

const _AdminThemeToggle = ({ className }: CommonProps) => {
    // React 19 compatible - single useTheme call with stable selector
    const { mode, setMode } = useTheme((state) => ({
        mode: state.mode,
        setMode: state.setMode
    }))

    const toggleMode = () => {
        setMode(mode === MODE_LIGHT ? MODE_DARK : MODE_LIGHT)
    }

    return (
        <button
            className={`relative flex cursor-pointer items-center justify-center rounded-xl p-2 text-neutral-500 hover:shadow-input dark:text-neutral-500 ${className}`}
            onClick={toggleMode}
            title={`Switch to ${mode === MODE_LIGHT ? 'dark' : 'light'} mode`}
        >
            <svg
                className="lucide lucide-sun rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"
                fill="none"
                height="16"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="16"
            >
                <circle cx="12" cy="12" r="5"></circle>
                <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"></path>
            </svg>
            <svg
                className="lucide lucide-moon absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
                fill="none"
                height="16"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="16"
            >
                <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
            </svg>
        </button>
    )
}

const AdminThemeToggle = withHeaderItem(_AdminThemeToggle)

export default AdminThemeToggle
