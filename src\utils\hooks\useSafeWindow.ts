'use client'

import { useEffect, useState } from 'react'

/**
 * Hook to safely access window object and location properties
 * Prevents SSR hydration mismatches by only accessing window on client-side
 */
export const useSafeWindow = () => {
    const [isClient, setIsClient] = useState(false)

    useEffect(() => {
        setIsClient(true)
    }, [])

    const safeWindow = isClient && typeof window !== 'undefined' ? window : null
    
    const location = safeWindow?.location || {
        protocol: 'https:',
        hostname: 'localhost',
        host: 'localhost:3000',
        port: '3000',
        pathname: '/',
        search: '',
        hash: '',
        href: 'https://localhost:3000/',
        origin: 'https://localhost:3000'
    }

    return {
        isClient,
        window: safeWindow,
        location,
        // Safe methods
        open: (url: string, target?: string, features?: string) => {
            if (safeWindow) {
                return safeWindow.open(url, target, features)
            }
            return null
        },
        reload: () => {
            if (safeWindow?.location) {
                safeWindow.location.reload()
            }
        }
    }
}

/**
 * Hook to safely access location properties with destructuring
 * Usage: const { protocol, hostname, pathname } = useSafeLocation()
 */
export const useSafeLocation = () => {
    const { location } = useSafeWindow()
    return location
}

export default useSafeWindow
