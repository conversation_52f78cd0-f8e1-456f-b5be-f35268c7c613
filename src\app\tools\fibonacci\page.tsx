'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { HiArrowLeft, HiChartBar } from 'react-icons/hi2'
import { useRouter } from 'next/navigation'

const FibonacciCalculatorPage = () => {
    const router = useRouter()
    const [highPrice, setHighPrice] = useState('1.1000')
    const [lowPrice, setLowPrice] = useState('1.0800')
    const [trendDirection, setTrendDirection] = useState('uptrend')
    const [calculationType, setCalculationType] = useState('retracement')
    const [fibLevels, setFibLevels] = useState<Array<{level: number, price: number, percentage: string}>>([])

    const retracementLevels = [0, 23.6, 38.2, 50, 61.8, 78.6, 100]
    const extensionLevels = [0, 61.8, 100, 138.2, 161.8, 200, 261.8]

    useEffect(() => {
        calculateFibonacci()
    }, [highPrice, lowPrice, trendDirection, calculationType])

    const calculateFibonacci = () => {
        const high = parseFloat(highPrice) || 0
        const low = parseFloat(lowPrice) || 0
        
        if (high === 0 || low === 0 || high === low) {
            setFibLevels([])
            return
        }

        const range = high - low
        const levels = calculationType === 'retracement' ? retracementLevels : extensionLevels
        
        const calculatedLevels = levels.map(level => {
            let price: number
            
            if (calculationType === 'retracement') {
                if (trendDirection === 'uptrend') {
                    // For uptrend retracement, calculate from high down
                    price = high - (range * level / 100)
                } else {
                    // For downtrend retracement, calculate from low up
                    price = low + (range * level / 100)
                }
            } else {
                // Extension levels
                if (trendDirection === 'uptrend') {
                    price = high + (range * level / 100)
                } else {
                    price = low - (range * level / 100)
                }
            }

            return {
                level,
                price,
                percentage: `${level}%`
            }
        })

        setFibLevels(calculatedLevels)
    }

    

    const getLevelColor = (level: number) => {
        if (level === 0 || level === 100) return 'text-gray-600 dark:text-gray-400'
        if (level === 23.6 || level === 78.6) return 'text-[#28C76F]'
        if (level === 38.2 || level === 61.8) return 'text-[#EA5455]'
        if (level === 50) return 'text-gray-700 dark:text-gray-300'
        return 'text-[#28C76F]'
    }

    const getLevelImportance = (level: number) => {
        if (level === 38.2 || level === 61.8) return 'High'
        if (level === 50) return 'Medium'
        if (level === 23.6 || level === 78.6) return 'Medium'
        return 'Low'
    }

    return (
        <div className="mt-0">
            <PublicPageLayout>
            {/* Hero Section */}
            <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black py-20 overflow-hidden">
                <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >

                        <h1 className="text-5xl text-gray-300 mt-8 font-bold mb-6">
                            Fibonacci Calculator
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Calculate Fibonacci retracement and extension levels for technical analysis. 
                            Essential tool for identifying potential support, resistance, and target levels.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Calculator Section */}
            <section className="py-16 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto px-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        {/* Calculator Form */}
                        <motion.div
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl"
                        >
                            <div className="flex items-center gap-3 mb-8">
                                <div className="p-3 bg-[#EA5455] bg-opacity-10 rounded-xl">
                                    <HiChartBar className="w-6 h-6 text-[#EA5455]" />
                                </div>
                                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                                    Fibonacci Calculator
                                </h2>
                            </div>

                            <div className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Calculation Type
                                    </label>
                                    <div className="grid grid-cols-2 gap-3">
                                        <button
                                            onClick={() => setCalculationType('retracement')}
                                            className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                                                calculationType === 'retracement'
                                                    ? 'bg-[#EA5455] text-white'
                                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                            }`}
                                        >
                                            Retracement
                                        </button>
                                        <button
                                            onClick={() => setCalculationType('extension')}
                                            className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                                                calculationType === 'extension'
                                                    ? 'bg-[#EA5455] text-white'
                                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                            }`}
                                        >
                                            Extension
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Trend Direction
                                    </label>
                                    <div className="grid grid-cols-2 gap-3">
                                        <button
                                            onClick={() => setTrendDirection('uptrend')}
                                            className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                                                trendDirection === 'uptrend'
                                                    ? 'bg-[#28C76F] text-white'
                                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                            }`}
                                        >
                                            Uptrend
                                        </button>
                                        <button
                                            onClick={() => setTrendDirection('downtrend')}
                                            className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                                                trendDirection === 'downtrend'
                                                    ? 'bg-[#EA5455] text-white'
                                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                            }`}
                                        >
                                            Downtrend
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        High Price
                                    </label>
                                    <input
                                        type="number"
                                        value={highPrice}
                                        onChange={(e) => setHighPrice(e.target.value)}
                                        placeholder="1.1000"
                                        step="0.0001"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Low Price
                                    </label>
                                    <input
                                        type="number"
                                        value={lowPrice}
                                        onChange={(e) => setLowPrice(e.target.value)}
                                        placeholder="1.0800"
                                        step="0.0001"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                </div>

                                <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4">
                                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                                        How to Use:
                                    </h4>
                                    <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-1">
                                        <li>• <strong>Retracement:</strong> Measure pullbacks in trends</li>
                                        <li>• <strong>Extension:</strong> Project potential targets</li>
                                        <li>• <strong>High/Low:</strong> Use swing high and swing low points</li>
                                    </ul>
                                </div>
                            </div>
                        </motion.div>

                        {/* Results */}
                        <motion.div
                            initial={{ opacity: 0, x: 30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            className="space-y-6"
                        >
                            {fibLevels.length > 0 && (
                                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl">
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                        Fibonacci {calculationType === 'retracement' ? 'Retracement' : 'Extension'} Levels
                                    </h3>
                                    <div className="space-y-3">
                                        {fibLevels.map((fib, index) => (
                                            <div
                                                key={index}
                                                className="flex justify-between items-center py-3 px-4 bg-gray-50 dark:bg-gray-700 rounded-xl"
                                            >
                                                <div className="flex items-center gap-3">
                                                    <span className={`font-semibold ${getLevelColor(fib.level)}`}>
                                                        {fib.percentage}
                                                    </span>
                                                    <span className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded-full text-gray-600 dark:text-gray-400">
                                                        {getLevelImportance(fib.level)}
                                                    </span>
                                                </div>
                                                <span className="font-mono text-gray-900 dark:text-white">
                                                    {fib.price.toFixed(4)}
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Key Levels Info */}
                            <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                    Key Fibonacci Levels
                                </h3>
                                <div className="space-y-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-[#EA5455] font-semibold">38.2% & 61.8%</span>
                                        <span className="text-sm text-gray-600 dark:text-gray-400">Golden Ratios - Most Important</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-purple-600 dark:text-purple-400 font-semibold">50%</span>
                                        <span className="text-sm text-gray-600 dark:text-gray-400">Psychological Level</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-[#28C76F] font-semibold">23.6% & 78.6%</span>
                                        <span className="text-sm text-gray-600 dark:text-gray-400">Secondary Levels</span>
                                    </div>
                                </div>
                            </div>

                            {/* Educational Content */}
                            <div className="bg-[#EA5455]/10 dark:bg-[#EA5455]/20 border border-[#EA5455]/30 dark:border-[#EA5455]/40 rounded-xl p-6">
                                <h3 className="text-lg font-semibold text-[#EA5455] mb-3">
                                    Trading with Fibonacci
                                </h3>
                                <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                                    <li>• <strong>Support/Resistance:</strong> Fib levels act as key price zones</li>
                                    <li>• <strong>Entry Points:</strong> Look for bounces at retracement levels</li>
                                    <li>• <strong>Profit Targets:</strong> Use extension levels for take profits</li>
                                    <li>• <strong>Confirmation:</strong> Combine with other technical indicators</li>
                                </ul>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </section>
                    </PublicPageLayout>
        </div>
    )
}

export default FibonacciCalculatorPage
