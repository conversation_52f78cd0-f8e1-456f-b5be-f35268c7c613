'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbDeviceGamepad2, TbChartLine, TbTarget, TbShield, TbTrendingUp, TbSettings, TbBrain, TbClock } from 'react-icons/tb'

const TradingSimulatorPage = () => {
    const simulatorFeatures = [
        {
            icon: TbChartLine,
            title: 'Real Market Data',
            description: 'Practice with live market feeds and real-time price movements.',
            benefits: ['Live price feeds', 'Real market conditions', 'Historical data replay', 'Multiple timeframes']
        },
        {
            icon: TbTarget,
            title: 'Strategy Testing',
            description: 'Test and refine your trading strategies in a risk-free environment.',
            benefits: ['Strategy backtesting', 'Performance analytics', 'Risk assessment', 'Optimization tools']
        },
        {
            icon: TbShield,
            title: 'Risk-Free Learning',
            description: 'Learn and practice without any financial risk to your capital.',
            benefits: ['Virtual money', 'No real losses', 'Unlimited practice', 'Confidence building']
        },
        {
            icon: TbBrain,
            title: 'Advanced Analytics',
            description: 'Comprehensive performance tracking and detailed analytics.',
            benefits: ['Trade analysis', 'Performance metrics', 'Progress tracking', 'Detailed reports']
        }
    ]

    const simulatorTools = [
        {
            title: 'Virtual Portfolio',
            description: 'Manage a virtual portfolio with realistic market conditions.',
            icon: TbTrendingUp
        },
        {
            title: 'Strategy Builder',
            description: 'Create and test custom trading strategies.',
            icon: TbSettings
        },
        {
            title: 'Performance Analytics',
            description: 'Track your trading performance with detailed metrics.',
            icon: TbChartLine
        },
        {
            title: 'Risk Management',
            description: 'Practice proper risk management techniques.',
            icon: TbShield
        }
    ]

    const learningModules = [
        {
            title: 'Beginner Simulator',
            description: 'Start with basic trading concepts and simple strategies.',
            duration: '2-4 weeks',
            level: 'Beginner',
            features: ['Basic order types', 'Simple strategies', 'Guided tutorials', 'Progress tracking']
        },
        {
            title: 'Intermediate Simulator',
            description: 'Practice advanced techniques and complex market scenarios.',
            duration: '4-8 weeks',
            level: 'Intermediate',
            features: ['Advanced orders', 'Multiple timeframes', 'Technical indicators', 'Strategy testing']
        },
        {
            title: 'Professional Simulator',
            description: 'Master professional-level trading with institutional tools.',
            duration: '8-12 weeks',
            level: 'Advanced',
            features: ['Portfolio management', 'Advanced analytics', 'Custom strategies', 'Performance optimization']
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Trading Simulator
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Master trading skills in a virtual environment with real market conditions
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-[#28C76F] hover:bg-[#28C76F]/90 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors duration-300"
                                    >
                                        Start Simulator
                                    </button>
                                    <button 
                                        onClick={() => window.open('/education/demo-account', '_blank')}
                                        className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-[#1E1E1E] transition-colors duration-300"
                                    >
                                        Try Demo First
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Simulator Features Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Simulator Features
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Experience professional-grade trading simulation with comprehensive features.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {simulatorFeatures.map((feature, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#28C76F]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <feature.icon className="w-6 h-6 text-[#28C76F]" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                                    {feature.title}
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {feature.description}
                                                </p>
                                                <ul className="space-y-2">
                                                    {feature.benefits.map((benefit, benefitIndex) => (
                                                        <li key={benefitIndex} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                            <div className="w-1.5 h-1.5 bg-[#28C76F] rounded-full"></div>
                                                            <span>{benefit}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Simulator Tools Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Simulation Tools
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Access professional trading tools in a simulated environment.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                {simulatorTools.map((tool, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                                            <tool.icon className="w-6 h-6 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                            {tool.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {tool.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Learning Modules Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Learning Modules
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Progress through structured learning modules designed for different skill levels.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                {learningModules.map((module, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="text-center mb-6">
                                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                                module.level === 'Beginner' 
                                                    ? 'bg-[#28C76F]/10 text-[#28C76F]' 
                                                    : module.level === 'Intermediate'
                                                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                                                    : 'bg-[#EA5455]/10 text-[#EA5455]'
                                            }`}>
                                                {module.level}
                                            </span>
                                        </div>
                                        
                                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 text-center">
                                            {module.title}
                                        </h3>
                                        
                                        <p className="text-gray-600 dark:text-gray-300 mb-4 text-center">
                                            {module.description}
                                        </p>
                                        
                                        <div className="text-center mb-6">
                                            <span className="inline-flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
                                                <TbClock className="w-4 h-4" />
                                                <span>{module.duration}</span>
                                            </span>
                                        </div>
                                        
                                        <div className="space-y-2 mb-6">
                                            {module.features.map((feature, featureIndex) => (
                                                <div key={featureIndex} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                    <div className="w-1.5 h-1.5 bg-[#EA5455] rounded-full flex-shrink-0"></div>
                                                    <span>{feature}</span>
                                                </div>
                                            ))}
                                        </div>
                                        
                                        <button className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-3 rounded-lg font-semibold transition-colors duration-300">
                                            Start Module
                                        </button>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#28C76F] to-[#28C76F]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Start Your Virtual Trading Journey
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Experience realistic trading simulation with professional tools and real market data.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-white text-[#28C76F] px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Access Simulator
                                    </button>
                                    <button 
                                        onClick={() => window.open('/education/trading-basics', '_blank')}
                                        className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-[#28C76F] transition-colors duration-300"
                                    >
                                        Learn Basics First
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default TradingSimulatorPage
