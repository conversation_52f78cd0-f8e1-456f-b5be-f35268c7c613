'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbCalendar, TbTrendingUp, TbAlertTriangle, TbClock, TbGlobe, TbChartLine, TbNews, TbTarget } from 'react-icons/tb'

const EconomicCalendarPage = () => {
    const calendarFeatures = [
        {
            icon: TbCalendar,
            title: 'Real-Time Updates',
            description: 'Stay informed with live updates of economic events and data releases.',
            benefits: ['Live event updates', 'Real-time notifications', 'Instant market impact', 'Global coverage']
        },
        {
            icon: TbTrendingUp,
            title: 'Market Impact Analysis',
            description: 'Understand how economic events affect currency movements.',
            benefits: ['Impact ratings', 'Historical analysis', 'Market predictions', 'Volatility forecasts']
        },
        {
            icon: TbAlertTriangle,
            title: 'High-Impact Alerts',
            description: 'Get notified about high-impact events that can move markets.',
            benefits: ['Custom alerts', 'Priority notifications', 'Risk warnings', 'Trading opportunities']
        },
        {
            icon: TbGlobe,
            title: 'Global Coverage',
            description: 'Track economic events from major economies worldwide.',
            benefits: ['Multi-country data', 'Time zone support', 'Regional analysis', 'Cross-market effects']
        }
    ]

    const eventTypes = [
        {
            title: 'Central Bank Decisions',
            description: 'Interest rate decisions and monetary policy announcements.',
            impact: 'High',
            icon: TbTrendingUp,
            examples: ['Federal Reserve Rate Decision', 'ECB Policy Meeting', 'Bank of England Rate Decision']
        },
        {
            title: 'Employment Data',
            description: 'Job market indicators and unemployment statistics.',
            impact: 'High',
            icon: TbChartLine,
            examples: ['Non-Farm Payrolls', 'Unemployment Rate', 'Job Creation Numbers']
        },
        {
            title: 'Inflation Reports',
            description: 'Consumer price index and inflation measurements.',
            impact: 'Medium',
            icon: TbNews,
            examples: ['CPI Data', 'Core Inflation', 'Producer Price Index']
        },
        {
            title: 'GDP Releases',
            description: 'Economic growth and gross domestic product data.',
            impact: 'Medium',
            icon: TbTarget,
            examples: ['Quarterly GDP', 'GDP Growth Rate', 'Economic Output']
        }
    ]

    const tradingTips = [
        {
            tip: 'Plan Ahead',
            description: 'Review the economic calendar weekly to plan your trading strategy.',
            importance: 'Critical'
        },
        {
            tip: 'Focus on High-Impact Events',
            description: 'Prioritize events marked as high-impact for maximum trading opportunities.',
            importance: 'High'
        },
        {
            tip: 'Understand Market Expectations',
            description: 'Compare actual results with forecasts to gauge market reaction.',
            importance: 'High'
        },
        {
            tip: 'Manage Risk During News',
            description: 'Use appropriate position sizing and stop losses during volatile periods.',
            importance: 'Critical'
        },
        {
            tip: 'Consider Time Zones',
            description: 'Be aware of different time zones when planning trades around events.',
            importance: 'Medium'
        },
        {
            tip: 'Monitor Multiple Currencies',
            description: 'Track events affecting all currencies in your trading pairs.',
            importance: 'High'
        }
    ]

    const upcomingEvents = [
        {
            time: '08:30 GMT',
            country: 'USD',
            event: 'Non-Farm Payrolls',
            impact: 'High',
            forecast: '180K',
            previous: '175K'
        },
        {
            time: '12:00 GMT',
            country: 'EUR',
            event: 'ECB Interest Rate Decision',
            impact: 'High',
            forecast: '4.50%',
            previous: '4.50%'
        },
        {
            time: '14:00 GMT',
            country: 'GBP',
            event: 'GDP Growth Rate',
            impact: 'Medium',
            forecast: '0.2%',
            previous: '0.1%'
        },
        {
            time: '23:50 GMT',
            country: 'JPY',
            event: 'Bank of Japan Policy Rate',
            impact: 'High',
            forecast: '-0.10%',
            previous: '-0.10%'
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Economic Calendar
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Track important economic events and market-moving news to make informed trading decisions
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-colors duration-300"
                                    >
                                        Access Calendar
                                    </button>
                                    <button 
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#1E1E1E] transition-colors duration-300"
                                    >
                                        View Today's Events
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Calendar Features Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Calendar Features
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Stay ahead of market movements with our comprehensive economic calendar.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {calendarFeatures.map((feature, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <feature.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                                    {feature.title}
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {feature.description}
                                                </p>
                                                <ul className="space-y-2">
                                                    {feature.benefits.map((benefit, benefitIndex) => (
                                                        <li key={benefitIndex} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                            <div className="w-1.5 h-1.5 bg-[#EA5455] rounded-full"></div>
                                                            <span>{benefit}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Event Types Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Key Economic Events
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Understand the different types of economic events and their market impact.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {eventTypes.map((event, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#28C76F]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <event.icon className="w-6 h-6 text-[#28C76F]" />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between mb-3">
                                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                                                        {event.title}
                                                    </h3>
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                        event.impact === 'High' 
                                                            ? 'bg-[#EA5455]/10 text-[#EA5455]' 
                                                            : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                                                    }`}>
                                                        {event.impact} Impact
                                                    </span>
                                                </div>
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {event.description}
                                                </p>
                                                <div className="space-y-1">
                                                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Examples:</h4>
                                                    {event.examples.map((example, exampleIndex) => (
                                                        <div key={exampleIndex} className="text-sm text-gray-600 dark:text-gray-400">
                                                            • {example}
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Sample Events Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Today's Key Events
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Sample of important economic events that traders monitor daily.
                                </p>
                            </motion.div>

                            <div className="bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden">
                                <div className="grid grid-cols-6 gap-4 p-4 bg-gray-100 dark:bg-gray-600 font-semibold text-gray-900 dark:text-white text-sm">
                                    <div>Time</div>
                                    <div>Currency</div>
                                    <div className="col-span-2">Event</div>
                                    <div>Forecast</div>
                                    <div>Previous</div>
                                </div>
                                {upcomingEvents.map((event, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, x: -20 }}
                                        whileInView={{ opacity: 1, x: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="grid grid-cols-6 gap-4 p-4 border-b border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
                                    >
                                        <div className="text-sm text-gray-600 dark:text-gray-400">{event.time}</div>
                                        <div className="text-sm font-medium text-gray-900 dark:text-white">{event.country}</div>
                                        <div className="col-span-2 flex items-center space-x-2">
                                            <span className="text-sm text-gray-900 dark:text-white">{event.event}</span>
                                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                                                event.impact === 'High' 
                                                    ? 'bg-[#EA5455]/10 text-[#EA5455]' 
                                                    : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                                            }`}>
                                                {event.impact}
                                            </span>
                                        </div>
                                        <div className="text-sm text-gray-600 dark:text-gray-400">{event.forecast}</div>
                                        <div className="text-sm text-gray-600 dark:text-gray-400">{event.previous}</div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#EA5455] to-[#EA5455]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Stay Ahead of Market Events
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Access our comprehensive economic calendar and never miss important market-moving events.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-white text-[#EA5455] px-5 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Access Full Calendar
                                    </button>
                                    <button 
                                        onClick={() => window.open('/education/trading-basics', '_blank')}
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#EA5455] transition-colors duration-300"
                                    >
                                        Learn Trading Basics
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default EconomicCalendarPage
