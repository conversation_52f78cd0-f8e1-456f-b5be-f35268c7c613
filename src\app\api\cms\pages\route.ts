import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

// GET /api/cms/pages - Get all pages
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const status = searchParams.get('status')
        
        let query = 'SELECT * FROM pages'
        const params: any[] = []
        
        if (status) {
            query += ' WHERE status = ?'
            params.push(status)
        }
        
        query += ' ORDER BY created_at DESC'
        
        const [rows] = await pool.execute<RowDataPacket[]>(query, params)
        
        return NextResponse.json({
            success: true,
            data: rows
        })
    } catch (error) {
        console.error('Error fetching pages:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch pages' },
            { status: 500 }
        )
    }
}

// POST /api/cms/pages - Create new page
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { title, slug, content, seo_meta, status = 'draft' } = body
        
        if (!title || !slug) {
            return NextResponse.json(
                { success: false, error: 'Title and slug are required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO pages (title, slug, content, seo_meta, status) VALUES (?, ?, ?, ?, ?)',
            [title, slug, content, JSON.stringify(seo_meta), status]
        )
        
        return NextResponse.json({
            success: true,
            data: { id: result.insertId, title, slug, content, seo_meta, status }
        }, { status: 201 })
    } catch (error: any) {
        console.error('Error creating page:', error)
        
        if (error.code === 'ER_DUP_ENTRY') {
            return NextResponse.json(
                { success: false, error: 'Page with this slug already exists' },
                { status: 409 }
            )
        }
        
        return NextResponse.json(
            { success: false, error: 'Failed to create page' },
            { status: 500 }
        )
    }
}

// PUT /api/cms/pages - Update page
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { id, title, slug, content, seo_meta, status } = body
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Page ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE pages SET title = ?, slug = ?, content = ?, seo_meta = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [title, slug, content, JSON.stringify(seo_meta), status, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Page not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { id, title, slug, content, seo_meta, status }
        })
    } catch (error: any) {
        console.error('Error updating page:', error)
        
        if (error.code === 'ER_DUP_ENTRY') {
            return NextResponse.json(
                { success: false, error: 'Page with this slug already exists' },
                { status: 409 }
            )
        }
        
        return NextResponse.json(
            { success: false, error: 'Failed to update page' },
            { status: 500 }
        )
    }
}

// DELETE /api/cms/pages - Delete page
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Page ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM pages WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Page not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Page deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting page:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete page' },
            { status: 500 }
        )
    }
}
