'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import Dialog from '@/components/ui/Dialog'
import Input from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import Switcher from '@/components/ui/Switcher'
import AdminHydrationBoundary from '@/components/shared/AdminHydrationBoundary'
import { PlusIcon, EditIcon, TrashIcon, MoveIcon, ToggleLeftIcon, ToggleRightIcon, GripVerticalIcon } from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

interface Menu {
    id: number
    label: string
    slug: string
    parent_id: number | null
    icon: string | null
    order: number
    is_active: boolean
    created_at: string
    children?: Menu[]
}

const MenusManagement = () => {
    const [menus, setMenus] = useState<Menu[]>([])
    const [loading, setLoading] = useState(true)
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [editingMenu, setEditingMenu] = useState<Menu | null>(null)
    const [formData, setFormData] = useState({
        label: '',
        slug: '',
        parent_id: null as number | null,
        icon: '',
        order: 0,
        is_active: true
    })
    const [error, setError] = useState<string | null>(null)

    // Fetch menus from API
    const fetchMenus = async () => {
        try {
            setLoading(true)
            const response = await fetch('/api/cms/menus?tree=true')
            const data = await response.json()
            
            if (data.success) {
                setMenus(data.data)
            } else {
                setError(data.error || 'Failed to fetch menus')
            }
        } catch (err) {
            setError('Failed to fetch menus')
            console.error('Error fetching menus:', err)
        } finally {
            setLoading(false)
        }
    }

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        try {
            const url = '/api/cms/menus'
            const method = editingMenu ? 'PUT' : 'POST'
            const payload = editingMenu
                ? { ...formData, id: editingMenu.id }
                : formData

            const response = await fetch(url, {
                method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            })

            const data = await response.json()

            if (data.success) {
                toast.push(
                    <Notification title="Success" type="success">
                        Menu item {editingMenu ? 'updated' : 'created'} successfully
                    </Notification>
                )
                setIsDialogOpen(false)
                setEditingMenu(null)
                setFormData({
                    label: '',
                    slug: '',
                    parent_id: null,
                    icon: '',
                    order: 0,
                    is_active: true
                })
                fetchMenus()
            } else {
                toast.push(
                    <Notification title="Error" type="danger">
                        {data.error || `Failed to ${editingMenu ? 'update' : 'create'} menu item`}
                    </Notification>
                )
            }
        } catch (error) {
            console.error('Error saving menu item:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    Error saving menu item
                </Notification>
            )
        }
    }

    // Handle edit
    const handleEdit = (menu: Menu) => {
        setEditingMenu(menu)
        setFormData({
            label: menu.label,
            slug: menu.slug,
            parent_id: menu.parent_id,
            icon: menu.icon || '',
            order: menu.order,
            is_active: menu.is_active
        })
        setIsDialogOpen(true)
    }

    // Toggle menu active status
    const toggleActive = async (id: number, currentStatus: boolean) => {
        try {
            const response = await fetch('/api/cms/menus', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id,
                    is_active: !currentStatus
                })
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Menu item {!currentStatus ? 'activated' : 'deactivated'}
                    </Notification>
                )
                fetchMenus() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to update menu item'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to update menu item
                </Notification>
            )
            console.error('Error updating menu item:', err)
        }
    }

    // Delete menu item
    const deleteMenuItem = async (id: number) => {
        if (!confirm('Are you sure you want to delete this menu item? This will also delete all child items.')) {
            return
        }

        try {
            const response = await fetch(`/api/cms/menus?id=${id}`, {
                method: 'DELETE'
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Menu item deleted successfully
                    </Notification>
                )
                fetchMenus() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to delete menu item'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to delete menu item
                </Notification>
            )
            console.error('Error deleting menu item:', err)
        }
    }

    // Render menu item
    const renderMenuItem = (menu: Menu, level: number = 0) => (
        <div key={menu.id} className={`${level > 0 ? 'ml-6 border-l-2 border-gray-200 pl-4' : ''}`}>
            <div className="flex items-center justify-between p-3 border rounded-lg mb-2">
                <div className="flex items-center gap-3">
                    <MoveIcon className="h-4 w-4 text-muted-foreground cursor-move" />
                    <div>
                        <div className="flex items-center gap-2">
                            <p className="font-medium">{menu.label}</p>
                            <Badge className={menu.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                                {menu.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{menu.slug}</p>
                        {menu.icon && (
                            <p className="text-xs text-muted-foreground">Icon: {menu.icon}</p>
                        )}
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant="plain"
                        size="sm"
                        title={menu.is_active ? 'Deactivate' : 'Activate'}
                        onClick={() => toggleActive(menu.id, menu.is_active)}
                    >
                        {menu.is_active ? (
                            <ToggleRightIcon className="h-4 w-4" />
                        ) : (
                            <ToggleLeftIcon className="h-4 w-4" />
                        )}
                    </Button>
                    <Button
                        variant="plain"
                        size="sm"
                        title="Edit"
                        onClick={() => handleEdit(menu)}
                    >
                        <EditIcon className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="plain"
                        size="sm"
                        title="Delete"
                        onClick={() => deleteMenuItem(menu.id)}
                    >
                        <TrashIcon className="h-4 w-4" />
                    </Button>
                </div>
            </div>
            {menu.children && menu.children.length > 0 && (
                <div className="ml-4">
                    {menu.children.map(child => renderMenuItem(child, level + 1))}
                </div>
            )}
        </div>
    )

    useEffect(() => {
        fetchMenus()
    }, [])

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Navigation Menus</h1>
                        <p className="text-muted-foreground">
                            Manage website navigation structure and menu items
                        </p>
                    </div>
                </div>
                <Card className="p-6">
                    <div className="text-center">Loading menus...</div>
                </Card>
            </div>
        )
    }

    if (error) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Navigation Menus</h1>
                        <p className="text-muted-foreground">
                            Manage website navigation structure and menu items
                        </p>
                    </div>
                </div>
                <Card className="p-6">
                    <div className="text-center text-red-600">
                        Error: {error}
                        <br />
                        <Button onClick={fetchMenus} className="mt-4">
                            Retry
                        </Button>
                    </div>
                </Card>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Navigation Menus</h1>
                    <p className="text-muted-foreground">
                        Manage website navigation structure and menu items
                    </p>
                </div>
                <Button
                    className="flex items-center gap-2"
                    onClick={() => setIsDialogOpen(true)}
                >
                    <PlusIcon className="h-4 w-4" />
                    Add Menu Item
                </Button>
            </div>

            <Card
                header={{ content: "Menu Structure" }}
                className="p-6"
            >
                {menus.length === 0 ? (
                    <div className="text-center py-8">
                        <p className="text-muted-foreground">No menu items found</p>
                        <Button className="mt-4">
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Create Your First Menu Item
                        </Button>
                    </div>
                ) : (
                    <div className="space-y-3">
                        {menus.map(menu => renderMenuItem(menu))}
                    </div>
                )}
            </Card>

            {/* Add/Edit Menu Dialog */}
            <AdminHydrationBoundary>
                <Dialog
                    isOpen={isDialogOpen}
                    onRequestClose={() => {
                        setIsDialogOpen(false)
                        setEditingMenu(null)
                        setFormData({
                            label: '',
                            slug: '',
                            parent_id: null,
                            icon: '',
                            order: 0,
                            is_active: true
                        })
                    }}
                    width={500}
                >
                    <div className="p-6">
                        <h2 className="text-xl font-semibold mb-4">
                            {editingMenu ? 'Edit Menu Item' : 'Add New Menu Item'}
                        </h2>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <Label>Label *</Label>
                                <Input
                                    value={formData.label}
                                    onChange={(e) => setFormData({...formData, label: e.target.value})}
                                    placeholder="Menu label"
                                    required
                                />
                            </div>

                            <div>
                                <Label>Slug *</Label>
                                <Input
                                    value={formData.slug}
                                    onChange={(e) => setFormData({...formData, slug: e.target.value})}
                                    placeholder="menu-slug"
                                    required
                                />
                            </div>

                            <div>
                                <Label>Icon</Label>
                                <Input
                                    value={formData.icon}
                                    onChange={(e) => setFormData({...formData, icon: e.target.value})}
                                    placeholder="Icon name (optional)"
                                />
                            </div>

                            <div>
                                <Label>Order</Label>
                                <Input
                                    type="number"
                                    value={formData.order}
                                    onChange={(e) => setFormData({...formData, order: parseInt(e.target.value) || 0})}
                                    placeholder="0"
                                />
                            </div>

                            <div className="flex items-center gap-2">
                                <Switcher
                                    checked={formData.is_active}
                                    onChange={(checked) => setFormData({...formData, is_active: checked})}
                                />
                                <Label>Active</Label>
                            </div>

                            <div className="flex justify-end gap-2 pt-4">
                                <Button
                                    type="button"
                                    variant="plain"
                                    onClick={() => setIsDialogOpen(false)}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit" variant="solid">
                                    {editingMenu ? 'Update' : 'Create'}
                                </Button>
                            </div>
                        </form>
                    </div>
                </Dialog>
            </AdminHydrationBoundary>
        </div>
    )
}

export default MenusManagement
