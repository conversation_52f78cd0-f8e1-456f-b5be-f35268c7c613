'use client'

import { motion } from 'framer-motion'
import { TbTrendingUp, TbWorld, TbShield, Tb<PERSON>lock, TbChartLine, TbCurrencyDollar } from 'react-icons/tb'
import { HiArrowRight } from 'react-icons/hi2'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function ForexPage() {
    const majorPairs = [
        {
            name: 'EUR/USD',
            price: '1.0845',
            change: '+0.12%',
            flag: '🇪🇺🇺🇸'
        },
        {
            name: 'GBP/USD',
            price: '1.2634',
            change: '-0.08%',
            flag: '🇬🇧🇺🇸'
        },
        {
            name: 'USD/JPY',
            price: '149.85',
            change: '+0.25%',
            flag: '🇺🇸🇯🇵'
        },
        {
            name: 'USD/CHF',
            price: '0.8756',
            change: '+0.15%',
            flag: '🇺🇸🇨🇭'
        },
        {
            name: 'AUD/USD',
            price: '0.6523',
            change: '-0.18%',
            flag: '🇦🇺🇺🇸'
        },
        {
            name: 'USD/CAD',
            price: '1.3689',
            change: '+0.09%',
            flag: '🇺🇸🇨🇦'
        }
    ]

    const features = [
        {
            icon: TbTrendingUp,
            title: 'Tight Spreads',
            description: 'Trade with competitive spreads starting from 0.1 pips on major currency pairs.'
        },
        {
            icon: TbWorld,
            title: '24/7 Trading',
            description: 'Access the forex market 24 hours a day, 5 days a week across global sessions.'
        },
        {
            icon: TbShield,
            title: 'High Liquidity',
            description: 'Benefit from the world\'s most liquid market with $7.5 trillion daily volume.'
        },
        {
            icon: TbClock,
            title: 'Fast Execution',
            description: 'Execute trades in milliseconds with our advanced trading infrastructure.'
        }
    ]

    const tradingBenefits = [
        'Over 60 currency pairs available',
        'Leverage up to 1:500',
        'No commission on forex trades',
        'Advanced charting and analysis',
        'Expert advisors and automated trading',
        'Mobile and desktop platforms'
    ]

    const sessions = [
        {
            name: 'Sydney',
            time: '22:00 - 07:00 GMT',
            status: 'Closed',
            color: 'bg-gray-100'
        },
        {
            name: 'Tokyo',
            time: '00:00 - 09:00 GMT',
            status: 'Closed',
            color: 'bg-gray-100'
        },
        {
            name: 'London',
            time: '08:00 - 17:00 GMT',
            status: 'Open',
            color: 'bg-[#28C76F]'
        },
        {
            name: 'New York',
            time: '13:00 - 22:00 GMT',
            status: 'Open',
            color: 'bg-[#28C76F]'
        }
    ]

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-5xl text-gray-300 font-bold mb-6 mt-8">
                                Forex Trading
                            </h1>
                            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Trade the world's largest financial market with over 60 currency pairs.
                                Access tight spreads, high leverage, and 24/7 trading opportunities.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center"
                                >
                                    Start Trading Forex
                                    <HiArrowRight className="ml-2 w-5 h-5" />
                                </button>
                                <button
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')}
                                    className="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-gray-900 transition-colors duration-200"
                                >
                                    Try Demo Account
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* Live Rates Section */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Live Exchange Rates
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Real-time pricing on major currency pairs
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {majorPairs.map((pair, index) => (
                                <motion.div
                                    key={pair.name}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex items-center">
                                            <span className="text-2xl mr-3">{pair.flag}</span>
                                            <div>
                                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                                    {pair.name}
                                                </h3>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    Major Pair
                                                </p>
                                            </div>
                                        </div>
                                        <TbChartLine className="w-6 h-6 text-[#EA5455]" />
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {pair.price}
                                        </span>
                                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                            pair.change.startsWith('+') 
                                                ? 'bg-[#28C76F] bg-opacity-10 text-[#28C76F]'
                                                : 'bg-[#EA5455] bg-opacity-10 text-[#EA5455]'
                                        }`}>
                                            {pair.change}
                                        </span>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Trading Sessions */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Global Trading Sessions
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Trade around the clock across major financial centers
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {sessions.map((session, index) => (
                                <motion.div
                                    key={session.name}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="bg-gray-50 dark:bg-gray-700 p-6 rounded-2xl text-center"
                                >
                                    <div className={`w-4 h-4 ${session.color} rounded-full mx-auto mb-4`}></div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                        {session.name}
                                    </h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                                        {session.time}
                                    </p>
                                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                        session.status === 'Open' 
                                            ? 'bg-[#28C76F] bg-opacity-10 text-[#28C76F]'
                                            : 'bg-gray-100 bg-opacity-10 text-gray-400'
                                    }`}>
                                        {session.status}
                                    </span>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Why Trade Forex?
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Discover the advantages of forex trading
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <feature.icon className="w-8 h-8 text-white" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Trading Benefits Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                            <motion.div
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Professional Trading Platform
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                    Access the forex market with our advanced trading platform featuring 
                                    institutional-grade tools and lightning-fast execution.
                                </p>
                                <ul className="space-y-4">
                                    {tradingBenefits.map((benefit, index) => (
                                        <li key={index} className="flex items-center">
                                            <div className="w-2 h-2 bg-[#EA5455] rounded-full mr-4"></div>
                                            <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                                        </li>
                                    ))}
                                </ul>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, x: 30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl"
                            >
                                <div className="text-center">
                                    <TbCurrencyDollar className="w-16 h-16 text-[#EA5455] mx-auto mb-6" />
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        Start Trading Today
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        Open your forex trading account and access the world's largest 
                                        financial market with competitive conditions.
                                    </p>
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="w-full py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                    >
                                        Open Trading Account
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </section>
            </div>
        </PublicPageLayout>
    )
}
