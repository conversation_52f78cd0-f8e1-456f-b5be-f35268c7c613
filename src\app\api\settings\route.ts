import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

// GET /api/settings - Get all settings or specific setting
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const key = searchParams.get('key')
        
        if (key) {
            // Get specific setting
            const [rows] = await pool.execute<RowDataPacket[]>(
                'SELECT * FROM settings WHERE setting_key = ?',
                [key]
            )
            
            if (rows.length === 0) {
                return NextResponse.json(
                    { success: false, error: 'Setting not found' },
                    { status: 404 }
                )
            }
            
            const setting = rows[0]
            let value = setting.setting_value
            
            // Parse JSON values
            if (setting.setting_type === 'json') {
                try {
                    value = JSON.parse(setting.setting_value)
                } catch (e) {
                    // Keep as string if JSON parsing fails
                }
            } else if (setting.setting_type === 'boolean') {
                value = setting.setting_value === 'true'
            } else if (setting.setting_type === 'number') {
                value = parseFloat(setting.setting_value)
            }
            
            return NextResponse.json({
                success: true,
                data: { ...setting, setting_value: value }
            })
        } else {
            // Get all settings
            const [rows] = await pool.execute<RowDataPacket[]>(
                'SELECT * FROM settings ORDER BY setting_key'
            )
            
            // Parse values based on type
            const settings = rows.map(setting => {
                let value = setting.setting_value
                
                if (setting.setting_type === 'json') {
                    try {
                        value = JSON.parse(setting.setting_value)
                    } catch (e) {
                        // Keep as string if JSON parsing fails
                    }
                } else if (setting.setting_type === 'boolean') {
                    value = setting.setting_value === 'true'
                } else if (setting.setting_type === 'number') {
                    value = parseFloat(setting.setting_value)
                }
                
                return { ...setting, setting_value: value }
            })
            
            return NextResponse.json({
                success: true,
                data: settings
            })
        }
    } catch (error) {
        console.error('Error fetching settings:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch settings' },
            { status: 500 }
        )
    }
}

// POST /api/settings - Create new setting
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { setting_key, setting_value, setting_type = 'text', description } = body
        
        if (!setting_key || setting_value === undefined) {
            return NextResponse.json(
                { success: false, error: 'Setting key and value are required' },
                { status: 400 }
            )
        }
        
        // Validate setting type
        const validTypes = ['text', 'json', 'boolean', 'number']
        if (!validTypes.includes(setting_type)) {
            return NextResponse.json(
                { success: false, error: 'Invalid setting type' },
                { status: 400 }
            )
        }
        
        // Convert value to string for storage
        let valueToStore = setting_value
        if (setting_type === 'json') {
            valueToStore = JSON.stringify(setting_value)
        } else if (setting_type === 'boolean') {
            valueToStore = setting_value.toString()
        } else if (setting_type === 'number') {
            valueToStore = setting_value.toString()
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)',
            [setting_key, valueToStore, setting_type, description]
        )
        
        return NextResponse.json({
            success: true,
            data: { id: result.insertId, setting_key, setting_value, setting_type, description }
        }, { status: 201 })
    } catch (error: any) {
        console.error('Error creating setting:', error)
        
        if (error.code === 'ER_DUP_ENTRY') {
            return NextResponse.json(
                { success: false, error: 'Setting with this key already exists' },
                { status: 409 }
            )
        }
        
        return NextResponse.json(
            { success: false, error: 'Failed to create setting' },
            { status: 500 }
        )
    }
}

// PUT /api/settings - Update setting
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { setting_key, setting_value, setting_type, description } = body
        
        if (!setting_key || setting_value === undefined) {
            return NextResponse.json(
                { success: false, error: 'Setting key and value are required' },
                { status: 400 }
            )
        }
        
        // Convert value to string for storage
        let valueToStore = setting_value
        if (setting_type === 'json') {
            valueToStore = JSON.stringify(setting_value)
        } else if (setting_type === 'boolean') {
            valueToStore = setting_value.toString()
        } else if (setting_type === 'number') {
            valueToStore = setting_value.toString()
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE settings SET setting_value = ?, setting_type = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE setting_key = ?',
            [valueToStore, setting_type, description, setting_key]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Setting not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { setting_key, setting_value, setting_type, description }
        })
    } catch (error) {
        console.error('Error updating setting:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to update setting' },
            { status: 500 }
        )
    }
}

// DELETE /api/settings - Delete setting
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const key = searchParams.get('key')
        
        if (!key) {
            return NextResponse.json(
                { success: false, error: 'Setting key is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM settings WHERE setting_key = ?',
            [key]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Setting not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Setting deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting setting:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete setting' },
            { status: 500 }
        )
    }
}
