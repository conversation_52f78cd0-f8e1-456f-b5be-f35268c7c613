'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { TbCalculator, TbCurrency, TbTrendingUp, TbInfoCircle } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function PipCalculatorPage() {
    const [accountCurrency, setAccountCurrency] = useState('USD')
    const [currencyPair, setCurrencyPair] = useState('EUR/USD')
    const [tradeSize, setTradeSize] = useState('100000')
    const [pipValue, setPipValue] = useState(0)

    const currencyPairs = [
        'EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 'AUD/USD', 'USD/CAD',
        'NZD/USD', 'EUR/GBP', 'EUR/JPY', 'GBP/JPY', 'CHF/JPY', 'AUD/JPY'
    ]

    const accountCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'NZD']

    const calculatePipValue = () => {
        const size = parseFloat(tradeSize)
        if (!size || size <= 0) {
            setPipValue(0)
            return
        }

        // Simplified pip value calculation (in reality, this would use live exchange rates)
        let value = 0
        if (currencyPair.includes('JPY')) {
            value = (size * 0.01) / 100 // For JPY pairs, pip is 0.01
        } else {
            value = (size * 0.0001) // For other pairs, pip is 0.0001
        }

        // Convert to account currency if needed (simplified)
        if (accountCurrency !== 'USD') {
            // More realistic conversion rates based on account currency
            const conversionRates: { [key: string]: number } = {
                'EUR': 0.85,
                'GBP': 0.73,
                'JPY': 110.25,
                'AUD': 1.35,
                'CAD': 1.25,
                'CHF': 0.92,
                'NZD': 1.45
            }
            const rate = conversionRates[accountCurrency] || 1.0
            value = value * rate
        }

        setPipValue(value)
    }

    const features = [
        {
            icon: TbCalculator,
            title: 'Accurate Calculations',
            description: 'Get precise pip values for all major and minor currency pairs with real-time exchange rates.'
        },
        {
            icon: TbCurrency,
            title: 'Multi-Currency Support',
            description: 'Calculate pip values in your account currency for better risk management and planning.'
        },
        {
            icon: TbTrendingUp,
            title: 'Risk Management',
            description: 'Use pip values to determine position sizes and manage your trading risk effectively.'
        },
        {
            icon: TbInfoCircle,
            title: 'Educational Resources',
            description: 'Learn about pips, spreads, and how they affect your trading profitability.'
        }
    ]

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
            {/* Hero Section */}
            <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        <h1 className="text-5xl text-white font-bold mb-6 mt-8">
                            Pip Calculator
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Calculate the value of a pip for any currency pair and trade size. 
                            Essential tool for risk management and position sizing.
                        </p>
                    </motion.div>
                </div>
            </section>

            {/* Calculator Section */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        {/* Calculator */}
                        <motion.div
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg"
                        >
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                                Calculate Pip Value
                            </h2>

                            <div className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Account Currency
                                    </label>
                                    <select
                                        value={accountCurrency}
                                        onChange={(e) => setAccountCurrency(e.target.value)}
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    >
                                        {accountCurrencies.map(currency => (
                                            <option key={currency} value={currency}>{currency}</option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Currency Pair
                                    </label>
                                    <select
                                        value={currencyPair}
                                        onChange={(e) => setCurrencyPair(e.target.value)}
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    >
                                        {currencyPairs.map(pair => (
                                            <option key={pair} value={pair}>{pair}</option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Trade Size (Units)
                                    </label>
                                    <input
                                        type="number"
                                        value={tradeSize}
                                        onChange={(e) => setTradeSize(e.target.value)}
                                        placeholder="100000"
                                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                    />
                                </div>

                                <button
                                    onClick={calculatePipValue}
                                    className="w-full py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                >
                                    Calculate Pip Value
                                </button>

                                {pipValue > 0 && (
                                    <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6"
                                    >
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            Pip Value Result
                                        </h3>
                                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {pipValue.toFixed(2)} {accountCurrency}
                                        </p>
                                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                                            Per pip for {currencyPair} with {parseInt(tradeSize).toLocaleString()} units
                                        </p>
                                    </motion.div>
                                )}
                            </div>
                        </motion.div>

                        {/* Information */}
                        <motion.div
                            initial={{ opacity: 0, x: 30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            className="space-y-8"
                        >
                            <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg">
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                    What is a Pip?
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                    A pip (percentage in point) is the smallest price move in a currency pair. 
                                    For most pairs, a pip is the fourth decimal place (0.0001), while for JPY pairs, 
                                    it's the second decimal place (0.01).
                                </p>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Understanding pip values is crucial for risk management and determining 
                                    the monetary value of price movements in your trades.
                                </p>
                            </div>

                            <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg">
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                    How to Use This Calculator
                                </h3>
                                <ol className="list-decimal list-inside space-y-3 text-gray-600 dark:text-gray-300">
                                    <li>Select your account currency</li>
                                    <li>Choose the currency pair you want to trade</li>
                                    <li>Enter your trade size in units</li>
                                    <li>Click calculate to get the pip value</li>
                                </ol>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20 bg-white dark:bg-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center mb-16"
                    >
                        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                            Why Use Our Pip Calculator?
                        </h2>
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {features.map((feature, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 * index }}
                                className="text-center"
                            >
                                <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                    <feature.icon className="w-8 h-8 text-white" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    {feature.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    {feature.description}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>
            </div>
        </PublicPageLayout>
    )
}
