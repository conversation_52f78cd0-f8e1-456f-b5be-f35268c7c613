'use client'
import { useState } from 'react'
import NavList from './NavList'
import Menu from '@/components/ui/Menu'

import classNames from '@/utils/classNames'
import useScrollTop from '@/utils/hooks/useScrollTop'

import { TbMenu2, TbX } from 'react-icons/tb'
import {
    TrendingUp, BarChart3, Globe, Smartphone,
    Calculator, Settings, PieChart, TrendingDown,
    Users, Share2, DollarSign, Award,
    BookOpen, GraduationCap, Video, FileText,
    Headphones, MessageCircle, HelpCircle, Mail,
    Clock
} from 'lucide-react'
import Link from 'next/link'
import Logo from '@/components/template/Logo'
import type { Mode } from '@/@types/theme'
import type { ReactNode } from 'react'

type NavigationProps = {
    toggleMode: () => void
    mode: Mode
}

const navMenu = [
    {
        title: 'About',
        value: 'about',
        href: '/about',
    },
    {
        title: 'Trading',
        value: 'trading',
        href: '/trading',
        megaMenu: {
            sections: [
                {
                    title: 'Trading Platforms',
                    items: [
                        { title: 'Account Types', href: '/account-type', description: 'Choose the perfect trading account for you', icon: <Users className="w-4 h-4" /> },
                        { title: 'Web Platform', href: '/trading/web-platform', description: 'Trade directly from your browser', icon: <Globe className="w-4 h-4" /> },
                        { title: 'Desktop Platform', href: '/trading/desktop', description: 'Advanced desktop trading software', icon: <BarChart3 className="w-4 h-4" /> },
                        { title: 'Mobile Trading', href: '/trading/mobile', description: 'Trade on the go with our mobile app', icon: <Smartphone className="w-4 h-4" /> },
                    ]
                },
                {
                    title: 'Markets',
                    items: [
                        { title: 'Forex', href: '/trading/forex', description: 'Major, minor and exotic currency pairs', icon: <TrendingUp className="w-4 h-4" /> },
                        { title: 'Commodities', href: '/trading/commodities', description: 'Gold, silver, oil and more', icon: <BarChart3 className="w-4 h-4" /> },
                        { title: 'Indices', href: '/trading/indices', description: 'Global stock market indices', icon: <PieChart className="w-4 h-4" /> },
                        { title: 'Cryptocurrencies', href: '/trading/crypto', description: 'Bitcoin, Ethereum and altcoins', icon: <TrendingDown className="w-4 h-4" /> },
                    ]
                },
                {
                    title: 'Trading Tools',
                    items: [
                        { title: 'Market Analysis', href: '/trading/analysis', description: 'Technical and fundamental analysis', icon: <BarChart3 className="w-4 h-4" /> },
                        { title: 'Trading Signals', href: '/trading/signals', description: 'Expert trading recommendations', icon: <TrendingUp className="w-4 h-4" /> },
                        { title: 'Economic Calendar', href: '/trading/calendar', description: 'Stay updated with market events', icon: <Clock className="w-4 h-4" /> },
                        { title: 'Trading APIs', href: '/trading/apis', description: 'Integrate with our trading APIs', icon: <Settings className="w-4 h-4" /> },
                    ]
                }
            ]
        }
    },
    {
        title: 'Tools',
        value: 'tools',
        href: '/tools',
        megaMenu: {
            sections: [
                {
                    title: 'Calculators',
                    items: [
                        { title: 'Pip Calculator', href: '/tools/pip-calculator', description: 'Calculate pip values for your trades', icon: <Calculator className="w-4 h-4" /> },
                        { title: 'Margin Calculator', href: '/tools/margin-calculator', description: 'Determine required margin', icon: <Calculator className="w-4 h-4" /> },
                        { title: 'Profit Calculator', href: '/tools/profit-calculator', description: 'Calculate potential profits', icon: <DollarSign className="w-4 h-4" /> },
                        { title: 'Risk Calculator', href: '/tools/risk-calculator', description: 'Manage your trading risk', icon: <Settings className="w-4 h-4" /> },
                    ]
                },
                {
                    title: 'Market Data',
                    items: [
                        { title: 'Live Rates', href: '/tools/live-rates', description: 'Real-time currency rates', icon: <TrendingUp className="w-4 h-4" /> },
                        { title: 'Market Hours', href: '/tools/market-hours', description: 'Global market trading hours', icon: <Clock className="w-4 h-4" /> },
                        { title: 'Currency Converter', href: '/tools/currency-converter', description: 'Convert between currencies', icon: <Globe className="w-4 h-4" /> },
                        { title: 'Volatility Tracker', href: '/tools/volatility', description: 'Monitor market volatility', icon: <BarChart3 className="w-4 h-4" /> },
                    ]
                },
                {
                    title: 'Analysis Tools',
                    items: [
                        { title: 'Correlation Matrix', href: '/tools/correlation', description: 'Currency pair correlations', icon: <PieChart className="w-4 h-4" /> },
                        { title: 'Fibonacci Calculator', href: '/tools/fibonacci', description: 'Calculate Fibonacci levels', icon: <BarChart3 className="w-4 h-4" /> },
                        { title: 'Position Size Calculator', href: '/tools/position-size', description: 'Optimize position sizing', icon: <Calculator className="w-4 h-4" /> },
                        { title: 'Swap Calculator', href: '/tools/swap-calculator', description: 'Calculate overnight fees', icon: <DollarSign className="w-4 h-4" /> },
                    ]
                }
            ]
        }
    },
    {
        title: 'Affiliate',
        value: 'affiliate',
        href: '/affiliate',
        megaMenu: {
            sections: [
                {
                    title: 'Get Started',
                    items: [
                        { title: 'How It Works', href: '/affiliate/how-it-works', description: 'Learn about our affiliate program', icon: <BookOpen className="w-4 h-4" /> },
                        { title: 'Getting Started', href: '/affiliate/getting-started', description: 'Start earning commissions today', icon: <Users className="w-4 h-4" /> },
                        { title: 'Commission Structure', href: '/affiliate/commissions', description: 'Competitive commission rates', icon: <DollarSign className="w-4 h-4" /> },
                        { title: 'Payment Methods', href: '/affiliate/payments', description: 'Flexible payment options', icon: <Award className="w-4 h-4" /> },
                    ]
                },
                {
                    title: 'Resources',
                    items: [
                        { title: 'Marketing Materials', href: '/affiliate/materials', description: 'Banners, links and promotional content', icon: <Share2 className="w-4 h-4" /> },
                        { title: 'Tracking Tools', href: '/affiliate/tracking', description: 'Monitor your referrals and earnings', icon: <BarChart3 className="w-4 h-4" /> },
                        { title: 'Reports & Analytics', href: '/affiliate/reports', description: 'Detailed performance reports', icon: <PieChart className="w-4 h-4" /> },
                        { title: 'API Integration', href: '/affiliate/api', description: 'Integrate with our affiliate API', icon: <Settings className="w-4 h-4" /> },
                    ]
                },
                {
                    title: 'Support',
                    items: [
                        { title: 'Affiliate Portal', href: 'https://mbf.mybrokerforex.com/user/login', description: 'Access your affiliate dashboard', icon: <Globe className="w-4 h-4" /> },
                        { title: 'FAQ', href: '/affiliate/faq', description: 'Frequently asked questions', icon: <HelpCircle className="w-4 h-4" /> },
                        { title: 'Terms & Conditions', href: '/affiliate/terms', description: 'Affiliate program terms', icon: <FileText className="w-4 h-4" /> },
                        { title: 'Support Center', href: '/affiliate/support', description: 'Get help from our team', icon: <Headphones className="w-4 h-4" /> },
                    ]
                }
            ]
        }
    },
    {
        title: 'Promotion',
        value: 'promotion',
        href: '/promotion',
    },
    {
        title: 'Education',
        value: 'education',
        href: '/education-center',
        megaMenu: {
            sections: [
                {
                    title: 'Learning Resources',
                    items: [
                        { title: 'Trading Basics', href: '/education/trading-basics', description: 'Learn the fundamentals of forex trading', icon: <BookOpen className="w-4 h-4" /> },
                        { title: 'Advanced Strategies', href: '/education/advanced-strategies', description: 'Master advanced trading techniques', icon: <GraduationCap className="w-4 h-4" /> },
                        { title: 'Market Analysis', href: '/education/market-analysis', description: 'Technical and fundamental analysis guides', icon: <BarChart3 className="w-4 h-4" /> },
                        { title: 'Risk Management', href: '/education/risk-management', description: 'Protect your trading capital', icon: <Settings className="w-4 h-4" /> },
                    ]
                },
                {
                    title: 'Educational Content',
                    items: [
                        { title: 'Video Tutorials', href: '/education/video-tutorials', description: 'Step-by-step video lessons', icon: <Video className="w-4 h-4" /> },
                        { title: 'Trading Webinars', href: '/education/trading-webinars', description: 'Live and recorded webinars', icon: <Users className="w-4 h-4" /> },
                        { title: 'E-books & Guides', href: '/education/ebooks-guides', description: 'Comprehensive trading guides', icon: <FileText className="w-4 h-4" /> },
                        { title: 'Market News', href: '/education/market-news', description: 'Latest market updates and insights', icon: <Globe className="w-4 h-4" /> },
                    ]
                },
                {
                    title: 'Trading Tools',
                    items: [
                        { title: 'Demo Account', href: '/education/demo-account', description: 'Practice trading risk-free', icon: <Users className="w-4 h-4" /> },
                        { title: 'Trading Simulator', href: '/education/trading-simulator', description: 'Virtual trading environment', icon: <Smartphone className="w-4 h-4" /> },
                        { title: 'Economic Calendar', href: '/education/economic-calendar', description: 'Track important market events', icon: <Clock className="w-4 h-4" /> },
                        { title: 'Glossary', href: '/education/glossary', description: 'Trading terms and definitions', icon: <BookOpen className="w-4 h-4" /> },
                    ]
                }
            ]
        }
    },
    {
        title: 'Support',
        value: 'support',
        href: '/support',
    },
]

// Helper function to get contextually appropriate icons for menu items
const getMenuIcon = (title: string) => {
    const iconMap: { [key: string]: ReactNode } = {
        // Trading menu icons
        'Trading': <BarChart3 className="w-5 h-5" />,
        'Account Types': <Users className="w-4 h-4" />,
        'Web Platform': <Globe className="w-4 h-4" />,
        'Desktop Platform': <BarChart3 className="w-4 h-4" />,
        'Mobile Trading': <Smartphone className="w-4 h-4" />,
        'Forex': <TrendingUp className="w-4 h-4" />,
        'Commodities': <BarChart3 className="w-4 h-4" />,
        'Indices': <PieChart className="w-4 h-4" />,
        'Cryptocurrencies': <TrendingDown className="w-4 h-4" />,

        // Tools menu icons
        'Tools': <Calculator className="w-5 h-5" />,
        'Economic Calendar': <Clock className="w-4 h-4" />,
        'Trading Calculator': <Calculator className="w-4 h-4" />,
        'Market Analysis': <BarChart3 className="w-4 h-4" />,
        'Trading Signals': <TrendingUp className="w-4 h-4" />,
        'Risk Management': <Settings className="w-4 h-4" />,
        'Performance Analytics': <PieChart className="w-4 h-4" />,

        // Education menu icons
        'Education': <BookOpen className="w-5 h-5" />,
        'Trading Basics': <BookOpen className="w-4 h-4" />,
        'Advanced Strategies': <GraduationCap className="w-4 h-4" />,
        'Video Tutorials': <Video className="w-4 h-4" />,
        'Webinars': <Video className="w-4 h-4" />,
        'eBooks': <FileText className="w-4 h-4" />,
        'Glossary': <FileText className="w-4 h-4" />,

        // Support menu icons
        'Support': <Headphones className="w-5 h-5" />,
        'Live Chat': <MessageCircle className="w-4 h-4" />,
        'Help Center': <HelpCircle className="w-4 h-4" />,
        'Contact Us': <Mail className="w-4 h-4" />,

        // Affiliate menu icons
        'Affiliate': <Share2 className="w-5 h-5" />,
        'Partner Program': <Share2 className="w-4 h-4" />,
        'Referral Rewards': <Award className="w-4 h-4" />,
        'Commission Structure': <DollarSign className="w-4 h-4" />,

        // Default icons
        'Promotion': <Award className="w-5 h-5" />,
        'About': <FileText className="w-5 h-5" />,
        'Contact': <Mail className="w-5 h-5" />
    }

    return iconMap[title] || <BarChart3 className="w-4 h-4" />
}

const Navigation = ({ toggleMode, mode }: NavigationProps) => {
    const { isSticky } = useScrollTop()
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen)
    }

    const closeMobileMenu = () => {
        setIsMobileMenuOpen(false)
    }

    return (
        <div
            style={{ transition: 'all 0.2s ease-in-out' }}
            className={classNames(
                'w-full fixed inset-x-0 z-[50]',
                isSticky ? 'top-4' : 'top-0'
            )}
            suppressHydrationWarning
        >
            <div
                className={classNames(
                    'flex flex-row self-start items-center justify-between py-4 max-w-7xl mt-5 mx-auto px-6 rounded-xl relative z-[60] w-full transition duration-200',
                    isSticky
                        ? 'bg-white/70 dark:bg-gray-800/70 backdrop-blur-md shadow-lg'
                        : 'bg-white/70 dark:bg-gray-800/70 backdrop-blur-md'
                )}
            >
                {/* Logo */}
                <div className="flex items-center">
                    <Link href="/" className="flex items-center">
                        <Logo mode={mode} />
                    </Link>
                </div>

                {/* Desktop Navigation */}
                <div className="hidden lg:flex">
                    <NavList tabs={navMenu} />
                </div>

                {/* Desktop Actions */}
                <div className="hidden lg:flex items-center space-x-4">
                    <button
                        onClick={toggleMode}
                        className="p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
                        aria-label="Toggle theme"
                    >
                        {mode === 'dark' ? (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        ) : (
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                            </svg>
                        )}
                    </button>
                    <a
                        href="https://mbf.mybrokerforex.com/user/login"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                        Login
                    </a>
                    <a
                        href="https://mbf.mybrokerforex.com/user/register"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                        Open Account
                    </a>
                </div>

                {/* Mobile Menu Button */}
                <div className="lg:hidden">
                    <button
                        onClick={toggleMobileMenu}
                        className={classNames(
                            'p-2 rounded-lg transition-all duration-200',
                            'text-gray-700 dark:text-gray-300',
                            'hover:bg-gray-100 dark:hover:bg-gray-800',
                            'focus:outline-none focus:ring-2 focus:ring-[#EA5455]/20'
                        )}
                        aria-label="Toggle mobile menu"
                    >
                        {isMobileMenuOpen ? (
                            <TbX className="w-6 h-6" />
                        ) : (
                            <TbMenu2 className="w-6 h-6" />
                        )}
                    </button>
                </div>
            </div>

            {/* Mobile Menu */}
            {isMobileMenuOpen && (
                <div className="lg:hidden">
                    {/* Backdrop */}
                    <div
                        className="fixed inset-0 bg-black/20 z-30"
                        onClick={closeMobileMenu}
                    />
                    
                    {/* Menu Content */}
                    <div
                        className={classNames(
                            "fixed inset-0 bg-white dark:bg-gray-900 z-40 overflow-y-auto transition-all duration-200",
                            isSticky ? "top-[88px]" : "top-[72px]"
                        )}
                        style={{
                            top: isSticky ? '88px' : '72px'
                        }}
                    >
                        <div className="px-6 py-8">
                            <Menu className="space-y-1">
                                {navMenu.map((item) => (
                                    (item as any).megaMenu ? (
                                        <Menu.MenuCollapse
                                            key={item.value}
                                            eventKey={item.value}
                                            label={
                                                <span className="flex items-center gap-3">
                                                    {getMenuIcon(item.title)}
                                                    <span className="font-medium">{item.title}</span>
                                                </span>
                                            }
                                            className="text-gray-900 dark:text-white hover:text-[#EA5455] dark:hover:text-[#EA5455] transition-colors"
                                        >
                                            {(item as any).megaMenu.sections.map((section: any, sectionIndex: number) => (
                                                section.items.map((subItem: any, subIndex: number) => (
                                                    <Menu.MenuItem
                                                        key={`${sectionIndex}-${subIndex}`}
                                                        eventKey={`${item.value}-${sectionIndex}-${subIndex}`}
                                                        className="text-gray-700 dark:text-gray-300 hover:text-[#EA5455] dark:hover:text-[#EA5455] transition-colors"
                                                        onSelect={() => {
                                                            window.location.href = subItem.href
                                                            closeMobileMenu()
                                                        }}
                                                    >
                                                        <span className="flex items-center gap-3">
                                                            {getMenuIcon(subItem.title)}
                                                            <span>{subItem.title}</span>
                                                        </span>
                                                    </Menu.MenuItem>
                                                ))
                                            ))}
                                        </Menu.MenuCollapse>
                                    ) : (
                                        <Menu.MenuItem
                                            key={item.value}
                                            eventKey={item.value}
                                            className="text-gray-900 dark:text-white hover:text-[#EA5455] dark:hover:text-[#EA5455] transition-colors"
                                            onSelect={() => {
                                                window.location.href = item.href
                                                closeMobileMenu()
                                            }}
                                        >
                                            <span className="flex items-center gap-3">
                                                {getMenuIcon(item.title)}
                                                <span className="font-medium">{item.title}</span>
                                            </span>
                                        </Menu.MenuItem>
                                    )
                                ))}
                            </Menu>
                            
                            {/* Mobile CTA */}
                            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 space-y-3">
                                {/* Home and Login Links */}
                                <div className="flex gap-3 mb-4">
                                    <Link
                                        href="/"
                                        className="flex-1 text-center bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white px-3 py-2 rounded-lg font-medium transition-all duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 text-sm"
                                        onClick={closeMobileMenu}
                                    >
                                        Home
                                    </Link>
                                    <a
                                        href="https://mbf.mybrokerforex.com/user/login"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="flex-1 text-center border-2 border-[#EA5455] text-[#EA5455] hover:bg-[#EA5455] hover:text-white px-3 py-2 rounded-lg font-medium transition-all duration-300 text-sm"
                                        onClick={closeMobileMenu}
                                    >
                                        Login
                                    </a>
                                </div>

                                {/* Main CTA */}
                                <a
                                    href="https://mbf.mybrokerforex.com/user/register"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="block text-center bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-4 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
                                    onClick={closeMobileMenu}
                                >
                                    Open Trading Account
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

export default Navigation
