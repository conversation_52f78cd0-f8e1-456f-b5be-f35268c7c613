const mysql = require('mysql2/promise');

async function debugLogoUpload() {
    let connection;
    
    try {
        // Create connection
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'mybrokerforex'
        });

        console.log('Connected to MySQL database');

        // Check if settings table exists
        const [tables] = await connection.execute("SHOW TABLES LIKE 'settings'");
        console.log('Settings table exists:', tables.length > 0);

        if (tables.length > 0) {
            // Describe settings table structure
            const [structure] = await connection.execute("DESCRIBE settings");
            console.log('\nSettings table structure:');
            structure.forEach(col => {
                console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'} ${col.Key ? `[${col.Key}]` : ''}`);
            });

            // Check current logo setting
            const [logoRows] = await connection.execute(
                "SELECT * FROM settings WHERE setting_key = 'site_logo'"
            );
            console.log('\nCurrent logo setting:');
            if (logoRows.length > 0) {
                console.log(logoRows[0]);
            } else {
                console.log('No logo setting found');
            }

            // Check all settings
            const [allSettings] = await connection.execute("SELECT * FROM settings");
            console.log('\nAll settings:');
            allSettings.forEach(setting => {
                console.log(`${setting.setting_key}: ${setting.setting_value}`);
            });
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

debugLogoUpload();
