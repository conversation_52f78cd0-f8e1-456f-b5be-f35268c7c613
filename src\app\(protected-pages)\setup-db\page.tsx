'use client'

import { useState } from 'react'
import But<PERSON> from '@/components/ui/Button'

const SetupDatabase = () => {
    const [message, setMessage] = useState('')
    const [loading, setLoading] = useState(false)

    const setupUserProfilesTable = async () => {
        setLoading(true)
        setMessage('Creating user_profiles table...')
        
        try {
            const response = await fetch('/api/setup/user-profiles-table', {
                method: 'POST',
            })
            
            const result = await response.json()
            
            if (result.success) {
                setMessage('✅ User profiles table created successfully!')
            } else {
                setMessage(`❌ Error: ${result.error}`)
            }
        } catch (error) {
            setMessage(`❌ Error: ${error}`)
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className="p-8">
            <h1 className="text-2xl font-bold mb-4">Database Setup</h1>
            <div className="space-y-4">
                <Button 
                    onClick={setupUserProfilesTable}
                    disabled={loading}
                    variant="solid"
                >
                    {loading ? 'Setting up...' : 'Setup User Profiles Table'}
                </Button>
                
                {message && (
                    <div className="p-4 border rounded-md">
                        {message}
                    </div>
                )}
            </div>
        </div>
    )
}

export default SetupDatabase
