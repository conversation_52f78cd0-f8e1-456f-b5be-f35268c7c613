'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { PlusIcon, EditIcon, TrashIcon, EyeIcon } from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import PostEditor from './PostEditor'

interface Post {
    id: number
    title: string
    slug: string
    content: string
    excerpt: string
    category: string
    image: string
    status: 'draft' | 'published' | 'archived'
    featured: boolean
    created_at: string
    updated_at: string
}

const BlogManagement = () => {
    const [posts, setPosts] = useState<Post[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [showEditor, setShowEditor] = useState(false)
    const [editingPost, setEditingPost] = useState<Post | undefined>(undefined)

    // Fetch posts from API
    const fetchPosts = async () => {
        try {
            setLoading(true)
            const response = await fetch('/api/cms/posts')
            const data = await response.json()
            
            if (data.success) {
                setPosts(data.data)
            } else {
                setError(data.error || 'Failed to fetch posts')
            }
        } catch (err) {
            setError('Failed to fetch posts')
            console.error('Error fetching posts:', err)
        } finally {
            setLoading(false)
        }
    }

    // Delete post
    const deletePost = async (id: number) => {
        if (!confirm('Are you sure you want to delete this post?')) {
            return
        }

        try {
            const response = await fetch(`/api/cms/posts?id=${id}`, {
                method: 'DELETE'
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Post deleted successfully
                    </Notification>
                )
                fetchPosts() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to delete post'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to delete post
                </Notification>
            )
            console.error('Error deleting post:', err)
        }
    }

    // Get status badge color
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800'
            case 'draft':
                return 'bg-yellow-100 text-yellow-800'
            case 'archived':
                return 'bg-gray-100 text-gray-800'
            default:
                return 'bg-gray-100 text-gray-800'
        }
    }

    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    }

    // Handle create new post
    const handleCreatePost = () => {
        setEditingPost(undefined)
        setShowEditor(true)
    }

    // Handle edit post
    const handleEditPost = (post: Post) => {
        setEditingPost(post)
        setShowEditor(true)
    }

    // Handle editor save
    const handleEditorSave = () => {
        setShowEditor(false)
        setEditingPost(undefined)
        fetchPosts() // Refresh the list
    }

    // Handle editor cancel
    const handleEditorCancel = () => {
        setShowEditor(false)
        setEditingPost(undefined)
    }

    useEffect(() => {
        fetchPosts()
    }, [])

    // Show editor if in edit mode
    if (showEditor) {
        return (
            <PostEditor
                post={editingPost}
                onSave={handleEditorSave}
                onCancel={handleEditorCancel}
            />
        )
    }

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Blog Management</h1>
                        <p className="text-muted-foreground">
                            Create and manage blog posts and articles
                        </p>
                    </div>
                </div>
                <Card className="p-6">
                    <div className="text-center">Loading posts...</div>
                </Card>
            </div>
        )
    }

    if (error) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Blog Management</h1>
                        <p className="text-muted-foreground">
                            Create and manage blog posts and articles
                        </p>
                    </div>
                </div>
                <Card className="p-6">
                    <div className="text-center text-red-600">
                        Error: {error}
                        <br />
                        <Button onClick={fetchPosts} className="mt-4">
                            Retry
                        </Button>
                    </div>
                </Card>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Blog Management</h1>
                    <p className="text-muted-foreground">
                        Create and manage blog posts and articles
                    </p>
                </div>
                <Button onClick={handleCreatePost} icon={<PlusIcon />}>
                    <span>Add New Post</span>
                </Button>
            </div>

            <Card
                header={{ content: `All Blog Posts (${posts.length})` }}
                className="p-6"
            >
                    {posts.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-muted-foreground">No posts found</p>
                            <Button className="mt-4" onClick={handleCreatePost} icon={<PlusIcon />}>
                                <span>Create Your First Post</span>
                            </Button>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {posts.map((post) => (
                                <div key={post.id} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-2">
                                            <h3 className="font-semibold">{post.title}</h3>
                                            <Badge className={getStatusColor(post.status)}>
                                                {post.status}
                                            </Badge>
                                            {post.featured && (
                                                <Badge className="bg-blue-100 text-blue-800">
                                                    Featured
                                                </Badge>
                                            )}
                                        </div>
                                        <p className="text-sm text-muted-foreground mb-1">
                                            {post.excerpt || 'No excerpt available'}
                                        </p>
                                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                            <span>Slug: /{post.slug}</span>
                                            {post.category && <span>Category: {post.category}</span>}
                                            <span>Updated: {formatDate(post.updated_at)}</span>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button variant="plain" size="sm" title="View" icon={<EyeIcon />}>
                                        </Button>
                                        <Button
                                            variant="plain"
                                            size="sm"
                                            title="Edit"
                                            onClick={() => handleEditPost(post)}
                                            icon={<EditIcon />}
                                        >
                                        </Button>
                                        <Button
                                            variant="plain"
                                            size="sm"
                                            title="Delete"
                                            onClick={() => deletePost(post.id)}
                                            icon={<TrashIcon />}
                                        >
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
            </Card>
        </div>
    )
}

export default BlogManagement
