'use client'

import { motion } from 'framer-motion'
import { Tb<PERSON>rain, TbChartLine, TbTarget, TbShield, TbTrendingUp, TbClock } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

const AdvancedStrategiesPage = () => {
    const strategies = [
        {
            icon: TbBrain,
            title: 'Price Action Trading',
            description: 'Master the art of reading price movements without indicators.',
            difficulty: 'Advanced',
            duration: '4-6 hours',
            topics: ['Candlestick Patterns', 'Support/Resistance', 'Breakouts', 'Market Structure']
        },
        {
            icon: TbChartLine,
            title: 'Fibonacci Trading',
            description: 'Use Fibonacci retracements and extensions for precise entries.',
            difficulty: 'Intermediate',
            duration: '3-4 hours',
            topics: ['Retracement Levels', 'Extension Levels', 'Golden Ratio', 'Confluence Zones']
        },
        {
            icon: TbTarget,
            title: 'Harmonic Patterns',
            description: 'Identify and trade harmonic price patterns for high-probability setups.',
            difficulty: 'Advanced',
            duration: '5-7 hours',
            topics: ['<PERSON><PERSON><PERSON> Pattern', 'Butterfly Pattern', 'Bat Pattern', 'Crab Pattern']
        },
        {
            icon: TbShield,
            title: 'Risk Management',
            description: 'Advanced risk management techniques for professional traders.',
            difficulty: 'Intermediate',
            duration: '3-4 hours',
            topics: ['Portfolio Management', 'Correlation Analysis', 'Risk Allocation', 'Drawdown Control']
        }
    ]

    const advancedTopics = [
        {
            title: 'Multi-Timeframe Analysis',
            description: 'Combine multiple timeframes for comprehensive market analysis.',
            icon: TbTrendingUp
        },
        {
            title: 'Order Flow Analysis',
            description: 'Understand market microstructure and order flow dynamics.',
            icon: TbChartLine
        },
        {
            title: 'Volatility Trading',
            description: 'Strategies for trading in different volatility environments.',
            icon: TbBrain
        },
        {
            title: 'News Trading',
            description: 'Capitalize on high-impact economic news releases.',
            icon: TbClock
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Advanced Strategies
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Master sophisticated trading techniques for experienced traders
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-[#EA5455] text-white px-5 py-2 rounded-xl font-semibold hover:bg-[#d63384] transition-colors duration-200"
                                >
                                    Access Advanced Content
                                </button>
                            </motion.div>
                        </div>
                    </section>

                    {/* Strategies Grid */}
                    <section className="py-20">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Advanced Trading Strategies
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Sophisticated techniques for experienced traders looking to enhance their skills
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {strategies.map((strategy, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                    >
                                        <div className="flex items-center justify-between mb-6">
                                            <div className="flex items-center">
                                                <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                                    <strategy.icon className="w-6 h-6 text-[#EA5455]" />
                                                </div>
                                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                                    {strategy.title}
                                                </h3>
                                            </div>
                                            <span className={`px-3 py-1 rounded-lg text-sm font-medium ${
                                                strategy.difficulty === 'Advanced' 
                                                    ? 'bg-[#EA5455]/10 text-[#EA5455]' 
                                                    : 'bg-[#28C76F]/10 text-[#28C76F]'
                                            }`}>
                                                {strategy.difficulty}
                                            </span>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                                            {strategy.description}
                                        </p>
                                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                                            <TbClock className="w-4 h-4 mr-1" />
                                            Duration: {strategy.duration}
                                        </div>
                                        <ul className="space-y-2">
                                            {strategy.topics.map((topic, topicIndex) => (
                                                <li key={topicIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                    <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                    {topic}
                                                </li>
                                            ))}
                                        </ul>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Advanced Topics */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Specialized Topics
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Deep dive into specialized trading concepts and methodologies
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                {advancedTopics.map((topic, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                                            <topic.icon className="w-6 h-6 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                            {topic.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {topic.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-[#EA5455]">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Ready to Master Advanced Trading?
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                    Access our complete library of advanced trading strategies and techniques
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-white text-[#EA5455] px-5 py-2 rounded-xl font-semibold hover:bg-gray-100 transition-colors duration-200"
                                >
                                    Get Advanced Access
                                </button>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default AdvancedStrategiesPage 