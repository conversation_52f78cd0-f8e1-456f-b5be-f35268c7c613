import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket } from 'mysql2'

// GET /api/cms/pages/stats - Get pages statistics
export async function GET(request: NextRequest) {
    try {
        // Get total count
        const [totalResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as total FROM pages'
        )
        const total = totalResult[0].total

        // Get published count
        const [publishedResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM pages WHERE status = "published"'
        )
        const published = publishedResult[0].count

        // Get draft count
        const [draftResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM pages WHERE status = "draft"'
        )
        const draft = draftResult[0].count

        // Get archived count
        const [archivedResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM pages WHERE status = "archived"'
        )
        const archived = archivedResult[0].count

        // Get recent pages
        const [recentPages] = await pool.execute<RowDataPacket[]>(
            'SELECT id, title, status, created_at, updated_at FROM pages ORDER BY updated_at DESC LIMIT 5'
        )

        return NextResponse.json({
            success: true,
            data: {
                total,
                published,
                draft,
                archived,
                recent: recentPages
            }
        })
    } catch (error) {
        console.error('Error fetching pages stats:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch pages statistics' },
            { status: 500 }
        )
    }
} 