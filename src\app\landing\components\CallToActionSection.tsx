'use client'

import { motion } from 'framer-motion'
import Container from './LandingContainer'
import { TbArrowRight, TbDownload, TbPhone } from 'react-icons/tb'
import { useRouter } from 'next/navigation'
import type { Mode } from '@/@types/theme'

type CallToActionSectionProps = {
    mode: Mode
}

const CallToActionSection = ({ mode }: CallToActionSectionProps) => {
    const router = useRouter()

    const handleStartTrading = () => {
        if (typeof window !== 'undefined') {
            window.open('https://mbf.mybrokerforex.com/user/register', '_blank')
        }
    }

    const handleContactUs = () => {
        router.push('/contact')
    }

    const handleDownloadPlatform = () => {
        // This would typically trigger a download or redirect to platform download page
        if (typeof window !== 'undefined') {
            window.open('/products/trading-platform', '_blank')
        }
    }

    return (
        <Container>
            <div className="max-w-7xl mx-auto px-4 py-20">
                {/* Main CTA Section */}
                <motion.div 
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="bg-[#1E1E1E] rounded-3xl p-8 md:p-12 text-white relative overflow-hidden"
                >
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-10">
                        <div className="absolute top-0 right-0 w-64 h-64 bg-[#EA5455] rounded-full blur-3xl"></div>
                        <div className="absolute bottom-0 left-0 w-64 h-64 bg-[#28C76F] rounded-full blur-3xl"></div>
                    </div>

                    <div className="relative z-10 text-center">
                        <motion.h2
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                            className="text-3xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white"
                        >
                            Ready to Start Your <span className="text-[#EA5455]">Trading Journey</span>?
                        </motion.h2>

                        <motion.p
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                            className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto"
                        >
                            Join thousands of successful traders who trust MyBrokerForex. 
                            Open your account today and start trading with competitive spreads and professional support.
                        </motion.p>

                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
                        >
                            <button
                                onClick={handleStartTrading}
                                className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center gap-2 group"
                            >
                                Start Trading Now
                                <TbArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                            </button>
                            
                            <button
                                onClick={handleDownloadPlatform}
                                className="border-2 border-white text-white hover:bg-white hover:text-[#1E1E1E] px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center gap-2"
                            >
                                <TbDownload className="h-5 w-5" />
                                Download Platform
                            </button>
                        </motion.div>

                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.5 }}
                            className="mt-8 text-sm text-gray-400"
                        >
                            <p>No hidden fees • Regulated broker • 24/7 support</p>
                        </motion.div>
                    </div>
                </motion.div>

                {/* Secondary CTA Options */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
                    <motion.div 
                        initial={{ opacity: 0, x: -30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-300"
                    >
                        <div className="flex items-center gap-4 mb-4">
                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-full flex items-center justify-center">
                                <TbPhone className="h-6 w-6 text-[#EA5455]" />
                            </div>
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Need Help Getting Started?</h3>
                                <p className="text-gray-600 dark:text-gray-300">Speak with our trading experts</p>
                            </div>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                            Our experienced team is here to help you choose the right account type and get started with trading.
                        </p>
                        <button
                            onClick={handleContactUs}
                            className="text-[#EA5455] hover:text-[#EA5455]/80 font-semibold flex items-center gap-2 transition-colors"
                        >
                            Contact Our Experts
                            <TbArrowRight className="h-4 w-4" />
                        </button>
                    </motion.div>

                    <motion.div 
                        initial={{ opacity: 0, x: 30 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                        className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-300"
                    >
                        <div className="flex items-center gap-4 mb-4">
                            <div className="w-12 h-12 bg-[#28C76F]/10 rounded-full flex items-center justify-center">
                                <TbDownload className="h-6 w-6 text-[#28C76F]" />
                            </div>
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Try Our Demo Account</h3>
                                <p className="text-gray-600 dark:text-gray-300">Practice with virtual funds</p>
                            </div>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                            Test our platform and practice your trading strategies with $10,000 in virtual funds.
                        </p>
                        <button
                            onClick={() => window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')}
                            className="text-[#28C76F] hover:text-[#28C76F]/80 font-semibold flex items-center gap-2 transition-colors"
                        >
                            Open Demo Account
                            <TbArrowRight className="h-4 w-4" />
                        </button>
                    </motion.div>
                </div>

                {/* Trust Indicators */}
                <motion.div 
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mt-16 text-center"
                >
                    <p className="text-gray-600 dark:text-gray-300 mb-6">Trusted by traders worldwide</p>
                    <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
                        <div className="text-sm font-semibold text-gray-900 dark:text-white">Regulated by FCA</div>
                        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
                        <div className="text-sm font-semibold text-gray-900 dark:text-white">Segregated Funds</div>
                        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
                        <div className="text-sm font-semibold text-gray-900 dark:text-white">SSL Encrypted</div>
                        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
                        <div className="text-sm font-semibold text-gray-900 dark:text-white">24/7 Support</div>
                    </div>
                </motion.div>
            </div>
        </Container>
    )
}

export default CallToActionSection
