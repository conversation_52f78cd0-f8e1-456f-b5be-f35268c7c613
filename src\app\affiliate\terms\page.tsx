'use client'

import { motion } from 'framer-motion'
import { TbFileText, TbShield, TbUsers, TbCash } from 'react-icons/tb'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateTermsPage = () => {
    const termsSections = [
        {
            title: 'Program Overview',
            content: [
                'The MyBrokerForex Affiliate Program allows qualified individuals and entities to earn commissions by referring new clients to our trading platform.',
                'Affiliates earn competitive commissions for each qualified client they refer who opens an account and begins trading.',
                'This agreement governs the relationship between MyBrokerForex and its affiliates.'
            ]
        },
        {
            title: 'Eligibility Requirements',
            content: [
                'Affiliates must be at least 18 years of age or the legal age in their jurisdiction.',
                'Affiliates must have a legitimate website, social media presence, or marketing channel.',
                'Affiliates must comply with all applicable laws and regulations in their jurisdiction.',
                'MyBrokerForex reserves the right to approve or reject affiliate applications at its discretion.'
            ]
        },
        {
            title: 'Commission Structure',
            content: [
                'Commission rates are tiered based on the number of qualified referrals per month.',
                'Commissions are calculated based on the trading volume of referred clients.',
                'Minimum payout thresholds apply and vary by payment method.',
                'Commission rates may be adjusted with 30 days written notice.'
            ]
        },
        {
            title: 'Marketing Guidelines',
            content: [
                'Affiliates must use only approved marketing materials provided by MyBrokerForex.',
                'Affiliates may not engage in spam, unsolicited email marketing, or deceptive practices.',
                'All marketing content must comply with financial services regulations.',
                'Affiliates must clearly disclose their relationship with MyBrokerForex in all marketing materials.'
            ]
        },
        {
            title: 'Prohibited Activities',
            content: [
                'Self-referrals or referring family members is strictly prohibited.',
                'Bidding on MyBrokerForex branded keywords in paid search campaigns.',
                'Creating misleading or false advertising about MyBrokerForex services.',
                'Engaging in any illegal or unethical marketing practices.'
            ]
        },
        {
            title: 'Payment Terms',
            content: [
                'Commissions are paid monthly, bi-weekly, or weekly based on affiliate preference.',
                'Payments are subject to minimum thresholds and processing fees may apply.',
                'MyBrokerForex reserves the right to withhold payments for suspected fraudulent activity.',
                'All payments are made in USD unless otherwise agreed upon.'
            ]
        },
        {
            title: 'Termination',
            content: [
                'Either party may terminate this agreement with 30 days written notice.',
                'MyBrokerForex may terminate immediately for breach of terms or fraudulent activity.',
                'Upon termination, affiliates must cease all promotional activities immediately.',
                'Outstanding commissions will be paid according to the regular payment schedule.'
            ]
        },
        {
            title: 'Liability and Disclaimers',
            content: [
                'MyBrokerForex is not liable for any indirect, incidental, or consequential damages.',
                'Affiliates are responsible for their own tax obligations related to commission income.',
                'This agreement is governed by the laws of the jurisdiction where MyBrokerForex is incorporated.',
                'Any disputes will be resolved through binding arbitration.'
            ]
        }
    ]

    const keyPoints = [
        {
            icon: TbUsers,
            title: 'Qualified Referrals',
            description: 'Earn commissions only for legitimate client referrals who meet our qualification criteria.'
        },
        {
            icon: TbCash,
            title: 'Transparent Payments',
            description: 'Clear commission structure with regular payments and detailed reporting.'
        },
        {
            icon: TbShield,
            title: 'Compliance Required',
            description: 'All marketing activities must comply with financial regulations and our guidelines.'
        },
        {
            icon: TbFileText,
            title: 'Clear Terms',
            description: 'Comprehensive terms and conditions to protect both parties in the partnership.'
        }
    ]

    return (
        <PageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                Terms & Conditions
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Affiliate program terms and conditions for MyBrokerForex partners
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* Key Points */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Key Points
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Important highlights from our affiliate terms and conditions
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {keyPoints.map((point, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-xl p-6 text-center shadow-lg"
                                >
                                    <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                                        <point.icon className="w-6 h-6 text-[#EA5455]" />
                                    </div>
                                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                        {point.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                                        {point.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Terms Content */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-4xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Detailed Terms & Conditions
                            </h2>
                            <p className="text-lg text-gray-600 dark:text-gray-300">
                                Last updated: January 2024
                            </p>
                        </motion.div>

                        <div className="space-y-12">
                            {termsSections.map((section, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8"
                                >
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                        {index + 1}. {section.title}
                                    </h3>
                                    <div className="space-y-4">
                                        {section.content.map((paragraph, paragraphIndex) => (
                                            <p key={paragraphIndex} className="text-gray-600 dark:text-gray-300 leading-relaxed">
                                                {paragraph}
                                            </p>
                                        ))}
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Important Notice */}
                <section className="py-20">
                    <div className="max-w-4xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-gradient-to-r from-[#EA5455] to-[#d63384] rounded-2xl p-8 text-white"
                        >
                            <div className="text-center mb-6">
                                <TbShield className="w-16 h-16 mx-auto mb-4 text-white/80" />
                                <h2 className="text-3xl font-bold mb-4">Important Notice</h2>
                            </div>
                            <div className="space-y-4 text-white/90">
                                <p>
                                    <strong>Risk Warning:</strong> Trading foreign exchange and contracts for difference (CFDs) involves significant risk and may not be suitable for all investors. Past performance is not indicative of future results.
                                </p>
                                <p>
                                    <strong>Regulatory Compliance:</strong> Affiliates must ensure their marketing activities comply with all applicable financial services regulations in their jurisdiction.
                                </p>
                                <p>
                                    <strong>Changes to Terms:</strong> MyBrokerForex reserves the right to modify these terms with 30 days notice. Continued participation constitutes acceptance of modified terms.
                                </p>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* Contact Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-4xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                        >
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                                Questions About Our Terms?
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                Contact our affiliate support team for clarification on any terms or conditions
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button 
                                    onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                                    className="bg-[#EA5455] text-white px-8 py-4 rounded-xl font-semibold hover:bg-[#d63384] transition-colors duration-200"
                                >
                                    Contact Affiliate Support
                                </button>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                    className="bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white px-8 py-4 rounded-xl font-semibold hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors duration-200"
                                >
                                    Join Affiliate Program
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PageLayout>
    )
}

export default AffiliateTermsPage
