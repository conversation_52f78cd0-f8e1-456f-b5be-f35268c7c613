export interface AnalyticData {
    thisMonth: {
        metrics: {
            visitors: {
                value: number
                growShrink: number
            }
            conversionRate: {
                value: number
                growShrink: number
            }
            adCampaignClicks: {
                value: number
                growShrink: number
            }
        }
        webAnalytic: {
            pageView: {
                value: number
                growShrink: number
            }
            avgTimeOnPage: {
                value: string
                growShrink: number
            }
            series: Array<{
                name: string
                data: number[]
            }>
            date: string[]
        }
        topChannel: {
            visitors: number
            channels: Array<{
                id: string
                name: string
                img: string
                total: number
                percentage: number
            }>
        }
        deviceSession: {
            labels: string[]
            series: number[]
            percentage: number[]
        }
        topPerformingPages: Array<{
            page: string
            views: number
            uniqueViews: number
            bounceRate: number
        }>
    }
}

export interface MetricCardProps {
    title: string
    value: number | string
    growShrink: number
    icon: React.ComponentType<any>
    format?: 'number' | 'percentage' | 'string'
}

export interface TrafficData {
    series: Array<{
        name: string
        data: number[]
    }>
    categories: string[]
}

export interface DeviceSessionData {
    labels: string[]
    series: number[]
    percentage: number[]
}

export interface TopChannelData {
    visitors: number
    channels: Array<{
        id: string
        name: string
        img: string
        total: number
        percentage: number
    }>
}
