'use client'

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import AnalyticHeader from './AnalyticHeader'
import Metrics from './Metrics'
import Loading from '@/components/shared/Loading'
import ErrorBoundary from '@/components/shared/ErrorBoundary'
import type { AnalyticData } from '../types'

// Dynamic imports for chart components to avoid SSR issues
const AnalyticChart = dynamic(() => import('./AnalyticChart'), {
    ssr: false,
    loading: () => <div className="h-80 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse" />
})

const Traffic = dynamic(() => import('./Traffic'), {
    ssr: false,
    loading: () => <div className="h-80 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse" />
})

const DeviceSession = dynamic(() => import('./DeviceSession'), {
    ssr: false,
    loading: () => <div className="h-80 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse" />
})

const CMSDashboard = dynamic(() => import('./CMSDashboard'), {
    ssr: false,
    loading: () => <div className="h-80 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse" />
})

import TopChannel from './TopChannel'
import TopPerformingPages from './TopPerformingPages'

interface AnalyticDashboardProps {
    initialData: AnalyticData
}

const AnalyticDashboard = ({ initialData }: AnalyticDashboardProps) => {
    const [data, setData] = useState<AnalyticData>(initialData)
    const [isRefreshing, setIsRefreshing] = useState(false)
    const [activeTab, setActiveTab] = useState<'analytics' | 'cms'>('analytics')

    // Simulate real-time data updates
    useEffect(() => {
        const updateData = () => {
            setData(prevData => ({
                ...prevData,
                thisMonth: {
                    ...prevData.thisMonth,
                    metrics: {
                        ...prevData.thisMonth.metrics,
                        visitors: {
                            ...prevData.thisMonth.metrics.visitors,
                            value: prevData.thisMonth.metrics.visitors.value + Math.floor(Math.random() * 10) - 5
                        }
                    }
                }
            }))
        }

        // Update data every 2 minutes
        const interval = setInterval(updateData, 120000)
        return () => clearInterval(interval)
    }, [])

    const handleRefresh = async () => {
        setIsRefreshing(true)
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Update with fresh data (simulate real API response)
        setData(prevData => ({
            ...prevData,
            thisMonth: {
                ...prevData.thisMonth,
                metrics: {
                    visitors: {
                        value: Math.floor(Math.random() * 10000) + 5000,
                        growShrink: Math.floor(Math.random() * 40) - 20
                    },
                    conversionRate: {
                        value: Math.floor(Math.random() * 10) + 2,
                        growShrink: Math.floor(Math.random() * 20) - 10
                    },
                    adCampaignClicks: {
                        value: Math.floor(Math.random() * 5000) + 1000,
                        growShrink: Math.floor(Math.random() * 30) - 15
                    }
                }
            }
        }))
        
        setIsRefreshing(false)
    }

    const handleExport = () => {
        // Simulate export functionality
        const dataStr = JSON.stringify(data, null, 2)
        const dataBlob = new Blob([dataStr], { type: 'application/json' })
        const url = URL.createObjectURL(dataBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `analytics-${new Date().toISOString().split('T')[0]}.json`
        link.click()
        URL.revokeObjectURL(url)
    }

    return (
        <ErrorBoundary>
            <div className="space-y-6">
                {/* Tab Navigation */}
                <div className="border-b border-gray-200 dark:border-gray-700">
                    <nav className="-mb-px flex space-x-8">
                        <button
                            onClick={() => setActiveTab('analytics')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'analytics'
                                    ? 'border-[#EA5455] text-[#EA5455]'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                        >
                            Analytics Dashboard
                        </button>
                        <button
                            onClick={() => setActiveTab('cms')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'cms'
                                    ? 'border-[#EA5455] text-[#EA5455]'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                        >
                            Content Management
                        </button>
                    </nav>
                </div>

                {/* Analytics Tab */}
                {activeTab === 'analytics' && (
                    <>
                        <AnalyticHeader
                            onRefresh={handleRefresh}
                            onExport={handleExport}
                        />

                        <Metrics data={data.thisMonth} />

                        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                            <div className="xl:col-span-2">
                                <AnalyticChart data={data.thisMonth} />
                            </div>
                        </div>

                        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                            <Traffic data={data.thisMonth} />
                            <DeviceSession data={data.thisMonth} />
                        </div>

                        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                            <TopChannel data={data.thisMonth} />
                            <TopPerformingPages data={data.thisMonth} />
                        </div>
                    </>
                )}

                {/* CMS Tab */}
                {activeTab === 'cms' && (
                    <CMSDashboard />
                )}
            </div>
        </ErrorBoundary>
    )
}

export default AnalyticDashboard
