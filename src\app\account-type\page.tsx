import { Metadata } from 'next'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export const metadata: Metadata = {
    title: 'Account Types - MyBrokerForex',
    description: 'Choose from our range of trading account types designed to meet different trading needs. From beginner-friendly accounts to professional trading solutions.',
}

export default function AccountTypePage() {
    return (
        <div className="mt-0">
            <PublicPageLayout>
                <div className="max-w-7xl mx-auto px-6 py-16">
                    {/* Hero Section */}
                    <div className="text-center mb-16">
                        <h1 className="text-4xl md:text-5xl mt-8 font-bold text-gray-900 dark:text-white mb-6">
                            Choose Your Trading Account
                        </h1>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            Select the perfect account type that matches your trading style, experience level, and investment goals. All accounts come with competitive spreads and professional support.
                        </p>
                    </div>

                    {/* Account Types */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                        {/* Standard Account */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 border border-gray-200 dark:border-gray-700">
                            <div className="text-center mb-6">
                                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Standard Account</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">Perfect for beginners and casual traders</p>
                            </div>
                            <div className="space-y-4 mb-6">
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Minimum Deposit:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">$100</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Spread From:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">1.5 pips</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Leverage:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">1:100</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Commission:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">No</span>
                                </div>
                            </div>
                            <ul className="space-y-3 mb-6">
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    MetaTrader 4 & 5
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    50+ Currency Pairs
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    24/7 Support
                                </li>
                            </ul>
                            <a
                                href="https://mbf.mybrokerforex.com/user/register"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl text-center block"
                            >
                                Open Account
                            </a>
                        </div>

                        {/* Premium Account */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 border-2 border-[#EA5455] relative">
                            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                <span className="bg-[#EA5455] text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
                            </div>
                            <div className="text-center mb-6">
                                <div className="w-16 h-16 bg-[#EA5455] rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Premium Account</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">For experienced traders seeking better conditions</p>
                            </div>
                            <div className="space-y-4 mb-6">
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Minimum Deposit:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">$1,000</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Spread From:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">0.8 pips</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Leverage:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">1:200</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Commission:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">$3/lot</span>
                                </div>
                            </div>
                            <ul className="space-y-3 mb-6">
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    All Standard Features
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Priority Support
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Market Analysis
                                </li>
                            </ul>
                            <a
                                href="https://mbf.mybrokerforex.com/user/register"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl text-center block"
                            >
                                Open Account
                            </a>
                        </div>

                        {/* VIP Account */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 border border-gray-200 dark:border-gray-700">
                            <div className="text-center mb-6">
                                <div className="w-16 h-16 bg-[#28C76F] rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">VIP Account</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">Exclusive benefits for professional traders</p>
                            </div>
                            <div className="space-y-4 mb-6">
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Minimum Deposit:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">$10,000</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Spread From:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">0.3 pips</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Leverage:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">1:500</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-300">Commission:</span>
                                    <span className="font-semibold text-gray-900 dark:text-white">$2/lot</span>
                                </div>
                            </div>
                            <ul className="space-y-3 mb-6">
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    All Premium Features
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Dedicated Account Manager
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Exclusive Trading Tools
                                </li>
                            </ul>
                            <a
                                href="https://mbf.mybrokerforex.com/user/register"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full bg-[#28C76F] hover:bg-[#28C76F]/90 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl text-center block"
                            >
                                Open Account
                            </a>
                        </div>
                    </div>

                    {/* Comparison Table */}
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                        <div className="px-8 py-6 border-b border-gray-200 dark:border-gray-700">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Account Comparison</h2>
                        </div>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-900 dark:text-white">Feature</th>
                                        <th className="px-6 py-4 text-center text-sm font-medium text-gray-900 dark:text-white">Standard</th>
                                        <th className="px-6 py-4 text-center text-sm font-medium text-gray-900 dark:text-white">Premium</th>
                                        <th className="px-6 py-4 text-center text-sm font-medium text-gray-900 dark:text-white">VIP</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr>
                                        <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">Minimum Deposit</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">$100</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">$1,000</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">$10,000</td>
                                    </tr>
                                    <tr>
                                        <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">Spread From</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">1.5 pips</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">0.8 pips</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">0.3 pips</td>
                                    </tr>
                                    <tr>
                                        <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">Maximum Leverage</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">1:100</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">1:200</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">1:500</td>
                                    </tr>
                                    <tr>
                                        <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">Account Manager</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">-</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">Shared</td>
                                        <td className="px-6 py-4 text-sm text-center text-gray-600 dark:text-gray-300">Dedicated</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </PublicPageLayout>
        </div>
    )
}
