'use client'

import { motion } from 'framer-motion'
import { TbChevronDown, TbChevronUp, TbQuestionMark, TbCash, TbUsers, TbShield } from 'react-icons/tb'
import { useState } from 'react'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateFaqPage = () => {
    const [openFaq, setOpenFaq] = useState<number | null>(null)

    const faqCategories = [
        {
            icon: TbQuestionMark,
            title: 'General Questions',
            color: 'text-blue-500'
        },
        {
            icon: TbCash,
            title: 'Payments & Commissions',
            color: 'text-[#28C76F]'
        },
        {
            icon: TbUsers,
            title: 'Marketing & Promotion',
            color: 'text-[#EA5455]'
        },
        {
            icon: TbShield,
            title: 'Compliance & Terms',
            color: 'text-orange-500'
        }
    ]

    const faqs = [
        {
            category: 'General',
            question: 'How do I become a MyBrokerForex affiliate?',
            answer: 'To become an affiliate, simply complete our online registration form, provide the required documentation, and wait for approval. The process typically takes 24-48 hours.'
        },
        {
            category: 'General',
            question: 'Is there a cost to join the affiliate program?',
            answer: 'No, joining our affiliate program is completely free. There are no setup fees, monthly fees, or hidden costs.'
        },
        {
            category: 'General',
            question: 'What marketing materials do you provide?',
            answer: 'We provide a comprehensive suite of marketing materials including banners, logos, landing pages, email templates, and educational content. All materials are professionally designed and regularly updated.'
        },
        {
            category: 'Payments',
            question: 'How much commission can I earn?',
            answer: 'Commission rates are tiered based on performance, ranging from $400 to $1,200 per qualified client. Higher volume affiliates can earn additional bonuses and revenue sharing opportunities.'
        },
        {
            category: 'Payments',
            question: 'When do I get paid?',
            answer: 'Payments are made according to your chosen schedule: weekly (minimum $500), bi-weekly (minimum $250), or monthly (minimum $50). Payments are processed within 2-3 business days.'
        },
        {
            category: 'Payments',
            question: 'What payment methods are available?',
            answer: 'We offer multiple payment methods including bank transfers, credit/debit cards, and popular e-wallets like PayPal, Skrill, and Neteller.'
        },
        {
            category: 'Marketing',
            question: 'Can I use paid advertising to promote MyBrokerForex?',
            answer: 'Yes, you can use paid advertising, but you must follow our marketing guidelines. This includes not bidding on our branded keywords and ensuring all ads comply with financial regulations.'
        },
        {
            category: 'Marketing',
            question: 'Can I promote on social media?',
            answer: 'Absolutely! Social media promotion is encouraged. We provide social media assets and guidelines to help you create compliant and effective campaigns.'
        },
        {
            category: 'Marketing',
            question: 'How do I track my referrals?',
            answer: 'You can track all your referrals through our affiliate dashboard, which provides real-time analytics including clicks, conversions, and earnings. You can also generate custom tracking links for different campaigns.'
        },
        {
            category: 'Compliance',
            question: 'What are the main compliance requirements?',
            answer: 'You must clearly disclose your affiliate relationship, follow all applicable financial regulations, use only approved marketing materials, and avoid any misleading or false advertising.'
        },
        {
            category: 'Compliance',
            question: 'Can I refer myself or family members?',
            answer: 'No, self-referrals and referring immediate family members is strictly prohibited and will result in account termination.'
        },
        {
            category: 'Compliance',
            question: 'What happens if I violate the terms?',
            answer: 'Violations may result in warnings, commission forfeiture, or account termination depending on the severity. We always provide clear communication about any issues and work with affiliates to resolve them.'
        }
    ]

    const toggleFaq = (index: number) => {
        setOpenFaq(openFaq === index ? null : index)
    }

    return (
        <PageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                Frequently Asked Questions
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Find answers to common questions about our affiliate program
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* FAQ Categories */}
                <section className="py-16 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-12"
                        >
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                Browse by Category
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                            {faqCategories.map((category, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                        <category.icon className={`w-8 h-8 ${category.color}`} />
                                    </div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                        {category.title}
                                    </h3>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* FAQ List */}
                <section className="py-20">
                    <div className="max-w-4xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Common Questions
                            </h2>
                        </motion.div>

                        <div className="space-y-4">
                            {faqs.map((faq, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.4, delay: index * 0.05 }}
                                    className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden"
                                >
                                    <button
                                        onClick={() => toggleFaq(index)}
                                        className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                                    >
                                        <div className="flex items-center">
                                            <span className="px-3 py-1 bg-[#EA5455] text-white text-sm rounded-full mr-4">
                                                {faq.category}
                                            </span>
                                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                {faq.question}
                                            </h3>
                                        </div>
                                        {openFaq === index ? (
                                            <TbChevronUp className="w-5 h-5 text-gray-500" />
                                        ) : (
                                            <TbChevronDown className="w-5 h-5 text-gray-500" />
                                        )}
                                    </button>
                                    {openFaq === index && (
                                        <motion.div
                                            initial={{ opacity: 0, height: 0 }}
                                            animate={{ opacity: 1, height: 'auto' }}
                                            exit={{ opacity: 0, height: 0 }}
                                            transition={{ duration: 0.3 }}
                                            className="px-6 pb-4"
                                        >
                                            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                                                {faq.answer}
                                            </p>
                                        </motion.div>
                                    )}
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Still Have Questions */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-4xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-gradient-to-r from-[#EA5455] to-[#d63384] rounded-2xl p-8 text-white"
                        >
                            <h2 className="text-3xl font-bold mb-4">
                                Still Have Questions?
                            </h2>
                            <p className="text-xl text-white/90 mb-8">
                                Our affiliate support team is here to help you succeed
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button 
                                    onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                                    className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-colors duration-200"
                                >
                                    Contact Support
                                </button>
                                <button 
                                    onClick={() => window.location.href = '/affiliate/support'}
                                    className="bg-white/20 text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/30 transition-colors duration-200"
                                >
                                    Visit Support Center
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* Quick Links */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                                Helpful Resources
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {[
                                { title: 'Getting Started Guide', description: 'Step-by-step guide to becoming an affiliate', link: '/affiliate/getting-started' },
                                { title: 'Marketing Materials', description: 'Download banners, logos, and promotional content', link: '/affiliate/materials' },
                                { title: 'Commission Structure', description: 'Learn about our competitive commission rates', link: '/affiliate/commissions' }
                            ].map((resource, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                        {resource.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        {resource.description}
                                    </p>
                                    <button 
                                        onClick={() => window.location.href = resource.link}
                                        className="text-[#EA5455] hover:text-[#d63384] font-medium transition-colors duration-200"
                                    >
                                        Learn More →
                                    </button>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-[#EA5455]">
                    <div className="max-w-7xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                Ready to Start Earning?
                            </h2>
                            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                Join our affiliate program and start earning competitive commissions
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                Join Affiliate Program
                            </button>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PageLayout>
    )
}

export default AffiliateFaqPage
