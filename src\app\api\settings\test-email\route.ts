import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { to, settings } = body

        if (!to || !settings) {
            return NextResponse.json(
                { success: false, error: 'Email address and settings are required' },
                { status: 400 }
            )
        }

        // Create transporter with the provided settings
        const transporter = nodemailer.createTransport({
            host: settings.smtp_host,
            port: parseInt(settings.smtp_port),
            secure: settings.smtp_encryption === 'ssl',
            auth: {
                user: settings.smtp_username,
                pass: settings.smtp_password,
            },
            tls: {
                rejectUnauthorized: false
            }
        })

        // Test email content
        const mailOptions = {
            from: `"${settings.from_name}" <${settings.from_email}>`,
            to: to,
            subject: 'MyBrokerForex - Email Configuration Test',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background: linear-gradient(135deg, #EA5455, #28C76F); padding: 20px; text-align: center;">
                        <h1 style="color: white; margin: 0;">MyBrokerForex</h1>
                        <p style="color: white; margin: 10px 0 0 0;">Email Configuration Test</p>
                    </div>
                    
                    <div style="padding: 30px; background: #f8f9fa;">
                        <h2 style="color: #333; margin-bottom: 20px;">✅ Email Configuration Successful!</h2>
                        
                        <p style="color: #666; line-height: 1.6;">
                            Congratulations! Your email configuration is working correctly. This test email was sent successfully using your SMTP settings.
                        </p>
                        
                        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28C76F;">
                            <h3 style="color: #333; margin-top: 0;">Configuration Details:</h3>
                            <ul style="color: #666; line-height: 1.8;">
                                <li><strong>SMTP Host:</strong> ${settings.smtp_host}</li>
                                <li><strong>Port:</strong> ${settings.smtp_port}</li>
                                <li><strong>Encryption:</strong> ${settings.smtp_encryption.toUpperCase()}</li>
                                <li><strong>From Email:</strong> ${settings.from_email}</li>
                                <li><strong>From Name:</strong> ${settings.from_name}</li>
                            </ul>
                        </div>
                        
                        <p style="color: #666; line-height: 1.6;">
                            Your email system is now ready to send notifications, password resets, and other important communications to your users.
                        </p>
                        
                        <div style="text-align: center; margin-top: 30px;">
                            <p style="color: #999; font-size: 14px;">
                                This is an automated test email from MyBrokerForex Admin Panel<br>
                                Sent on ${new Date().toLocaleString()}
                            </p>
                        </div>
                    </div>
                    
                    <div style="background: #333; padding: 20px; text-align: center;">
                        <p style="color: #999; margin: 0; font-size: 14px;">
                            © ${new Date().getFullYear()} MyBrokerForex. All rights reserved.
                        </p>
                    </div>
                </div>
            `,
            text: `
MyBrokerForex - Email Configuration Test

✅ Email Configuration Successful!

Congratulations! Your email configuration is working correctly. This test email was sent successfully using your SMTP settings.

Configuration Details:
- SMTP Host: ${settings.smtp_host}
- Port: ${settings.smtp_port}
- Encryption: ${settings.smtp_encryption.toUpperCase()}
- From Email: ${settings.from_email}
- From Name: ${settings.from_name}

Your email system is now ready to send notifications, password resets, and other important communications to your users.

This is an automated test email from MyBrokerForex Admin Panel
Sent on ${new Date().toLocaleString()}

© ${new Date().getFullYear()} MyBrokerForex. All rights reserved.
            `
        }

        // Send the email
        await transporter.sendMail(mailOptions)

        return NextResponse.json({
            success: true,
            message: 'Test email sent successfully'
        })

    } catch (error: any) {
        console.error('Test email error:', error)
        
        let errorMessage = 'Failed to send test email'
        
        if (error.code === 'EAUTH') {
            errorMessage = 'Authentication failed. Please check your username and password.'
        } else if (error.code === 'ECONNECTION') {
            errorMessage = 'Connection failed. Please check your SMTP host and port.'
        } else if (error.code === 'ESOCKET') {
            errorMessage = 'Socket error. Please check your network connection.'
        } else if (error.message) {
            errorMessage = error.message
        }

        return NextResponse.json(
            { success: false, error: errorMessage },
            { status: 500 }
        )
    }
}
