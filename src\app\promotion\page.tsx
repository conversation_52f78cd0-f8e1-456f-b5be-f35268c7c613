import { Metadata } from 'next'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export const metadata: Metadata = {
    title: 'Promotions - MyBrokerForex',
    description: 'Discover exclusive trading promotions, bonuses, and special offers at MyBrokerForex. Take advantage of our limited-time deals to enhance your trading experience.',
}

export default function PromotionPage() {
    return (
        <div className="mt-0">
            <PublicPageLayout>
                <div className="max-w-7xl mx-auto px-6 py-16">
                    {/* Hero Section */}
                    <div className="text-center mb-16">
                        <h1 className="text-4xl md:text-5xl font-bold mt-8 text-gray-900 dark:text-white mb-6">
                            Exclusive Trading Promotions
                        </h1>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            Take advantage of our limited-time offers and special bonuses designed to enhance your trading experience and maximize your potential returns.
                        </p>
                    </div>

                    {/* Current Promotions */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                        {/* Welcome Bonus */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 border border-gray-200 dark:border-gray-700">
                            <div className="text-center mb-6">
                                <div className="w-16 h-16 bg-[#EA5455] rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Welcome Bonus</h3>
                                <div className="text-3xl font-bold text-[#EA5455] mb-4">100%</div>
                            </div>
                            <ul className="space-y-3 mb-6">
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Up to $1,000 bonus
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    For new accounts
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Minimum deposit $100
                                </li>
                            </ul>
                            <a
                                href="https://mbf.mybrokerforex.com/user/register"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl text-center block"
                            >
                                Claim Bonus
                            </a>
                        </div>

                        {/* Deposit Bonus */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 border border-gray-200 dark:border-gray-700">
                            <div className="text-center mb-6">
                                <div className="w-16 h-16 bg-[#28C76F] rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Reload Bonus</h3>
                                <div className="text-3xl font-bold text-[#28C76F] mb-4">50%</div>
                            </div>
                            <ul className="space-y-3 mb-6">
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Up to $500 bonus
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    For existing clients
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Monthly offer
                                </li>
                            </ul>
                            <a
                                href="https://mbf.mybrokerforex.com/user/login"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full bg-[#28C76F] hover:bg-[#28C76F]/90 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl text-center block"
                            >
                                Get Bonus
                            </a>
                        </div>

                        {/* Referral Program */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 border border-gray-200 dark:border-gray-700">
                            <div className="text-center mb-6">
                                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Refer & Earn</h3>
                                <div className="text-3xl font-bold text-blue-500 mb-4">$100</div>
                            </div>
                            <ul className="space-y-3 mb-6">
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Per successful referral
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Unlimited referrals
                                </li>
                                <li className="flex items-center text-gray-600 dark:text-gray-300">
                                    <svg className="w-5 h-5 text-[#28C76F] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Instant payouts
                                </li>
                            </ul>
                            <a
                                href="https://mbf.mybrokerforex.com/user/login"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl text-center block"
                            >
                                Start Referring
                            </a>
                        </div>
                    </div>

                    {/* Terms and Conditions */}
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-8">
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Terms & Conditions</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600 dark:text-gray-300">
                            <div>
                                <h3 className="font-semibold text-gray-900 dark:text-white mb-3">General Terms</h3>
                                <ul className="space-y-2">
                                    <li>• Promotions are subject to availability</li>
                                    <li>• One bonus per client account</li>
                                    <li>• Minimum trading volume requirements apply</li>
                                    <li>• Bonuses cannot be withdrawn directly</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Eligibility</h3>
                                <ul className="space-y-2">
                                    <li>• Available to verified accounts only</li>
                                    <li>• Restricted in certain jurisdictions</li>
                                    <li>• Subject to KYC compliance</li>
                                    <li>• MyBrokerForex reserves the right to modify terms</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </PublicPageLayout>
        </div>
    )
}
