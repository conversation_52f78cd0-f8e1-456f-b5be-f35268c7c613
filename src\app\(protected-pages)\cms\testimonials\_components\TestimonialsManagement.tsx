'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { PlusIcon, EditIcon, TrashIcon, StarIcon, UserIcon, QuoteIcon, ToggleLeftIcon, ToggleRightIcon } from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

interface Testimonial {
    id: number
    client_name: string
    quote: string
    image: string | null
    rating: number
    company: string | null
    position: string | null
    is_featured: boolean
    is_active: boolean
    created_at: string
}

const TestimonialsManagement = () => {
    const [testimonials, setTestimonials] = useState<Testimonial[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    // Fetch testimonials from API
    const fetchTestimonials = async () => {
        try {
            setLoading(true)
            const response = await fetch('/api/cms/testimonials')
            const data = await response.json()
            
            if (data.success) {
                setTestimonials(data.data)
            } else {
                setError(data.error || 'Failed to fetch testimonials')
            }
        } catch (err) {
            setError('Failed to fetch testimonials')
            console.error('Error fetching testimonials:', err)
        } finally {
            setLoading(false)
        }
    }

    // Toggle testimonial active status
    const toggleActive = async (id: number, currentStatus: boolean) => {
        try {
            const response = await fetch('/api/cms/testimonials', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id,
                    is_active: !currentStatus
                })
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Testimonial {!currentStatus ? 'activated' : 'deactivated'}
                    </Notification>
                )
                fetchTestimonials() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to update testimonial'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to update testimonial
                </Notification>
            )
            console.error('Error updating testimonial:', err)
        }
    }

    // Toggle testimonial featured status
    const toggleFeatured = async (id: number, currentStatus: boolean) => {
        try {
            const response = await fetch('/api/cms/testimonials', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id,
                    is_featured: !currentStatus
                })
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Testimonial {!currentStatus ? 'featured' : 'unfeatured'}
                    </Notification>
                )
                fetchTestimonials() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to update testimonial'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to update testimonial
                </Notification>
            )
            console.error('Error updating testimonial:', err)
        }
    }

    // Delete testimonial
    const deleteTestimonial = async (id: number) => {
        if (!confirm('Are you sure you want to delete this testimonial?')) {
            return
        }

        try {
            const response = await fetch(`/api/cms/testimonials?id=${id}`, {
                method: 'DELETE'
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Testimonial deleted successfully
                    </Notification>
                )
                fetchTestimonials() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to delete testimonial'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to delete testimonial
                </Notification>
            )
            console.error('Error deleting testimonial:', err)
        }
    }

    // Render star rating
    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <StarIcon 
                key={i} 
                className={`h-4 w-4 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
            />
        ))
    }

    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    }

    useEffect(() => {
        fetchTestimonials()
    }, [])

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Testimonials</h1>
                        <p className="text-muted-foreground">
                            Manage customer testimonials and reviews
                        </p>
                    </div>
                </div>
                <Card className="p-6">
                    <div className="text-center">Loading testimonials...</div>
                </Card>
            </div>
        )
    }

    if (error) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Testimonials</h1>
                        <p className="text-muted-foreground">
                            Manage customer testimonials and reviews
                        </p>
                    </div>
                </div>
                <Card className="p-6">
                    <div className="text-center text-red-600">
                        Error: {error}
                        <br />
                        <Button onClick={fetchTestimonials} className="mt-4">
                            Retry
                        </Button>
                    </div>
                </Card>
            </div>
        )
    }

    const activeTestimonials = testimonials.filter(t => t.is_active).length
    const featuredTestimonials = testimonials.filter(t => t.is_featured).length
    const averageRating = testimonials.length > 0 
        ? (testimonials.reduce((sum, t) => sum + t.rating, 0) / testimonials.length).toFixed(1)
        : '0'

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Testimonials</h1>
                    <p className="text-muted-foreground">
                        Manage customer testimonials and reviews
                    </p>
                </div>
                <Button className="flex items-center gap-2">
                    <PlusIcon className="h-4 w-4" />
                    Add New Testimonial
                </Button>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card className="p-4">
                    <div className="text-center">
                        <p className="text-2xl font-bold">{testimonials.length}</p>
                        <p className="text-sm text-muted-foreground">Total Reviews</p>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{activeTestimonials}</p>
                        <p className="text-sm text-muted-foreground">Published</p>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">{featuredTestimonials}</p>
                        <p className="text-sm text-muted-foreground">Featured</p>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="text-center">
                        <div className="flex items-center justify-center gap-1">
                            <p className="text-2xl font-bold">{averageRating}</p>
                            <StarIcon className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        </div>
                        <p className="text-sm text-muted-foreground">Avg Rating</p>
                    </div>
                </Card>
            </div>

            <Card
                header={{ content: `Customer Testimonials (${testimonials.length})` }}
                className="p-6"
            >
                    {testimonials.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-muted-foreground">No testimonials found</p>
                            <Button className="mt-4">
                                <PlusIcon className="h-4 w-4 mr-2" />
                                Add Your First Testimonial
                            </Button>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {testimonials.map((testimonial) => (
                                <div key={testimonial.id} className="border rounded-lg p-4 space-y-4">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                                {testimonial.image ? (
                                                    <img 
                                                        src={testimonial.image} 
                                                        alt={testimonial.client_name}
                                                        className="w-12 h-12 rounded-full object-cover"
                                                    />
                                                ) : (
                                                    <UserIcon className="h-6 w-6 text-blue-600" />
                                                )}
                                            </div>
                                            <div>
                                                <h3 className="font-semibold">{testimonial.client_name}</h3>
                                                {testimonial.position && (
                                                    <p className="text-sm text-muted-foreground">{testimonial.position}</p>
                                                )}
                                                {testimonial.company && (
                                                    <p className="text-xs text-muted-foreground">{testimonial.company}</p>
                                                )}
                                                <div className="flex items-center gap-1 mt-1">
                                                    {renderStars(testimonial.rating)}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {testimonial.is_featured && (
                                                <Badge className="bg-blue-100 text-blue-800">Featured</Badge>
                                            )}
                                            <Badge className={testimonial.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                                                {testimonial.is_active ? 'Published' : 'Draft'}
                                            </Badge>
                                            <Button
                                                variant="plain"
                                                size="sm"
                                                title={testimonial.is_featured ? 'Unfeature' : 'Feature'}
                                                onClick={() => toggleFeatured(testimonial.id, testimonial.is_featured)}
                                            >
                                                ⭐
                                            </Button>
                                            <Button
                                                variant="plain"
                                                size="sm"
                                                title={testimonial.is_active ? 'Deactivate' : 'Activate'}
                                                onClick={() => toggleActive(testimonial.id, testimonial.is_active)}
                                            >
                                                {testimonial.is_active ? (
                                                    <ToggleRightIcon className="h-4 w-4" />
                                                ) : (
                                                    <ToggleLeftIcon className="h-4 w-4" />
                                                )}
                                            </Button>
                                            <Button variant="plain" size="sm" title="Edit">
                                                <EditIcon className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="plain"
                                                size="sm"
                                                title="Delete"
                                                onClick={() => deleteTestimonial(testimonial.id)}
                                            >
                                                <TrashIcon className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="relative">
                                        <QuoteIcon className="absolute -top-2 -left-2 h-6 w-6 text-muted-foreground/30" />
                                        <p className="text-sm italic pl-4">
                                            "{testimonial.quote}"
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                        <span>Posted: {formatDate(testimonial.created_at)}</span>
                                        <span>Rating: {testimonial.rating}/5</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
            </Card>
        </div>
    )
}

export default TestimonialsManagement
