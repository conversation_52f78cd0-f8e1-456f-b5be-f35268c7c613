'use client'

import { motion } from 'framer-motion'
import { TbDeviceMobile, TbNotification, TbFingerprint, TbCloudDownload, TbChartLine, TbBolt } from 'react-icons/tb'
import { HiArrowRight } from 'react-icons/hi2'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function MobileTradingPage() {
    const features = [
        {
            icon: TbDeviceMobile,
            title: 'Native Mobile Apps',
            description: 'Optimized iOS and Android apps designed specifically for mobile trading with intuitive touch controls.'
        },
        {
            icon: TbNotification,
            title: 'Push Notifications',
            description: 'Real-time alerts for price movements, trade executions, and market news to keep you informed.'
        },
        {
            icon: TbFingerprint,
            title: 'Biometric Security',
            description: 'Secure login with fingerprint and face recognition for quick and safe account access.'
        },
        {
            icon: TbCloudDownload,
            title: 'Offline Charts',
            description: 'Download charts and market data for offline analysis when internet connectivity is limited.'
        },
        {
            icon: TbChartLine,
            title: 'Mobile Charting',
            description: 'Full-featured charting tools optimized for mobile screens with gesture-based navigation.'
        },
        {
            icon: TbBolt,
            title: 'One-Tap Trading',
            description: 'Execute trades instantly with our streamlined mobile interface designed for speed.'
        }
    ]

    const appFeatures = [
        'Real-time quotes and charts',
        'Advanced order management',
        'Economic calendar integration',
        'Market news and analysis',
        'Account management tools',
        'Multi-language support'
    ]

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
            {/* Hero Section */}
            <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        <h1 className="text-5xl text-gray-300 font-bold mb-6 mt-8">
                            Mobile Trading Apps
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Trade forex on the go with our powerful mobile applications. Available for iOS and Android 
                            with full trading functionality and real-time market access.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center">
                                Download for iOS
                                <HiArrowRight className="ml-2 w-5 h-5" />
                            </button>
                            <button className="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-gray-900 transition-colors duration-200">
                                Download for Android
                            </button>
                        </div>
                    </motion.div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                        className="text-center mb-16"
                    >
                        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                            Mobile App Features
                        </h2>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            Experience the full power of forex trading in the palm of your hand with our feature-rich mobile applications.
                        </p>
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {features.map((feature, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 * index }}
                                className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                            >
                                <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mb-6">
                                    <feature.icon className="w-8 h-8 text-white" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    {feature.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    {feature.description}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* App Features Section */}
            <section className="py-20 bg-white dark:bg-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <motion.div
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8 }}
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Trade Anywhere, Anytime
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                Our mobile apps provide the same powerful trading capabilities as our desktop platform, 
                                optimized for mobile devices with intuitive touch controls.
                            </p>
                            <ul className="space-y-4">
                                {appFeatures.map((feature, index) => (
                                    <motion.li
                                        key={index}
                                        initial={{ opacity: 0, x: -20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ duration: 0.6, delay: 0.1 * index }}
                                        className="flex items-center text-gray-700 dark:text-gray-300"
                                    >
                                        <div className="w-6 h-6 bg-[#28C76F] rounded-full flex items-center justify-center mr-4">
                                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                        {feature}
                                    </motion.li>
                                ))}
                            </ul>
                        </motion.div>

                        <motion.div
                            initial={{ opacity: 0, x: 30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.8, delay: 0.2 }}
                            className="relative"
                        >
                            <div className="bg-gradient-to-br from-[#EA5455] to-[#d63384] p-8 rounded-2xl text-white">
                                <h3 className="text-2xl font-bold mb-6">Download Our Apps</h3>
                                <p className="text-lg mb-8">
                                    Get started with mobile trading today. Download our apps from the App Store or Google Play.
                                </p>
                                <div className="space-y-4">
                                    <button className="w-full py-4 bg-white text-[#EA5455] font-semibold rounded-xl hover:bg-gray-100 transition-colors duration-200">
                                        Download for iOS
                                    </button>
                                    <button className="w-full py-4 bg-white text-[#EA5455] font-semibold rounded-xl hover:bg-gray-100 transition-colors duration-200">
                                        Download for Android
                                    </button>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </div>
            </section>

            {/* Download Section */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center"
                    >
                        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                            Start Mobile Trading Today
                        </h2>
                        <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                            Join millions of traders who trust our mobile platform for their forex trading needs.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button
                                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                            >
                                Open Trading Account
                            </button>
                            <button className="px-8 py-4 border-2 border-[#EA5455] text-[#EA5455] font-semibold rounded-xl hover:bg-[#EA5455] hover:text-white transition-colors duration-200">
                                Try Demo Account
                            </button>
                        </div>
                    </motion.div>
                </div>
            </section>
            </div>
        </PublicPageLayout>
    )
}
