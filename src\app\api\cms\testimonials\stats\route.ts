import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket } from 'mysql2'

// GET /api/cms/testimonials/stats - Get testimonials statistics
export async function GET(request: NextRequest) {
    try {
        // Get total count
        const [totalResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as total FROM testimonials'
        )
        const total = totalResult[0].total

        // Get active count
        const [activeResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM testimonials WHERE is_active = 1'
        )
        const active = activeResult[0].count

        // Get inactive count
        const [inactiveResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM testimonials WHERE is_active = 0'
        )
        const inactive = inactiveResult[0].count

        // Get featured count
        const [featuredResult] = await pool.execute<RowDataPacket[]>(
            'SELECT COUNT(*) as count FROM testimonials WHERE is_featured = 1'
        )
        const featured = featuredResult[0].count

        // Get recent testimonials
        const [recentTestimonials] = await pool.execute<RowDataPacket[]>(
            'SELECT id, client_name as title, is_active as status, created_at, client_name as author FROM testimonials ORDER BY created_at DESC LIMIT 5'
        )

        return NextResponse.json({
            success: true,
            data: {
                total,
                published: active,
                draft: inactive,
                featured,
                recent: recentTestimonials
            }
        })
    } catch (error) {
        console.error('Error fetching testimonials stats:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch testimonials statistics' },
            { status: 500 }
        )
    }
} 