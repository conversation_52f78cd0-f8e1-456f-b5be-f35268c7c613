'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { TbArrowUp } from 'react-icons/tb'

interface BackToTopButtonProps {
    showAfter?: number
    className?: string
}

const BackToTopButton = ({ 
    showAfter = 300, 
    className = '' 
}: BackToTopButtonProps) => {
    const [isVisible, setIsVisible] = useState(false)
    const [isScrolling, setIsScrolling] = useState(false)

    useEffect(() => {
        let scrollTimeout: NodeJS.Timeout

        const toggleVisibility = () => {
            const scrolled = document.documentElement.scrollTop
            
            if (scrolled > showAfter) {
                setIsVisible(true)
            } else {
                setIsVisible(false)
            }

            // Show scrolling state
            setIsScrolling(true)
            clearTimeout(scrollTimeout)
            scrollTimeout = setTimeout(() => {
                setIsScrolling(false)
            }, 150)
        }

        // Check initial scroll position
        toggleVisibility()

        window.addEventListener('scroll', toggleVisibility, { passive: true })

        return () => {
            window.removeEventListener('scroll', toggleVisibility)
            clearTimeout(scrollTimeout)
        }
    }, [showAfter])

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        })
    }

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.button
                    initial={{ opacity: 0, scale: 0.8, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 20 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ 
                        type: "spring", 
                        stiffness: 260, 
                        damping: 20 
                    }}
                    onClick={scrollToTop}
                    className={`
                        fixed bottom-6 right-6 z-50
                        w-10 h-10 md:w-11 md:h-11
                        bg-[#EA5455]/80 hover:bg-[#EA5455]
                        text-white
                        rounded-full
                        shadow-md hover:shadow-lg
                        flex items-center justify-center
                        transition-all duration-200
                        border border-white/10
                        backdrop-blur-sm
                        group
                        ${isScrolling ? 'ring-1 ring-[#EA5455]/20' : ''}
                        ${className}
                    `}
                    aria-label="Back to top"
                    title="Back to top"
                >
                    <TbArrowUp
                        className={`
                            w-4 h-4 md:w-5 md:h-5
                            transition-transform duration-200
                            ${isScrolling ? 'scale-110' : ''}
                            group-hover:scale-110
                        `}
                    />
                    
                    {/* Pulse effect on scroll */}
                    {isScrolling && (
                        <motion.div
                            initial={{ scale: 1, opacity: 0.6 }}
                            animate={{ scale: 1.5, opacity: 0 }}
                            transition={{ duration: 0.6, ease: "easeOut" }}
                            className="absolute inset-0 bg-[#EA5455] rounded-full"
                        />
                    )}
                </motion.button>
            )}
        </AnimatePresence>
    )
}

export default BackToTopButton
