'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-scroll'
import NextLink from 'next/link'
import classNames from '@/utils/classNames'

type AnchorTab = {
    title: string
    value: string
    to: string
}

type LinkTab = {
    title: string
    value: string
    href: string
    megaMenu?: {
        sections: {
            title: string
            items: {
                title: string
                href: string
                icon?: string | React.ReactNode
                description?: string
            }[]
        }[]
    }
}

type Tab = LinkTab | AnchorTab

const NavList = ({
    tabs: propTabs,
    tabClassName,
    onTabClick,
}: {
    tabs: Tab[]
    tabClassName?: string
    onTabClick?: () => void
}) => {
    const [active, setActive] = useState<Tab>(propTabs[0])
    const [show, setShow] = useState(false)
    const [megaMenuOpen, setMegaMenuOpen] = useState<string | null>(null)
    const [isHydrated, setIsHydrated] = useState(false)

    useEffect(() => {
        setIsHydrated(true)
    }, [])

    const moveSelectedTabToTop = (idx: number) => {
        setShow(true)
        const newTabs = [...propTabs]
        const selectedTab = newTabs.splice(idx, 1)
        newTabs.unshift(selectedTab[0])
        setActive(newTabs[0])
        onTabClick?.()
    }

    // --- Refactored hover logic ---
    const handleTriggerMouseEnter = (idx: number, tab: Tab) => {
        moveSelectedTabToTop(idx)
        if ((tab as LinkTab).megaMenu) {
            setMegaMenuOpen(tab.value)
        }
    }
    const handleTriggerMouseLeave = (tab: Tab) => {
        if ((tab as LinkTab).megaMenu) {
            setMegaMenuOpen(null)
        }
    }
    const handleMegaMenuMouseEnter = (tabValue: string) => {
        setMegaMenuOpen(tabValue)
    }
    const handleMegaMenuMouseLeave = () => {
        setMegaMenuOpen(null)
    }
    const handleLinkClick = () => {
        setMegaMenuOpen(null)
    }

    return (
        <div className={isHydrated ? "relative w-full" : "relative"}>
            <div className={isHydrated ? "flex items-center justify-center gap-2" : "flex"}>
                {propTabs.map((tab, idx) => (
                    <div
                        key={tab.title}
                        className={isHydrated ? "relative group" : "relative"}
                        onMouseEnter={() => handleTriggerMouseEnter(idx, tab)}
                        onMouseLeave={() => handleTriggerMouseLeave(tab)}
                    >
                        <button
                            className={classNames(
                                'relative px-5 py-2 rounded-xl',
                                tabClassName,
                            )}
                            onClick={() => {
                                moveSelectedTabToTop(idx)
                            }}
                        >
                            {active.value === tab.value && (
                                <motion.div
                                    layoutId="clickedbutton"
                                    transition={{
                                        type: 'spring',
                                        bounce: 0.3,
                                        duration: 0.6,
                                    }}
                                    className={classNames(
                                        'absolute inset-0 rounded-xl',
                                        show && 'bg-gray-100 dark:bg-gray-700',
                                    )}
                                    suppressHydrationWarning
                                />
                            )}
                            {(tab as AnchorTab).to ? (
                                <Link
                                    smooth
                                    to={(tab as AnchorTab).to}
                                    className="relative block heading-text text-sm z-10"
                                    duration={500}
                                    suppressHydrationWarning
                                >
                                    {tab.title}
                                </Link>
                            ) : (
                                <NextLink
                                    href={(tab as LinkTab).href}
                                    className="relative block heading-text text-sm z-10"
                                    suppressHydrationWarning
                                >
                                    {tab.title}
                                </NextLink>
                            )}
                        </button>
                        {/* Mega Menu */}
                        {isHydrated && (tab as LinkTab).megaMenu && megaMenuOpen === tab.value && (
                            <>
                                {/* Invisible bridge to prevent menu from closing when moving mouse */}
                                <div
                                    className="absolute top-full left-1/2 transform -translate-x-1/2 w-[900px] max-w-[90vw] h-6 z-[9998]"
                                    onMouseEnter={() => handleMegaMenuMouseEnter(tab.value)}
                                    onMouseLeave={handleMegaMenuMouseLeave}
                                />
                                <motion.div
                                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                                    animate={{ opacity: 1, y: 0, scale: 1 }}
                                    exit={{ opacity: 0, y: 10, scale: 0.95 }}
                                    transition={{ duration: 0.3, ease: "easeOut" }}
                                    className="absolute top-full left-1/2 transform -translate-x-1/2 mt-4 w-[900px] max-w-[90vw] bg-white dark:bg-[#1E1E1E] backdrop-blur-md rounded-xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-4 z-[9999]"
                                    suppressHydrationWarning
                                    onMouseEnter={() => handleMegaMenuMouseEnter(tab.value)}
                                    onMouseLeave={handleMegaMenuMouseLeave}
                                >
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                        {(tab as LinkTab).megaMenu!.sections.map((section, sectionIdx) => (
                                            <div key={sectionIdx} className="space-y-2">
                                                <h3 className="font-bold text-xs uppercase tracking-wide mb-2 text-black dark:text-white">
                                                    {section.title}
                                                </h3>
                                                <ul className="space-y-1">
                                                    {section.items.map((item, itemIdx) => (
                                                        <li key={itemIdx}>
                                                            <NextLink
                                                                href={item.href}
                                                                className="flex items-start text-black dark:text-white hover:text-[#EA5455] dark:hover:text-[#EA5455] transition-colors duration-200 text-sm py-2 px-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 font-normal group"
                                                                onClick={handleLinkClick}
                                                            >
                                                                {item.icon ? (
                                                                    <span className="text-lg mr-3 mt-0.5 group-hover:scale-110 transition-transform duration-200">
                                                                        {item.icon}
                                                                    </span>
                                                                ) : (
                                                                    <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-3 mt-2"></span>
                                                                )}
                                                                <div className="flex-1">
                                                                    <div className="font-bold text-xs text-black dark:text-white group-hover:text-[#EA5455] transition-colors duration-200">
                                                                        {item.title}
                                                                    </div>
                                                                    {item.description && (
                                                                        <div className="text-xs text-black/70 dark:text-white/70 mt-1 leading-relaxed">
                                                                            {item.description}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </NextLink>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        ))}
                                    </div>
                                    {/* Mega Menu Footer */}
                                    <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                                        <div className="flex items-center justify-between">
                                            <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                                                {tab.title.toUpperCase()}
                                            </p>
                                            <NextLink
                                                href={(tab as LinkTab).href}
                                                className="inline-flex items-center px-4 py-2 bg-[#EA5455] text-white text-xs font-medium rounded-lg hover:bg-[#d63384] transition-colors duration-200"
                                                onClick={handleLinkClick}
                                            >
                                                View All
                                                <svg className="ml-2 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                </svg>
                                            </NextLink>
                                        </div>
                                    </div>
                                </motion.div>
                            </>
                        )}
                    </div>
                ))}
            </div>
        </div>
    )
}

export default NavList
