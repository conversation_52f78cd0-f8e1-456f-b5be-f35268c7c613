'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { 
    FileTextIcon, 
    BookOpenIcon, 
    MenuIcon, 
    MessageSquareIcon,
    PlusIcon,
    EditIcon,
    TrashIcon,
    EyeIcon,
    RefreshCwIcon,
    TrendingUpIcon,
    UsersIcon,
    CalendarIcon
} from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

interface ContentItem {
    id: number
    title: string
    status: 'draft' | 'published' | 'archived'
    created_at: string
    updated_at: string
    author?: string
    views?: number
}

interface PagesData {
    total: number
    published: number
    draft: number
    archived: number
    recent: ContentItem[]
}

interface BlogData {
    total: number
    published: number
    draft: number
    archived: number
    recent: ContentItem[]
}

interface MenusData {
    total: number
    active: number
    inactive: number
    recent: ContentItem[]
}

interface TestimonialsData {
    total: number
    published: number
    draft: number
    featured: number
    recent: ContentItem[]
}

interface CMSStats {
    pages: PagesData
    blog: BlogData
    menus: MenusData
    testimonials: TestimonialsData
    totalViews: number
    totalContent: number
}

const CMSDashboard = () => {
    const [stats, setStats] = useState<CMSStats | null>(null)
    const [loading, setLoading] = useState(true)
    const [refreshing, setRefreshing] = useState(false)
    const [activeTab, setActiveTab] = useState<'overview' | 'pages' | 'blog' | 'menus' | 'testimonials'>('overview')

    // Fetch CMS data
    const fetchCMSData = async () => {
        try {
            setLoading(true)
            
            // Real API calls to database
            const [pagesRes, blogRes, menusRes, testimonialsRes] = await Promise.all([
                fetch('/api/cms/pages/stats'),
                fetch('/api/cms/blog/stats'),
                fetch('/api/cms/menus/stats'),
                fetch('/api/cms/testimonials/stats')
            ])

            // Check if all responses are successful
            if (!pagesRes.ok || !blogRes.ok || !menusRes.ok || !testimonialsRes.ok) {
                throw new Error('Failed to fetch CMS data')
            }

            const [pagesData, blogData, menusData, testimonialsData] = await Promise.all([
                pagesRes.json(),
                blogRes.json(),
                menusRes.json(),
                testimonialsRes.json()
            ])

            // Calculate total views and content
            const totalViews = 0 // This would come from analytics API
            const totalContent = pagesData.data.total + blogData.data.total + menusData.data.total + testimonialsData.data.total

            const realStats: CMSStats = {
                pages: {
                    total: pagesData.data.total,
                    published: pagesData.data.published,
                    draft: pagesData.data.draft,
                    archived: pagesData.data.archived,
                    recent: pagesData.data.recent.map((page: any) => ({
                        id: page.id,
                        title: page.title,
                        status: page.status,
                        created_at: page.created_at,
                        updated_at: page.updated_at,
                        views: 0 // This would come from analytics
                    }))
                },
                blog: {
                    total: blogData.data.total,
                    published: blogData.data.published,
                    draft: blogData.data.draft,
                    archived: blogData.data.archived,
                    recent: blogData.data.recent.map((post: any) => ({
                        id: post.id,
                        title: post.title,
                        status: post.status,
                        created_at: post.created_at,
                        updated_at: post.updated_at,
                        author: post.author,
                        views: 0 // This would come from analytics
                    }))
                },
                menus: {
                    total: menusData.data.total,
                    active: menusData.data.active,
                    inactive: menusData.data.inactive,
                    recent: menusData.data.recent.map((menu: any) => ({
                        id: menu.id,
                        title: menu.title,
                        status: menu.status,
                        created_at: menu.created_at,
                        updated_at: menu.updated_at
                    }))
                },
                testimonials: {
                    total: testimonialsData.data.total,
                    published: testimonialsData.data.published,
                    draft: testimonialsData.data.draft,
                    featured: testimonialsData.data.featured,
                    recent: testimonialsData.data.recent.map((testimonial: any) => ({
                        id: testimonial.id,
                        title: testimonial.title,
                        status: testimonial.status,
                        created_at: testimonial.created_at,
                        updated_at: testimonial.updated_at,
                        author: testimonial.author
                    }))
                },
                totalViews,
                totalContent
            }

            setStats(realStats)
        } catch (error) {
            console.error('Error fetching CMS data:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    Failed to load CMS data. Please try again.
                </Notification>
            )
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchCMSData()
    }, [])

    const handleRefresh = async () => {
        setRefreshing(true)
        await fetchCMSData()
        setRefreshing(false)
        toast.push(
            <Notification title="Success" type="success">
                CMS data refreshed successfully.
            </Notification>
        )
    }

    const handleCreateNew = (type: string) => {
        // Navigate to create page for the specific content type
        window.location.href = `/cms/${type}/create`
    }

    const handleEdit = (type: string, id: number) => {
        // Navigate to edit page
        window.location.href = `/cms/${type}/${id}/edit`
    }

    const handleDelete = async (type: string, id: number) => {
        if (confirm('Are you sure you want to delete this item?')) {
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 500))
                
                // Update local state
                setStats(prev => {
                    if (!prev) return prev
                    
                    const updatedStats = { ...prev }
                    const typeKey = type as keyof CMSStats
                    const data = updatedStats[typeKey] as any
                    
                    if (data.recent) {
                        data.recent = data.recent.filter((item: any) => item.id !== id)
                        data.total = Math.max(0, data.total - 1)
                    }
                    
                    return updatedStats
                })

                toast.push(
                    <Notification title="Success" type="success">
                        {type} deleted successfully.
                    </Notification>
                )
            } catch (error) {
                toast.push(
                    <Notification title="Error" type="danger">
                        Failed to delete {type}.
                    </Notification>
                )
            }
        }
    }

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            published: { className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200', text: 'Published' },
            draft: { className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200', text: 'Draft' },
            archived: { className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200', text: 'Archived' },
            active: { className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200', text: 'Active' },
            inactive: { className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200', text: 'Inactive' }
        }
        
        const config = statusConfig[status as keyof typeof statusConfig] || { className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200', text: status }
        return <Badge className={config.className}>{config.text}</Badge>
    }

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#EA5455]"></div>
            </div>
        )
    }

    if (!stats) {
        return (
            <div className="text-center py-8">
                <p className="text-gray-500">No CMS data available.</p>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Content Management System</h1>
                    <p className="text-gray-600 dark:text-gray-300">Manage all your website content in one place</p>
                </div>
                <Button
                    icon={<RefreshCwIcon className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />}
                    onClick={handleRefresh}
                    disabled={refreshing}
                >
                    Refresh
                </Button>
            </div>

            {/* Navigation Tabs */}
            <div className="border-b border-gray-200 dark:border-gray-700">
                <nav className="-mb-px flex space-x-8">
                    {[
                        { key: 'overview', label: 'Overview', icon: TrendingUpIcon },
                        { key: 'pages', label: 'Pages', icon: FileTextIcon },
                        { key: 'blog', label: 'Blog', icon: BookOpenIcon },
                        { key: 'menus', label: 'Menus', icon: MenuIcon },
                        { key: 'testimonials', label: 'Testimonials', icon: MessageSquareIcon }
                    ].map((tab) => (
                        <button
                            key={tab.key}
                            onClick={() => setActiveTab(tab.key as any)}
                            className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === tab.key
                                    ? 'border-[#EA5455] text-[#EA5455]'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                        >
                            <tab.icon className="h-4 w-4" />
                            <span>{tab.label}</span>
                        </button>
                    ))}
                </nav>
            </div>

            {/* Overview Tab */}
            {activeTab === 'overview' && (
                <div className="space-y-6">
                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Content</p>
                                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalContent}</p>
                                </div>
                                <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                                    <FileTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                </div>
                            </div>
                        </Card>

                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Views</p>
                                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalViews.toLocaleString()}</p>
                                </div>
                                <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                                    <EyeIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                                </div>
                            </div>
                        </Card>

                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Published Pages</p>
                                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.pages.published}</p>
                                </div>
                                <div className="p-3 bg-[#EA5455]/10 rounded-full">
                                    <FileTextIcon className="h-6 w-6 text-[#EA5455]" />
                                </div>
                            </div>
                        </Card>

                        <Card className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Blog Posts</p>
                                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.blog.published}</p>
                                </div>
                                <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                                    <BookOpenIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                                </div>
                            </div>
                        </Card>
                    </div>

                    {/* Recent Content */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <Card className="p-6">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold">Recent Pages</h3>
                                <Button size="sm" onClick={() => handleCreateNew('pages')}>
                                    <PlusIcon className="h-4 w-4 mr-2" />
                                    New Page
                                </Button>
                            </div>
                            <div className="space-y-3">
                                {stats.pages.recent.map((page) => (
                                    <div key={page.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                        <div className="flex-1">
                                            <p className="font-medium text-gray-900 dark:text-white">{page.title}</p>
                                            <p className="text-sm text-gray-500">{page.views} views</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {getStatusBadge(page.status)}
                                            <Button size="sm" variant="plain" onClick={() => handleEdit('pages', page.id)}>
                                                <EditIcon className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </Card>

                        <Card className="p-6">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold">Recent Blog Posts</h3>
                                <Button size="sm" onClick={() => handleCreateNew('blog')}>
                                    <PlusIcon className="h-4 w-4 mr-2" />
                                    New Post
                                </Button>
                            </div>
                            <div className="space-y-3">
                                {stats.blog.recent.map((post) => (
                                    <div key={post.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                        <div className="flex-1">
                                            <p className="font-medium text-gray-900 dark:text-white">{post.title}</p>
                                            <p className="text-sm text-gray-500">by {post.author} • {post.views} views</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {getStatusBadge(post.status)}
                                            <Button size="sm" variant="plain" onClick={() => handleEdit('blog', post.id)}>
                                                <EditIcon className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </Card>
                    </div>
                </div>
            )}

            {/* Pages Tab */}
            {activeTab === 'pages' && (
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold">Pages Management</h2>
                        <Button onClick={() => handleCreateNew('pages')}>
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Create New Page
                        </Button>
                    </div>
                    
                    <Card className="p-6">
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                        <th className="text-left py-3 px-4 font-medium">Title</th>
                                        <th className="text-left py-3 px-4 font-medium">Status</th>
                                        <th className="text-left py-3 px-4 font-medium">Views</th>
                                        <th className="text-left py-3 px-4 font-medium">Updated</th>
                                        <th className="text-left py-3 px-4 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {stats.pages.recent.map((page) => (
                                        <tr key={page.id} className="border-b border-gray-100 dark:border-gray-800">
                                            <td className="py-3 px-4 font-medium">{page.title}</td>
                                            <td className="py-3 px-4">{getStatusBadge(page.status)}</td>
                                            <td className="py-3 px-4">{page.views?.toLocaleString() || 0}</td>
                                            <td className="py-3 px-4 text-sm text-gray-500">{new Date(page.updated_at).toLocaleDateString()}</td>
                                            <td className="py-3 px-4">
                                                <div className="flex items-center space-x-2">
                                                    <Button size="sm" variant="plain" onClick={() => handleEdit('pages', page.id)}>
                                                        <EditIcon className="h-4 w-4" />
                                                    </Button>
                                                    <Button size="sm" variant="plain" onClick={() => handleDelete('pages', page.id)}>
                                                        <TrashIcon className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </Card>
                </div>
            )}

            {/* Blog Tab */}
            {activeTab === 'blog' && (
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold">Blog Management</h2>
                        <Button onClick={() => handleCreateNew('blog')}>
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Create New Post
                        </Button>
                    </div>
                    
                    <Card className="p-6">
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                        <th className="text-left py-3 px-4 font-medium">Title</th>
                                        <th className="text-left py-3 px-4 font-medium">Author</th>
                                        <th className="text-left py-3 px-4 font-medium">Status</th>
                                        <th className="text-left py-3 px-4 font-medium">Views</th>
                                        <th className="text-left py-3 px-4 font-medium">Updated</th>
                                        <th className="text-left py-3 px-4 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {stats.blog.recent.map((post) => (
                                        <tr key={post.id} className="border-b border-gray-100 dark:border-gray-800">
                                            <td className="py-3 px-4 font-medium">{post.title}</td>
                                            <td className="py-3 px-4">{post.author}</td>
                                            <td className="py-3 px-4">{getStatusBadge(post.status)}</td>
                                            <td className="py-3 px-4">{post.views?.toLocaleString() || 0}</td>
                                            <td className="py-3 px-4 text-sm text-gray-500">{new Date(post.updated_at).toLocaleDateString()}</td>
                                            <td className="py-3 px-4">
                                                <div className="flex items-center space-x-2">
                                                    <Button size="sm" variant="plain" onClick={() => handleEdit('blog', post.id)}>
                                                        <EditIcon className="h-4 w-4" />
                                                    </Button>
                                                    <Button size="sm" variant="plain" onClick={() => handleDelete('blog', post.id)}>
                                                        <TrashIcon className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </Card>
                </div>
            )}

            {/* Menus Tab */}
            {activeTab === 'menus' && (
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold">Menus Management</h2>
                        <Button onClick={() => handleCreateNew('menus')}>
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Create New Menu
                        </Button>
                    </div>
                    
                    <Card className="p-6">
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                        <th className="text-left py-3 px-4 font-medium">Menu Name</th>
                                        <th className="text-left py-3 px-4 font-medium">Status</th>
                                        <th className="text-left py-3 px-4 font-medium">Updated</th>
                                        <th className="text-left py-3 px-4 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {stats.menus.recent.map((menu) => (
                                        <tr key={menu.id} className="border-b border-gray-100 dark:border-gray-800">
                                            <td className="py-3 px-4 font-medium">{menu.title}</td>
                                            <td className="py-3 px-4">{getStatusBadge(menu.status)}</td>
                                            <td className="py-3 px-4 text-sm text-gray-500">{new Date(menu.updated_at).toLocaleDateString()}</td>
                                            <td className="py-3 px-4">
                                                <div className="flex items-center space-x-2">
                                                    <Button size="sm" variant="plain" onClick={() => handleEdit('menus', menu.id)}>
                                                        <EditIcon className="h-4 w-4" />
                                                    </Button>
                                                    <Button size="sm" variant="plain" onClick={() => handleDelete('menus', menu.id)}>
                                                        <TrashIcon className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </Card>
                </div>
            )}

            {/* Testimonials Tab */}
            {activeTab === 'testimonials' && (
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold">Testimonials Management</h2>
                        <Button onClick={() => handleCreateNew('testimonials')}>
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Add Testimonial
                        </Button>
                    </div>
                    
                    <Card className="p-6">
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                        <th className="text-left py-3 px-4 font-medium">Title</th>
                                        <th className="text-left py-3 px-4 font-medium">Author</th>
                                        <th className="text-left py-3 px-4 font-medium">Status</th>
                                        <th className="text-left py-3 px-4 font-medium">Updated</th>
                                        <th className="text-left py-3 px-4 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {stats.testimonials.recent.map((testimonial) => (
                                        <tr key={testimonial.id} className="border-b border-gray-100 dark:border-gray-800">
                                            <td className="py-3 px-4 font-medium">{testimonial.title}</td>
                                            <td className="py-3 px-4">{testimonial.author}</td>
                                            <td className="py-3 px-4">{getStatusBadge(testimonial.status)}</td>
                                            <td className="py-3 px-4 text-sm text-gray-500">{new Date(testimonial.updated_at).toLocaleDateString()}</td>
                                            <td className="py-3 px-4">
                                                <div className="flex items-center space-x-2">
                                                    <Button size="sm" variant="plain" onClick={() => handleEdit('testimonials', testimonial.id)}>
                                                        <EditIcon className="h-4 w-4" />
                                                    </Button>
                                                    <Button size="sm" variant="plain" onClick={() => handleDelete('testimonials', testimonial.id)}>
                                                        <TrashIcon className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </Card>
                </div>
            )}
        </div>
    )
}

export default CMSDashboard 