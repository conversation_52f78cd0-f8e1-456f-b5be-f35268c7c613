const mysql = require('mysql2/promise');

async function setupSettingsTable() {
    let connection;
    
    try {
        // Create connection
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'mybrokerforex'
        });

        console.log('Connected to MySQL database');

        // Create settings table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) UNIQUE NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        console.log('Settings table created successfully');

        // Insert default settings
        const defaultSettings = [
            ['site_name', 'MyBrokerForex'],
            ['site_url', 'https://mybrokerforex.com'],
            ['site_description', 'Your trusted partner in forex trading with competitive spreads and professional support.'],
            ['admin_email', '<EMAIL>'],
            ['support_email', '<EMAIL>'],
            ['site_logo', '/images/logo/logo-dark-full.png'],
            ['user_registration', '1'],
            ['email_verification', '1'],
            ['maintenance_mode', '0'],
            ['analytics_tracking', '1']
        ];

        for (const [key, value] of defaultSettings) {
            await connection.execute(
                "INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)",
                [key, value]
            );
        }

        console.log('Default settings inserted successfully');
        console.log('Settings table setup completed!');

    } catch (error) {
        console.error('Error setting up settings table:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

setupSettingsTable();
