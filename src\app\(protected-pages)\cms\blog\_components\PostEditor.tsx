'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Textarea } from '@/components/ui/Textarea'
import Select from '@/components/ui/Select'
import Checkbox from '@/components/ui/Checkbox'
import { SaveIcon, ArrowLeftIcon } from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

interface Post {
    id?: number
    title: string
    slug: string
    content: string
    excerpt: string
    category: string
    image: string
    status: 'draft' | 'published' | 'archived'
    featured: boolean
}

interface PostEditorProps {
    post?: Post
    onSave: () => void
    onCancel: () => void
}

const PostEditor = ({ post, onSave, onCancel }: PostEditorProps) => {
    const [formData, setFormData] = useState<Post>({
        title: '',
        slug: '',
        content: '',
        excerpt: '',
        category: '',
        image: '',
        status: 'draft',
        featured: false
    })
    const [isSaving, setIsSaving] = useState(false)

    useEffect(() => {
        if (post) {
            setFormData(post)
        }
    }, [post])

    const generateSlug = (title: string) => {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim()
    }

    const handleTitleChange = (value: string) => {
        setFormData(prev => ({
            ...prev,
            title: value,
            slug: !post ? generateSlug(value) : prev.slug // Only auto-generate for new posts
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        
        if (!formData.title.trim() || !formData.slug.trim()) {
            toast.push(
                <Notification type="danger" title="Error">
                    Title and slug are required
                </Notification>
            )
            return
        }

        setIsSaving(true)

        try {
            const url = '/api/cms/posts'
            const method = post ? 'PUT' : 'POST'
            const body = post ? { ...formData, id: post.id } : formData

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            })

            const result = await response.json()

            if (result.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Post {post ? 'updated' : 'created'} successfully
                    </Notification>
                )
                onSave()
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {result.error || `Failed to ${post ? 'update' : 'create'} post`}
                    </Notification>
                )
            }
        } catch (error) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to {post ? 'update' : 'create'} post
                </Notification>
            )
            console.error('Error saving post:', error)
        } finally {
            setIsSaving(false)
        }
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="plain" onClick={onCancel} icon={<ArrowLeftIcon />}>
                        <span>Back to Posts</span>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">
                            {post ? 'Edit Post' : 'Create New Post'}
                        </h1>
                        <p className="text-muted-foreground">
                            {post ? 'Update blog post content and settings' : 'Create a new blog post'}
                        </p>
                    </div>
                </div>
                <Button
                    onClick={handleSubmit}
                    disabled={isSaving}
                    icon={<SaveIcon />}
                >
                    <span>{isSaving ? 'Saving...' : 'Save Post'}</span>
                </Button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        <Card header={{ content: 'Post Content' }} className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="title">Post Title *</Label>
                                    <Input
                                        id="title"
                                        value={formData.title}
                                        onChange={(e) => handleTitleChange(e.target.value)}
                                        placeholder="Enter post title"
                                        required
                                    />
                                </div>
                                
                                <div>
                                    <Label htmlFor="slug">URL Slug *</Label>
                                    <Input
                                        id="slug"
                                        value={formData.slug}
                                        onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                                        placeholder="post-url-slug"
                                        required
                                    />
                                    <p className="text-sm text-muted-foreground mt-1">
                                        URL: /blog/{formData.slug}
                                    </p>
                                </div>
                                
                                <div>
                                    <Label htmlFor="excerpt">Excerpt</Label>
                                    <Textarea
                                        id="excerpt"
                                        value={formData.excerpt}
                                        onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                                        placeholder="Brief description of the post"
                                        rows={3}
                                    />
                                </div>
                                
                                <div>
                                    <Label htmlFor="content">Post Content</Label>
                                    <Textarea
                                        id="content"
                                        value={formData.content}
                                        onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                                        placeholder="Enter post content (HTML supported)"
                                        rows={15}
                                    />
                                </div>
                            </div>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card header={{ content: 'Post Settings' }} className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="status">Status</Label>
                                    <Select
                                        value={{ value: formData.status, label: formData.status.charAt(0).toUpperCase() + formData.status.slice(1) }}
                                        options={[
                                            { value: 'draft', label: 'Draft' },
                                            { value: 'published', label: 'Published' },
                                            { value: 'archived', label: 'Archived' }
                                        ]}
                                        onChange={(option: any) => setFormData(prev => ({
                                            ...prev,
                                            status: option?.value as 'draft' | 'published' | 'archived'
                                        }))}
                                    />
                                </div>
                                
                                <div>
                                    <Label htmlFor="category">Category</Label>
                                    <Input
                                        id="category"
                                        value={formData.category}
                                        onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                                        placeholder="e.g., Education, Analysis, News"
                                    />
                                </div>
                                
                                <div>
                                    <Label htmlFor="image">Featured Image URL</Label>
                                    <Input
                                        id="image"
                                        value={formData.image}
                                        onChange={(e) => setFormData(prev => ({ ...prev, image: e.target.value }))}
                                        placeholder="https://example.com/image.jpg"
                                    />
                                </div>
                                
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="featured"
                                        checked={formData.featured}
                                        onChange={(checked) => setFormData(prev => ({
                                            ...prev,
                                            featured: checked as boolean
                                        }))}
                                    />
                                    <Label htmlFor="featured">Featured Post</Label>
                                </div>
                            </div>
                        </Card>
                    </div>
                </div>
            </form>
        </div>
    )
}

export default PostEditor
