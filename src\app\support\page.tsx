'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import { TbHeadphones, TbMail, TbPhone, TbClock, TbChevronDown, TbChevronUp, TbMessageCircle, TbHelp } from 'react-icons/tb'
import PageLayout from '@/components/layout/PageLayout'

const SupportPage = () => {
  const [openFaq, setOpenFaq] = useState<number | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })

  const supportChannels = [
    {
      icon: TbMessageCircle,
      title: "Live Chat",
      description: "Get instant help from our support team",
      availability: "24/7",
      action: "Start Chat",
      color: "text-[#EA5455]",
      bgColor: "bg-[#EA5455]/10"
    },
    {
      icon: TbMail,
      title: "Email Support",
      description: "Send us a detailed message",
      availability: "Response within 2 hours",
      action: "Send Email",
      color: "text-blue-500",
      bgColor: "bg-blue-500/10"
    },
    {
      icon: TbPhone,
      title: "Phone Support",
      description: "Speak directly with our experts",
      availability: "Mon-Fri 9AM-6PM GMT",
      action: "Call Now",
      color: "text-green-500",
      bgColor: "bg-green-500/10"
    }
  ]

  const faqs = [
    {
      question: "How do I open a trading account?",
      answer: "Opening an account is simple. Click 'Sign Up' on our homepage, choose your account type, complete the registration form, verify your identity, and make your first deposit. The entire process typically takes 15-30 minutes."
    },
    {
      question: "What is the minimum deposit required?",
      answer: "The minimum deposit varies by account type: Standard account requires $100, Professional account requires $1,000, and VIP account requires $10,000. All deposits are processed securely and funds are available for trading immediately."
    },
    {
      question: "How can I withdraw my funds?",
      answer: "You can withdraw funds through the same method used for deposit. Withdrawals are typically processed within 24 hours for verified accounts. Bank transfers may take 3-5 business days, while e-wallet withdrawals are usually instant."
    },
    {
      question: "What trading platforms do you offer?",
      answer: "We offer our proprietary web-based platform accessible from any browser, mobile apps for iOS and Android, and desktop applications for Windows and Mac. All platforms feature advanced charting, real-time quotes, and professional trading tools."
    },
    {
      question: "Is my money safe with MyBrokerForex?",
      answer: "Yes, your funds are completely secure. We are regulated by the FCA, maintain segregated client accounts with tier-1 banks, and use bank-grade SSL encryption. Client funds are never used for operational purposes and are fully protected."
    },
    {
      question: "What are your trading hours?",
      answer: "Forex markets are open 24 hours a day, 5 days a week, from Sunday 5 PM EST to Friday 5 PM EST. Our platform provides continuous access to global currency markets during these hours."
    },
    {
      question: "Do you offer educational resources?",
      answer: "Yes, we provide comprehensive educational materials including trading courses, live webinars, platform tutorials, market analysis, and a complete trading glossary. All resources are free for our clients."
    },
    {
      question: "What is leverage and how does it work?",
      answer: "Leverage allows you to control larger positions with smaller capital. For example, with 1:100 leverage, you can control $10,000 with just $100. While leverage can amplify profits, it also increases risk, so proper risk management is essential."
    }
  ]

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
    // Reset form
    setFormData({ name: '', email: '', subject: '', message: '' })
  }

  return (
      <PageLayout>
        {/* Hero Section */}
        <section className="relative py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Support <span className="text-[#EA5455]">Center</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Get the help you need, when you need it. Our expert support team is here 24/7
                to assist you with any questions or issues.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Support Channels */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">How Can We Help?</h2>
              <p className="text-xl text-muted-foreground">
                Choose your preferred way to get in touch with our support team.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {supportChannels.map((channel, index) => {
                const IconComponent = channel.icon
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white dark:bg-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 text-center"
                  >
                    <div className={`w-16 h-16 ${channel.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                      <IconComponent className={`h-8 w-8 ${channel.color}`} />
                    </div>
                    <h3 className="text-xl font-bold mb-3">{channel.title}</h3>
                    <p className="text-muted-foreground mb-4">{channel.description}</p>
                    <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-6">
                      <TbClock className="h-4 w-4" />
                      {channel.availability}
                    </div>
                    <button className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 border-2 ${channel.color.replace('text-', 'border-')} ${channel.color} hover:${channel.color.replace('text-', 'bg-')} hover:text-white`}>
                      {channel.action}
                    </button>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Frequently Asked Questions</h2>
              <p className="text-xl text-muted-foreground">
                Find quick answers to the most common questions about trading with MyBrokerForex.
              </p>
            </motion.div>

            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden"
                >
                  <button
                    onClick={() => toggleFaq(index)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <span className="font-semibold">{faq.question}</span>
                    {openFaq === index ? (
                      <TbChevronUp className="h-5 w-5 text-[#EA5455]" />
                    ) : (
                      <TbChevronDown className="h-5 w-5 text-muted-foreground" />
                    )}
                  </button>
                  {openFaq === index && (
                    <div className="px-6 pb-4">
                      <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Form */}
        <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Still Need Help?</h2>
              <p className="text-xl text-muted-foreground">
                Can't find what you're looking for? Send us a message and we'll get back to you within 2 hours.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white dark:bg-gray-900 rounded-2xl p-8 border border-gray-200 dark:border-gray-700"
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-semibold mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-800"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-800"
                      placeholder="Enter your email address"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-semibold mb-2">
                    Subject *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-800"
                    placeholder="What can we help you with?"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-semibold mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#EA5455] focus:border-transparent bg-white dark:bg-gray-800 resize-none"
                    placeholder="Please describe your question or issue in detail..."
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-300"
                >
                  Send Message
                </button>
              </form>
            </motion.div>
          </div>
        </section>

        {/* Additional Resources */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Additional Resources</h2>
              <p className="text-xl text-muted-foreground">
                Explore more ways to get the information and help you need.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TbHelp className="h-8 w-8 text-[#EA5455]" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Help Center</h3>
                <p className="text-sm text-muted-foreground mb-4">Browse our comprehensive help articles and guides</p>
                <button className="text-[#EA5455] hover:text-[#EA5455]/80 font-semibold text-sm">
                  Visit Help Center →
                </button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TbMessageCircle className="h-8 w-8 text-[#EA5455]" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Community Forum</h3>
                <p className="text-sm text-muted-foreground mb-4">Connect with other traders and share experiences</p>
                <button className="text-[#EA5455] hover:text-[#EA5455]/80 font-semibold text-sm">
                  Join Community →
                </button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TbHeadphones className="h-8 w-8 text-[#EA5455]" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Video Tutorials</h3>
                <p className="text-sm text-muted-foreground mb-4">Watch step-by-step platform tutorials</p>
                <button className="text-[#EA5455] hover:text-[#EA5455]/80 font-semibold text-sm">
                  Watch Videos →
                </button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#EA5455]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TbPhone className="h-8 w-8 text-[#EA5455]" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Phone Support</h3>
                <p className="text-sm text-muted-foreground mb-4">Speak directly with our support specialists</p>
                <button className="text-[#EA5455] hover:text-[#EA5455]/80 font-semibold text-sm">
                  Call ******-FOREX →
                </button>
              </motion.div>
            </div>
          </div>
        </section>
      </PageLayout>
  )
}

export default SupportPage