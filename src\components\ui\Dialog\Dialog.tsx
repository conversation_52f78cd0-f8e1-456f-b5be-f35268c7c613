import { useEffect } from 'react'
import classNames from 'classnames'
import CloseButton from '../CloseButton'
import { motion } from 'framer-motion'
import useWindowSize from '../hooks/useWindowSize'
import type { MouseEvent, ReactNode } from 'react'

export interface DialogProps {
    isOpen: boolean
    onRequestClose?: (e?: React.MouseEvent) => void
    onClose?: () => void
    closable?: boolean
    contentClassName?: string
    overlayClassName?: string
    children?: ReactNode
    className?: string
    width?: number | string
    height?: number | string
    style?: {
        content?: React.CSSProperties
        overlay?: React.CSSProperties
    }
}

const Dialog = (props: DialogProps) => {
    const {
        children,
        className,
        closable = true,
        isOpen = false,
        onRequestClose,
        overlayClassName,
        contentClassName,
        width = 520,
        height,
        style = {},
        ...rest
    } = props

    const { width: windowWidth, height: windowHeight } = useWindowSize()

    const defaultDialogContentStyle: React.CSSProperties = {
        inset: 'unset',
    }

    const defaultDialogOverlayStyle: React.CSSProperties = {
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
    }

    const getDialogContentStyle = () => {
        const dialogContentDefaultStyle = {
            ...defaultDialogContentStyle,
            ...style.content,
        }

        if (width && typeof width === 'number') {
            const windowMargin = 40
            const widthMargin = (windowWidth || 0) - windowMargin
            const modalWidth = width <= widthMargin ? width : widthMargin
            dialogContentDefaultStyle.width = modalWidth
        }

        if (width && typeof width === 'string') {
            dialogContentDefaultStyle.width = width
        }

        if (height && typeof height === 'number') {
            const windowMargin = 40
            const heightMargin = (windowHeight || 0) - windowMargin
            const modalHeight = height <= heightMargin ? height : heightMargin
            dialogContentDefaultStyle.height = modalHeight
        }

        if (height && typeof height === 'string') {
            dialogContentDefaultStyle.height = height
        }

        return dialogContentDefaultStyle
    }

    const getDialogOverlayStyle = () => {
        return {
            ...defaultDialogOverlayStyle,
            ...style.overlay,
        }
    }

    const handleOverlayClick = (e: MouseEvent<HTMLDivElement>) => {
        if (e.target === e.currentTarget && onRequestClose) {
            onRequestClose()
        }
    }

    // Handle escape key
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isOpen && onRequestClose) {
                onRequestClose()
            }
        }

        if (isOpen) {
            document.addEventListener('keydown', handleEscape)
            document.body.style.overflow = 'hidden'
        }

        return () => {
            document.removeEventListener('keydown', handleEscape)
            document.body.style.overflow = 'unset'
        }
    }, [isOpen, onRequestClose])

    if (!isOpen) return null

    return (
        <div
            className={classNames(
                'fixed inset-0 z-50 flex items-center justify-center',
                overlayClassName
            )}
            style={getDialogOverlayStyle()}
            onClick={handleOverlayClick}
            {...rest}
        >
            <motion.div
                className={classNames(
                    'relative bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-full max-h-full overflow-auto',
                    contentClassName,
                    className
                )}
                style={getDialogContentStyle()}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
            >
                {closable && (
                    <CloseButton
                        className="absolute top-4 right-4 z-10"
                        onClick={onRequestClose}
                    />
                )}
                {children}
            </motion.div>
        </div>
    )
}

export default Dialog
