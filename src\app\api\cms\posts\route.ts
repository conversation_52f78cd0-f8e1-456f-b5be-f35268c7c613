import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

// GET /api/cms/posts - Get all posts
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const status = searchParams.get('status')
        const category = searchParams.get('category')
        const featured = searchParams.get('featured')
        
        let query = 'SELECT * FROM posts WHERE 1=1'
        const params: any[] = []
        
        if (status) {
            query += ' AND status = ?'
            params.push(status)
        }
        
        if (category) {
            query += ' AND category = ?'
            params.push(category)
        }
        
        if (featured) {
            query += ' AND featured = ?'
            params.push(featured === 'true')
        }
        
        query += ' ORDER BY created_at DESC'
        
        const [rows] = await pool.execute<RowDataPacket[]>(query, params)
        
        return NextResponse.json({
            success: true,
            data: rows
        })
    } catch (error) {
        console.error('Error fetching posts:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch posts' },
            { status: 500 }
        )
    }
}

// POST /api/cms/posts - Create new post
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { title, slug, content, excerpt, category, image, status = 'draft', featured = false } = body
        
        if (!title || !slug) {
            return NextResponse.json(
                { success: false, error: 'Title and slug are required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO posts (title, slug, content, excerpt, category, image, status, featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [title, slug, content, excerpt, category, image, status, featured]
        )
        
        return NextResponse.json({
            success: true,
            data: { id: result.insertId, title, slug, content, excerpt, category, image, status, featured }
        }, { status: 201 })
    } catch (error: any) {
        console.error('Error creating post:', error)
        
        if (error.code === 'ER_DUP_ENTRY') {
            return NextResponse.json(
                { success: false, error: 'Post with this slug already exists' },
                { status: 409 }
            )
        }
        
        return NextResponse.json(
            { success: false, error: 'Failed to create post' },
            { status: 500 }
        )
    }
}

// PUT /api/cms/posts - Update post
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { id, title, slug, content, excerpt, category, image, status, featured } = body
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Post ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE posts SET title = ?, slug = ?, content = ?, excerpt = ?, category = ?, image = ?, status = ?, featured = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [title, slug, content, excerpt, category, image, status, featured, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Post not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { id, title, slug, content, excerpt, category, image, status, featured }
        })
    } catch (error: any) {
        console.error('Error updating post:', error)
        
        if (error.code === 'ER_DUP_ENTRY') {
            return NextResponse.json(
                { success: false, error: 'Post with this slug already exists' },
                { status: 409 }
            )
        }
        
        return NextResponse.json(
            { success: false, error: 'Failed to update post' },
            { status: 500 }
        )
    }
}

// DELETE /api/cms/posts - Delete post
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Post ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM posts WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Post not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Post deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting post:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete post' },
            { status: 500 }
        )
    }
}
