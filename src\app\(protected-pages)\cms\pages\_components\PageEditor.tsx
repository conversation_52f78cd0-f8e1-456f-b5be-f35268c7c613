'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Textarea } from '@/components/ui/Textarea'
import Select from '@/components/ui/Select'
import { SaveIcon, ArrowLeftIcon, BoldIcon, ItalicIcon, ListIcon, QuoteIcon, LinkIcon, ImageIcon, CodeIcon, HeadingIcon } from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

interface Page {
    id?: number
    title: string
    slug: string
    content: string
    seo_meta: {
        title?: string
        description?: string
        keywords?: string
    }
    status: 'draft' | 'published' | 'archived'
}

interface PageEditorProps {
    page?: Page
    onSave: () => void
    onCancel: () => void
}

const VisualEditor = ({ value, onChange }: { value: string; onChange: (value: string) => void }) => {
    const [isPreview, setIsPreview] = useState(false)

    const insertText = (text: string) => {
        const textarea = document.getElementById('content') as HTMLTextAreaElement
        if (textarea) {
            const start = textarea.selectionStart
            const end = textarea.selectionEnd
            const newValue = value.substring(0, start) + text + value.substring(end)
            onChange(newValue)
            
            // Set cursor position after inserted text
            setTimeout(() => {
                textarea.focus()
                textarea.setSelectionRange(start + text.length, start + text.length)
            }, 0)
        }
    }

    const formatText = (format: string) => {
        const textarea = document.getElementById('content') as HTMLTextAreaElement
        if (textarea) {
            const start = textarea.selectionStart
            const end = textarea.selectionEnd
            const selectedText = value.substring(start, end)
            
            let formattedText = ''
            switch (format) {
                case 'bold':
                    formattedText = `<strong>${selectedText}</strong>`
                    break
                case 'italic':
                    formattedText = `<em>${selectedText}</em>`
                    break
                case 'heading':
                    formattedText = `<h2>${selectedText}</h2>`
                    break
                case 'list':
                    formattedText = `<ul>\n  <li>${selectedText}</li>\n</ul>`
                    break
                case 'quote':
                    formattedText = `<blockquote>${selectedText}</blockquote>`
                    break
                case 'link':
                    formattedText = `<a href="#" target="_blank">${selectedText}</a>`
                    break
                case 'image':
                    formattedText = `<img src="" alt="${selectedText}" />`
                    break
                case 'code':
                    formattedText = `<code>${selectedText}</code>`
                    break
                default:
                    formattedText = selectedText
            }
            
            const newValue = value.substring(0, start) + formattedText + value.substring(end)
            onChange(newValue)
        }
    }

    return (
        <div className="space-y-4">
            {/* Toolbar */}
            <div className="flex flex-wrap items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => formatText('bold')}
                    icon={<BoldIcon className="w-4 h-4" />}
                    title="Bold"
                />
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => formatText('italic')}
                    icon={<ItalicIcon className="w-4 h-4" />}
                    title="Italic"
                />
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => formatText('heading')}
                    icon={<HeadingIcon className="w-4 h-4" />}
                    title="Heading"
                />
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => formatText('list')}
                    icon={<ListIcon className="w-4 h-4" />}
                    title="List"
                />
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => formatText('quote')}
                    icon={<QuoteIcon className="w-4 h-4" />}
                    title="Quote"
                />
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => formatText('link')}
                    icon={<LinkIcon className="w-4 h-4" />}
                    title="Link"
                />
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => formatText('image')}
                    icon={<ImageIcon className="w-4 h-4" />}
                    title="Image"
                />
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => formatText('code')}
                    icon={<CodeIcon className="w-4 h-4" />}
                    title="Code"
                />
                <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
                <Button
                    variant={isPreview ? "solid" : "plain"}
                    size="sm"
                    onClick={() => setIsPreview(!isPreview)}
                >
                    {isPreview ? 'Edit' : 'Preview'}
                </Button>
            </div>

            {/* Editor/Preview */}
            {isPreview ? (
                <div className="border rounded-lg p-4 bg-white dark:bg-gray-900 min-h-[300px]">
                    <div 
                        className="prose dark:prose-invert max-w-none"
                        dangerouslySetInnerHTML={{ __html: value }}
                    />
                </div>
            ) : (
                <Textarea
                    id="content"
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    placeholder="Enter page content... Use the toolbar above to format your text."
                    rows={15}
                    className="font-mono text-sm"
                />
            )}

            {/* Quick Insert Buttons */}
            <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Quick Insert:</span>
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => insertText('<h1>Heading 1</h1>')}
                >
                    H1
                </Button>
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => insertText('<h2>Heading 2</h2>')}
                >
                    H2
                </Button>
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => insertText('<p>Paragraph text</p>')}
                >
                    Paragraph
                </Button>
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => insertText('<ul>\n  <li>List item 1</li>\n  <li>List item 2</li>\n</ul>')}
                >
                    List
                </Button>
                <Button
                    variant="plain"
                    size="sm"
                    onClick={() => insertText('<blockquote>Quote text</blockquote>')}
                >
                    Quote
                </Button>
            </div>
        </div>
    )
}

const PageEditor = ({ page, onSave, onCancel }: PageEditorProps) => {
    const [formData, setFormData] = useState<Page>({
        title: '',
        slug: '',
        content: '',
        seo_meta: {
            title: '',
            description: '',
            keywords: ''
        },
        status: 'draft'
    })
    const [isSaving, setIsSaving] = useState(false)

    useEffect(() => {
        if (page) {
            setFormData({
                ...page,
                seo_meta: typeof page.seo_meta === 'string' 
                    ? JSON.parse(page.seo_meta) 
                    : page.seo_meta || {}
            })
        }
    }, [page])

    const generateSlug = (title: string) => {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim()
    }

    const handleTitleChange = (value: string) => {
        setFormData(prev => ({
            ...prev,
            title: value,
            slug: !page ? generateSlug(value) : prev.slug // Only auto-generate for new pages
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        
        if (!formData.title.trim() || !formData.slug.trim()) {
            toast.push(
                <Notification type="danger" title="Error">
                    Title and slug are required
                </Notification>
            )
            return
        }

        setIsSaving(true)

        try {
            const url = '/api/cms/pages'
            const method = page ? 'PUT' : 'POST'
            const body = page ? { ...formData, id: page.id } : formData

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            })

            const result = await response.json()

            if (result.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Page {page ? 'updated' : 'created'} successfully
                    </Notification>
                )
                onSave()
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {result.error || `Failed to ${page ? 'update' : 'create'} page`}
                    </Notification>
                )
            }
        } catch (error) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to {page ? 'update' : 'create'} page
                </Notification>
            )
            console.error('Error saving page:', error)
        } finally {
            setIsSaving(false)
        }
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="plain" onClick={onCancel} icon={<ArrowLeftIcon />}>
                        <span>Back to Pages</span>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">
                            {page ? 'Edit Page' : 'Create New Page'}
                        </h1>
                        <p className="text-muted-foreground">
                            {page ? 'Update page content and settings' : 'Create a new page for your website'}
                        </p>
                    </div>
                </div>
                <Button
                    onClick={handleSubmit}
                    disabled={isSaving}
                    icon={<SaveIcon />}
                >
                    <span>{isSaving ? 'Saving...' : 'Save Page'}</span>
                </Button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        <Card header={{ content: 'Page Content' }} className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="title">Page Title *</Label>
                                    <Input
                                        id="title"
                                        value={formData.title}
                                        onChange={(e) => handleTitleChange(e.target.value)}
                                        placeholder="Enter page title"
                                        required
                                    />
                                </div>
                                
                                <div>
                                    <Label htmlFor="slug">URL Slug *</Label>
                                    <Input
                                        id="slug"
                                        value={formData.slug}
                                        onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                                        placeholder="page-url-slug"
                                        required
                                    />
                                    <p className="text-sm text-muted-foreground mt-1">
                                        URL: /{formData.slug}
                                    </p>
                                </div>
                                
                                <div>
                                    <Label htmlFor="content">Page Content</Label>
                                    <VisualEditor
                                        value={formData.content}
                                        onChange={(value) => setFormData(prev => ({ ...prev, content: value }))}
                                    />
                                </div>
                            </div>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card header={{ content: 'Page Settings' }} className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="status">Status</Label>
                                    <Select
                                        value={{ value: formData.status, label: formData.status.charAt(0).toUpperCase() + formData.status.slice(1) }}
                                        options={[
                                            { value: 'draft', label: 'Draft' },
                                            { value: 'published', label: 'Published' },
                                            { value: 'archived', label: 'Archived' }
                                        ]}
                                        onChange={(option: any) => setFormData(prev => ({
                                            ...prev,
                                            status: option?.value as 'draft' | 'published' | 'archived'
                                        }))}
                                    />
                                </div>
                            </div>
                        </Card>

                        <Card header={{ content: 'SEO Settings' }} className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="seo-title">SEO Title</Label>
                                    <Input
                                        id="seo-title"
                                        value={formData.seo_meta.title || ''}
                                        onChange={(e) => setFormData(prev => ({
                                            ...prev,
                                            seo_meta: { ...prev.seo_meta, title: e.target.value }
                                        }))}
                                        placeholder="SEO title for search engines"
                                    />
                                </div>
                                
                                <div>
                                    <Label htmlFor="seo-description">SEO Description</Label>
                                    <Textarea
                                        id="seo-description"
                                        value={formData.seo_meta.description || ''}
                                        onChange={(e) => setFormData(prev => ({
                                            ...prev,
                                            seo_meta: { ...prev.seo_meta, description: e.target.value }
                                        }))}
                                        placeholder="SEO description for search engines"
                                        rows={3}
                                    />
                                </div>
                                
                                <div>
                                    <Label htmlFor="seo-keywords">SEO Keywords</Label>
                                    <Input
                                        id="seo-keywords"
                                        value={formData.seo_meta.keywords || ''}
                                        onChange={(e) => setFormData(prev => ({
                                            ...prev,
                                            seo_meta: { ...prev.seo_meta, keywords: e.target.value }
                                        }))}
                                        placeholder="keyword1, keyword2, keyword3"
                                    />
                                </div>
                            </div>
                        </Card>
                    </div>
                </div>
            </form>
        </div>
    )
}

export default PageEditor
