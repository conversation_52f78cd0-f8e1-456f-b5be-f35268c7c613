export const accessModules = [
    {
        id: 'users',
        name: 'User management',
        description: 'Access control for user management',
        accessor: [
            {
                label: 'Read',
                value: 'read',
            },
            {
                label: 'Write',
                value: 'write',
            },
            {
                label: 'Delete',
                value: 'delete',
            },
        ],
    },
    {
        id: 'products',
        name: 'Products authority',
        description: 'Access control for product operations',
        accessor: [
            {
                label: 'Read',
                value: 'read',
            },
            {
                label: 'Write',
                value: 'write',
            },
            {
                label: 'Delete',
                value: 'delete',
            },
        ],
    },
    {
        id: 'configurations',
        name: 'System configurations',
        description: 'Access control for system settings',
        accessor: [
            {
                label: 'Read',
                value: 'read',
            },
            {
                label: 'Write',
                value: 'write',
            },
            {
                label: 'Delete',
                value: 'delete',
            },
        ],
    },
    {
        id: 'files',
        name: 'File management',
        description: 'Access control for file management',
        accessor: [
            {
                label: 'Read',
                value: 'read',
            },
            {
                label: 'Write',
                value: 'write',
            },
            {
                label: 'Delete',
                value: 'delete',
            },
        ],
    },
    {
        id: 'reports',
        name: 'Reports',
        description: 'Access control for generating reports',
        accessor: [
            {
                label: 'Read',
                value: 'read',
            },
            {
                label: 'Write',
                value: 'write',
            },
            {
                label: 'Delete',
                value: 'delete',
            },
        ],
    },
]
