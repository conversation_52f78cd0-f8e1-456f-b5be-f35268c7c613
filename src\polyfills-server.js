// Server-side polyfills for Node.js environment
// This file is imported at the top of next.config.mjs to ensure polyfills are available during build

if (typeof global !== 'undefined') {
  // Define self for server-side rendering
  if (typeof global.self === 'undefined') {
    global.self = global;
  }
  
  // Define globalThis for older Node.js versions
  if (typeof global.globalThis === 'undefined') {
    global.globalThis = global;
  }
  
  // Define window as undefined for server-side
  if (typeof global.window === 'undefined') {
    global.window = undefined;
  }
  
  // Define document as undefined for server-side
  if (typeof global.document === 'undefined') {
    global.document = undefined;
  }
  
  // Define navigator as undefined for server-side
  if (typeof global.navigator === 'undefined') {
    global.navigator = undefined;
  }

  // Define location as undefined for server-side
  if (typeof global.location === 'undefined') {
    global.location = undefined;
  }

  // Define localStorage as undefined for server-side
  if (typeof global.localStorage === 'undefined') {
    global.localStorage = undefined;
  }

  // Define sessionStorage as undefined for server-side
  if (typeof global.sessionStorage === 'undefined') {
    global.sessionStorage = undefined;
  }

  // Define screen as undefined for server-side
  if (typeof global.screen === 'undefined') {
    global.screen = undefined;
  }

  // Define history as undefined for server-side
  if (typeof global.history === 'undefined') {
    global.history = undefined;
  }

  // Define performance as undefined for server-side
  if (typeof global.performance === 'undefined') {
    global.performance = undefined;
  }

  // Define crypto as undefined for server-side
  if (typeof global.crypto === 'undefined') {
    global.crypto = undefined;
  }

  // Define indexedDB as undefined for server-side
  if (typeof global.indexedDB === 'undefined') {
    global.indexedDB = undefined;
  }

  // Define webkitIndexedDB as undefined for server-side
  if (typeof global.webkitIndexedDB === 'undefined') {
    global.webkitIndexedDB = undefined;
  }

  // Define mozIndexedDB as undefined for server-side
  if (typeof global.mozIndexedDB === 'undefined') {
    global.mozIndexedDB = undefined;
  }

  // Define msIndexedDB as undefined for server-side
  if (typeof global.msIndexedDB === 'undefined') {
    global.msIndexedDB = undefined;
  }
}
