'use client'

import { useEffect, useState } from 'react'
import type { ReactNode } from 'react'

interface HydrationSafeWrapperProps {
    children: ReactNode
    className?: string
    suppressHydrationWarning?: boolean
}

/**
 * HydrationSafeWrapper - Prevents hydration mismatches caused by browser extensions
 * 
 * This component handles cases where browser extensions (like <PERSON><PERSON>, AVG, <PERSON><PERSON><PERSON>, etc.)
 * inject attributes like 'bis_skin_checked="1"' into DOM elements, causing hydration errors.
 * 
 * Usage:
 * <HydrationSafeWrapper>
 *   <YourComponent />
 * </HydrationSafeWrapper>
 */
const HydrationSafeWrapper = ({ 
    children, 
    className,
    suppressHydrationWarning = true 
}: HydrationSafeWrapperProps) => {
    const [isClient, setIsClient] = useState(false)

    useEffect(() => {
        // Only render on client side to avoid hydration mismatches
        setIsClient(true)
    }, [])

    if (!isClient) {
        // Server-side render: return a simple wrapper
        return (
            <div 
                className={className}
                suppressHydrationWarning={suppressHydrationWarning}
            >
                {children}
            </div>
        )
    }

    // Client-side render: return the actual content
    return (
        <div 
            className={className}
            suppressHydrationWarning={suppressHydrationWarning}
        >
            {children}
        </div>
    )
}

export default HydrationSafeWrapper
