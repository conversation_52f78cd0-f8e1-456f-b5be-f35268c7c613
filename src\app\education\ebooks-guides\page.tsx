'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbBook, TbDownload, TbStar, TbClock, TbFileText, TbChartLine, TbBrain, TbShield } from 'react-icons/tb'

const EbooksGuidesPage = () => {
    const featuredGuides = [
        {
            title: 'Complete Forex Trading Guide',
            description: 'Comprehensive 150-page guide covering everything from basics to advanced strategies.',
            pages: 150,
            downloads: '12.5K',
            rating: 4.9,
            level: 'All Levels',
            category: 'Complete Guide',
            topics: ['Forex Basics', 'Technical Analysis', 'Fundamental Analysis', 'Risk Management', 'Trading Psychology'],
            icon: TbBook,
            color: 'bg-[#EA5455]/10 text-[#EA5455]'
        },
        {
            title: 'Technical Analysis Mastery',
            description: 'In-depth guide to chart patterns, indicators, and technical trading strategies.',
            pages: 95,
            downloads: '8.9K',
            rating: 4.8,
            level: 'Intermediate',
            category: 'Technical Analysis',
            topics: ['Chart Patterns', 'Technical Indicators', 'Price Action', 'Multi-Timeframe Analysis'],
            icon: TbChartLine,
            color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
        },
        {
            title: 'Risk Management Handbook',
            description: 'Essential guide to protecting your trading capital and managing risk effectively.',
            pages: 75,
            downloads: '9.8K',
            rating: 4.9,
            level: 'All Levels',
            category: 'Risk Management',
            topics: ['Position Sizing', 'Stop Loss Strategies', 'Portfolio Management', 'Drawdown Control'],
            icon: TbShield,
            color: 'bg-[#28C76F]/10 text-[#28C76F]'
        },
        {
            title: 'Trading Psychology Guide',
            description: 'Master the mental game of trading and overcome psychological barriers.',
            pages: 85,
            downloads: '7.2K',
            rating: 4.7,
            level: 'Intermediate',
            category: 'Psychology',
            topics: ['Emotional Control', 'Discipline', 'Confidence Building', 'Stress Management'],
            icon: TbBrain,
            color: 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400'
        }
    ]

    const guideCategories = [
        {
            title: 'Beginner Guides',
            description: 'Start your trading journey with our comprehensive beginner resources.',
            count: 8,
            icon: TbBook,
            guides: [
                'Forex Trading Fundamentals',
                'Understanding Currency Pairs',
                'How to Read Charts',
                'Basic Trading Strategies',
                'Platform Tutorial Guide',
                'First Trade Walkthrough',
                'Common Beginner Mistakes',
                'Building Trading Discipline'
            ]
        },
        {
            title: 'Strategy Guides',
            description: 'Proven trading strategies and techniques from professional traders.',
            count: 12,
            icon: TbChartLine,
            guides: [
                'Scalping Strategies',
                'Swing Trading Guide',
                'Trend Following Systems',
                'Range Trading Techniques',
                'Breakout Trading Methods',
                'News Trading Strategies',
                'Carry Trade Guide',
                'Algorithmic Trading Basics',
                'Multi-Timeframe Analysis',
                'Price Action Trading',
                'Fibonacci Trading Guide',
                'Support & Resistance Mastery'
            ]
        },
        {
            title: 'Market Analysis',
            description: 'Learn to analyze markets using technical and fundamental approaches.',
            count: 10,
            icon: TbFileText,
            guides: [
                'Technical Analysis Complete Guide',
                'Fundamental Analysis Handbook',
                'Economic Calendar Usage',
                'Central Bank Policy Analysis',
                'Market Sentiment Analysis',
                'Correlation Analysis Guide',
                'Volatility Analysis Methods',
                'Seasonal Trading Patterns',
                'Inter-market Analysis',
                'News Impact Assessment'
            ]
        }
    ]

    const quickGuides = [
        { title: 'Quick Start Trading Checklist', pages: 5, downloads: '15.2K' },
        { title: '10 Essential Trading Rules', pages: 8, downloads: '13.8K' },
        { title: 'Risk Management Cheat Sheet', pages: 4, downloads: '11.9K' },
        { title: 'Economic Calendar Guide', pages: 6, downloads: '9.5K' },
        { title: 'Platform Setup Guide', pages: 7, downloads: '8.7K' },
        { title: 'Common Trading Mistakes', pages: 9, downloads: '7.3K' }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    E-books & Guides
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-100 mb-8 max-w-3xl mx-auto">
                                    Download comprehensive trading guides and e-books to accelerate your forex education
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-colors duration-300"
                                    >
                                        Access All Guides
                                    </button>
                                    <button 
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#1E1E1E] transition-colors duration-300"
                                    >
                                        Browse Library
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Featured Guides Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Featured Trading Guides
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Our most popular and comprehensive trading guides, trusted by thousands of traders worldwide.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {featuredGuides.map((guide, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <guide.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between mb-3">
                                                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${guide.color}`}>
                                                        {guide.level}
                                                    </span>
                                                    <div className="flex items-center space-x-1">
                                                        <TbStar className="w-4 h-4 text-yellow-400 fill-current" />
                                                        <span className="text-sm text-gray-600 dark:text-gray-400">{guide.rating}</span>
                                                    </div>
                                                </div>
                                                
                                                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                                    {guide.title}
                                                </h3>
                                                
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {guide.description}
                                                </p>
                                                
                                                <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                                                    <span>{guide.pages} pages</span>
                                                    <span>{guide.downloads} downloads</span>
                                                </div>
                                                
                                                <div className="mb-4">
                                                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm mb-2">Topics Covered:</h4>
                                                    <div className="flex flex-wrap gap-1">
                                                        {guide.topics.map((topic, topicIndex) => (
                                                            <span key={topicIndex} className="px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-xs">
                                                                {topic}
                                                            </span>
                                                        ))}
                                                    </div>
                                                </div>
                                                
                                                <button className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-3 rounded-lg font-semibold transition-colors duration-300 flex items-center justify-center space-x-2">
                                                    <TbDownload className="w-5 h-5" />
                                                    <span>Download Guide</span>
                                                </button>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Guide Categories Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Guide Categories
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Explore our organized collection of trading guides by category and skill level.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                {guideCategories.map((category, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="text-center mb-6">
                                            <div className="w-12 h-12 bg-[#28C76F]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                                                <category.icon className="w-6 h-6 text-[#28C76F]" />
                                            </div>
                                            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                                {category.title}
                                            </h3>
                                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                {category.description}
                                            </p>
                                            <span className="inline-block px-3 py-1 bg-[#EA5455]/10 text-[#EA5455] rounded-full text-sm font-medium">
                                                {category.count} Guides
                                            </span>
                                        </div>

                                        <div className="space-y-2">
                                            {category.guides.map((guide, guideIndex) => (
                                                <div key={guideIndex} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-[#EA5455] dark:hover:text-[#EA5455] cursor-pointer transition-colors duration-200">
                                                    <TbFileText className="w-4 h-4 flex-shrink-0" />
                                                    <span>{guide}</span>
                                                </div>
                                            ))}
                                        </div>

                                        <button className="w-full mt-6 bg-[#28C76F] hover:bg-[#28C76F]/90 text-white py-3 rounded-lg font-semibold transition-colors duration-300">
                                            Browse Category
                                        </button>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Quick Guides Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Quick Reference Guides
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Short, focused guides for quick reference and immediate application.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {quickGuides.map((guide, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300 cursor-pointer"
                                    >
                                        <div className="flex items-center space-x-3 mb-4">
                                            <div className="w-10 h-10 bg-[#EA5455]/10 rounded-lg flex items-center justify-center">
                                                <TbFileText className="w-5 h-5 text-[#EA5455]" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="font-bold text-gray-900 dark:text-white">
                                                    {guide.title}
                                                </h3>
                                            </div>
                                        </div>

                                        <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                                            <span>{guide.pages} pages</span>
                                            <span>{guide.downloads} downloads</span>
                                        </div>

                                        <button className="w-full bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-2 rounded-lg font-semibold transition-colors duration-300 flex items-center justify-center space-x-2">
                                            <TbDownload className="w-4 h-4" />
                                            <span>Download</span>
                                        </button>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#EA5455] to-[#EA5455]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Build Your Trading Library Today
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Get instant access to our complete collection of trading guides and e-books.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-white text-[#EA5455] px-5 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Access Full Library
                                    </button>
                                    <button
                                        onClick={() => window.open('/education/demo-account', '_blank')}
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#EA5455] transition-colors duration-300"
                                    >
                                        Start with Demo
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default EbooksGuidesPage
