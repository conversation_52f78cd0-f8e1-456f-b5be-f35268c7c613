'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { PlusIcon, EditIcon, TrashIcon, EyeIcon } from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import PageEditor from './PageEditor'

interface Page {
    id: number
    title: string
    slug: string
    content: string
    seo_meta: any
    status: 'draft' | 'published' | 'archived'
    created_at: string
    updated_at: string
}

const PagesManagement = () => {
    const [pages, setPages] = useState<Page[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [showEditor, setShowEditor] = useState(false)
    const [editingPage, setEditingPage] = useState<Page | undefined>(undefined)

    // Fetch pages from API
    const fetchPages = async () => {
        try {
            setLoading(true)
            const response = await fetch('/api/cms/pages')
            const data = await response.json()
            
            if (data.success) {
                setPages(data.data)
            } else {
                setError(data.error || 'Failed to fetch pages')
            }
        } catch (err) {
            setError('Failed to fetch pages')
            console.error('Error fetching pages:', err)
        } finally {
            setLoading(false)
        }
    }

    // Delete page
    const deletePage = async (id: number) => {
        if (!confirm('Are you sure you want to delete this page?')) {
            return
        }

        try {
            const response = await fetch(`/api/cms/pages?id=${id}`, {
                method: 'DELETE'
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Page deleted successfully
                    </Notification>
                )
                fetchPages() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to delete page'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to delete page
                </Notification>
            )
            console.error('Error deleting page:', err)
        }
    }

    // Get status badge color
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'published':
                return 'bg-green-100 text-green-800'
            case 'draft':
                return 'bg-yellow-100 text-yellow-800'
            case 'archived':
                return 'bg-gray-100 text-gray-800'
            default:
                return 'bg-gray-100 text-gray-800'
        }
    }

    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    }

    // Handle create new page
    const handleCreatePage = () => {
        setEditingPage(undefined)
        setShowEditor(true)
    }

    // Handle edit page
    const handleEditPage = (page: Page) => {
        setEditingPage(page)
        setShowEditor(true)
    }

    // Handle editor save
    const handleEditorSave = () => {
        setShowEditor(false)
        setEditingPage(undefined)
        fetchPages() // Refresh the list
    }

    // Handle editor cancel
    const handleEditorCancel = () => {
        setShowEditor(false)
        setEditingPage(undefined)
    }

    useEffect(() => {
        fetchPages()
    }, [])

    // Show editor if in edit mode
    if (showEditor) {
        return (
            <PageEditor
                page={editingPage}
                onSave={handleEditorSave}
                onCancel={handleEditorCancel}
            />
        )
    }

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Pages Management</h1>
                        <p className="text-muted-foreground">
                            Create and manage website pages
                        </p>
                    </div>
                </div>
                <Card>
                    <div className="p-6">
                        <div className="text-center">Loading pages...</div>
                    </div>
                </Card>
            </div>
        )
    }

    if (error) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Pages Management</h1>
                        <p className="text-muted-foreground">
                            Create and manage website pages
                        </p>
                    </div>
                </div>
                <Card>
                    <div className="p-6">
                        <div className="text-center text-red-600">
                            Error: {error}
                            <br />
                            <Button onClick={fetchPages} className="mt-4">
                                Retry
                            </Button>
                        </div>
                    </div>
                </Card>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Pages Management</h1>
                    <p className="text-muted-foreground">
                        Create and manage website pages
                    </p>
                </div>
                <Button onClick={handleCreatePage} icon={<PlusIcon />}>
                    <span>Add New Page</span>
                </Button>
            </div>

            <Card header={{ content: `All Pages (${pages.length})` }}>
                    {pages.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-muted-foreground">No pages found</p>
                            <Button className="mt-4" onClick={handleCreatePage} icon={<PlusIcon />}>
                                <span>Create Your First Page</span>
                            </Button>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {pages.map((page) => (
                                <div key={page.id} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-1">
                                            <h3 className="font-semibold">{page.title}</h3>
                                            <Badge className={getStatusColor(page.status)}>
                                                {page.status}
                                            </Badge>
                                        </div>
                                        <p className="text-sm text-muted-foreground mb-1">
                                            Slug: /{page.slug}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            Last updated: {formatDate(page.updated_at)}
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button variant="plain" size="sm" title="View" icon={<EyeIcon />}>
                                        </Button>
                                        <Button
                                            variant="plain"
                                            size="sm"
                                            title="Edit"
                                            onClick={() => handleEditPage(page)}
                                            icon={<EditIcon />}
                                        >
                                        </Button>
                                        <Button
                                            variant="plain"
                                            size="sm"
                                            title="Delete"
                                            onClick={() => deletePage(page.id)}
                                            icon={<TrashIcon />}
                                        >
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
            </Card>
        </div>
    )
}

export default PagesManagement
