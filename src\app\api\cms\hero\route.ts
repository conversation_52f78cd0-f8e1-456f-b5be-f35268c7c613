import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

// GET /api/cms/hero - Get all hero sections
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const active = searchParams.get('active')
        
        let query = 'SELECT * FROM hero_sections'
        const params: any[] = []
        
        if (active) {
            query += ' WHERE is_active = ?'
            params.push(active === 'true')
        }
        
        query += ' ORDER BY created_at DESC'
        
        const [rows] = await pool.execute<RowDataPacket[]>(query, params)
        
        return NextResponse.json({
            success: true,
            data: rows
        })
    } catch (error) {
        console.error('Error fetching hero sections:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch hero sections' },
            { status: 500 }
        )
    }
}

// POST /api/cms/hero - Create new hero section
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            title, 
            subtitle, 
            video_url, 
            cta_link, 
            cta_text, 
            image, 
            background_type = 'image', 
            is_active = true 
        } = body
        
        if (!title) {
            return NextResponse.json(
                { success: false, error: 'Title is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO hero_sections (title, subtitle, video_url, cta_link, cta_text, image, background_type, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [title, subtitle, video_url, cta_link, cta_text, image, background_type, is_active]
        )
        
        return NextResponse.json({
            success: true,
            data: { 
                id: result.insertId, 
                title, 
                subtitle, 
                video_url, 
                cta_link, 
                cta_text, 
                image, 
                background_type, 
                is_active 
            }
        }, { status: 201 })
    } catch (error) {
        console.error('Error creating hero section:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to create hero section' },
            { status: 500 }
        )
    }
}

// PUT /api/cms/hero - Update hero section
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            id, 
            title, 
            subtitle, 
            video_url, 
            cta_link, 
            cta_text, 
            image, 
            background_type, 
            is_active 
        } = body
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Hero section ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE hero_sections SET title = ?, subtitle = ?, video_url = ?, cta_link = ?, cta_text = ?, image = ?, background_type = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [title, subtitle, video_url, cta_link, cta_text, image, background_type, is_active, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Hero section not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { id, title, subtitle, video_url, cta_link, cta_text, image, background_type, is_active }
        })
    } catch (error) {
        console.error('Error updating hero section:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to update hero section' },
            { status: 500 }
        )
    }
}

// DELETE /api/cms/hero - Delete hero section
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Hero section ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM hero_sections WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Hero section not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Hero section deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting hero section:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete hero section' },
            { status: 500 }
        )
    }
}
