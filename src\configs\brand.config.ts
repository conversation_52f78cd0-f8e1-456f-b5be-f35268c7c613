/**
 * MyBrokerForex Brand Configuration
 * Centralized brand settings for consistent usage across the application
 */

export const BRAND_CONFIG = {
    // Company Information
    name: 'MyBrokerForex',
    fullName: 'MyBrokerForex Ltd',
    tagline: 'Professional Trading',
    description: 'Leading online forex broker providing professional trading services to retail and institutional clients worldwide.',
    
    // Contact Information
    contact: {
        email: '<EMAIL>',
        phone: '+****************',
        address: '123 Financial District, London EC2V 8RF, UK',
        website: 'https://mybrokerforex.com'
    },
    
    // Social Media
    social: {
        facebook: 'https://facebook.com/mybrokerforex',
        twitter: 'https://twitter.com/mybrokerforex',
        linkedin: 'https://linkedin.com/company/mybrokerforex',
        youtube: 'https://youtube.com/mybrokerforex',
        instagram: 'https://instagram.com/mybrokerforex'
    },
    
    // Brand Colors
    colors: {
        primary: '#EA5455',      // Red
        secondary: '#28C76F',    // Green
        dark: '#1E1E1E',        // Dark
        light: '#F8F8F8',       // Light
        white: '#FFFFFF',
        black: '#000000'
    },
    
    // Regulatory Information
    regulatory: {
        fcaNumber: '123456',
        companyRegistration: '12345678',
        fscsProtection: '£85,000',
        jurisdiction: 'United Kingdom'
    },
    
    // SEO & Meta
    seo: {
        title: 'MyBrokerForex - Professional Forex Trading Platform',
        description: 'Trade forex with MyBrokerForex. Competitive spreads, advanced trading platform, and expert support. FCA regulated broker with segregated client funds.',
        keywords: 'forex trading, forex broker, online trading, currency trading, FCA regulated, professional trading platform',
        author: 'MyBrokerForex Ltd'
    },
    
    // Logo Configuration
    logo: {
        text: 'MB',
        width: 120,
        height: 40,
        alt: 'MyBrokerForex Logo'
    }
} as const

export type BrandConfig = typeof BRAND_CONFIG

export default BRAND_CONFIG
