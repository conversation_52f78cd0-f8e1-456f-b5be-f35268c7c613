# MyBrokerForex Environment Variables

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=mybrokerforex

# Forex Data API Configuration
# Get your free API key from: https://www.alphavantage.co/support/#api-key
NEXT_PUBLIC_ALPHA_VANTAGE_API_KEY=HK1BYS44MJ5G7WUQ

# Optional: Additional Forex Data Providers
# NEXT_PUBLIC_FIXER_API_KEY=your_fixer_api_key_here
# NEXT_PUBLIC_TRADERMADE_API_KEY=your_tradermade_api_key_here
# NEXT_PUBLIC_POLYGON_API_KEY=your_polygon_api_key_here

# Application Configuration
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# Firebase Configuration (if using)
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=
