'use client'

import { motion } from 'framer-motion'
import { Tb<PERSON><PERSON>, TbApi, TbShield, TbBook, TbTarget, TbCopy } from 'react-icons/tb'
import { useState } from 'react'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateApiPage = () => {
    const [copiedCode, setCopiedCode] = useState('')

    const apiFeatures = [
        {
            icon: TbApi,
            title: 'RESTful API',
            description: 'Modern REST API with JSON responses for easy integration.',
            features: ['HTTP/HTTPS support', 'JSON format', 'Rate limiting', 'Error handling']
        },
        {
            icon: TbShield,
            title: 'Secure Authentication',
            description: 'API key-based authentication with secure token management.',
            features: ['API key authentication', 'Token refresh', 'IP whitelisting', 'SSL encryption']
        },
        {
            icon: TbBook,
            title: 'Comprehensive Documentation',
            description: 'Detailed documentation with examples and code samples.',
            features: ['Interactive docs', 'Code examples', 'SDKs available', 'Postman collection']
        },
        {
            icon: TbC<PERSON>,
            title: 'Multiple Languages',
            description: 'Support for popular programming languages and frameworks.',
            features: ['PHP SDK', 'Python SDK', 'JavaScript SDK', 'cURL examples']
        }
    ]

    const apiEndpoints = [
        {
            method: 'GET',
            endpoint: '/api/v1/affiliate/stats',
            description: 'Get affiliate performance statistics',
            response: 'Returns clicks, conversions, and revenue data'
        },
        {
            method: 'GET',
            endpoint: '/api/v1/affiliate/clients',
            description: 'Retrieve referred client information',
            response: 'Returns list of referred clients and their status'
        },
        {
            method: 'GET',
            endpoint: '/api/v1/affiliate/commissions',
            description: 'Get commission history and payments',
            response: 'Returns commission data and payment history'
        },
        {
            method: 'POST',
            endpoint: '/api/v1/affiliate/links',
            description: 'Generate new tracking links',
            response: 'Returns newly created tracking link'
        }
    ]

    const codeExamples = [
        {
            language: 'PHP',
            code: `<?php
$api_key = 'your_api_key_here';
$url = 'https://api.mybrokerforex.com/v1/affiliate/stats';

$headers = [
    'Authorization: Bearer ' . $api_key,
    'Content-Type: application/json'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$data = json_decode($response, true);
curl_close($ch);

print_r($data);`
        },
        {
            language: 'JavaScript',
            code: `const apiKey = 'your_api_key_here';
const url = 'https://api.mybrokerforex.com/v1/affiliate/stats';

fetch(url, {
    method: 'GET',
    headers: {
        'Authorization': \`Bearer \${apiKey}\`,
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    console.log(data);
})
.catch(error => {
    console.error('Error:', error);
});`
        },
        {
            language: 'Python',
            code: `import requests

api_key = 'your_api_key_here'
url = 'https://api.mybrokerforex.com/v1/affiliate/stats'

headers = {
    'Authorization': f'Bearer {api_key}',
    'Content-Type': 'application/json'
}

response = requests.get(url, headers=headers)
data = response.json()

print(data)`
        }
    ]

    const copyToClipboard = (code: string, language: string) => {
        navigator.clipboard.writeText(code)
        setCopiedCode(language)
        setTimeout(() => setCopiedCode(''), 2000)
    }

    return (
        <PageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl text-gray-300 mt-8 md:text-6xl font-bold mb-6">
                                API Documentation
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Integrate MyBrokerForex affiliate data into your applications
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* API Features */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                API Features
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Powerful API features to integrate affiliate data into your systems
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {apiFeatures.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center mb-6">
                                        <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                            <feature.icon className="w-6 h-6 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {feature.title}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {feature.description}
                                    </p>
                                    <ul className="space-y-2">
                                        {feature.features.map((item, itemIndex) => (
                                            <li key={itemIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                {item}
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* API Endpoints */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                API Endpoints
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Available endpoints for accessing your affiliate data
                            </p>
                        </motion.div>

                        <div className="space-y-6">
                            {apiEndpoints.map((endpoint, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6"
                                >
                                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                        <div className="flex items-center mb-2 md:mb-0">
                                            <span className={`px-3 py-1 rounded-full text-sm font-medium mr-4 ${
                                                endpoint.method === 'GET' 
                                                    ? 'bg-[#28C76F] text-white' 
                                                    : 'bg-[#EA5455] text-white'
                                            }`}>
                                                {endpoint.method}
                                            </span>
                                            <code className="text-lg font-mono text-gray-900 dark:text-white">
                                                {endpoint.endpoint}
                                            </code>
                                        </div>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-2">
                                        {endpoint.description}
                                    </p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        <strong>Response:</strong> {endpoint.response}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Code Examples */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Code Examples
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Sample code to get you started with the API integration
                            </p>
                        </motion.div>

                        <div className="space-y-8">
                            {codeExamples.map((example, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-900 rounded-xl overflow-hidden"
                                >
                                    <div className="flex items-center justify-between px-6 py-4 bg-gray-800">
                                        <h3 className="text-lg font-semibold text-white">
                                            {example.language}
                                        </h3>
                                        <button
                                            onClick={() => copyToClipboard(example.code, example.language)}
                                            className="flex items-center text-gray-300 hover:text-white transition-colors duration-200"
                                        >
                                            <TbCopy className="w-4 h-4 mr-1" />
                                            {copiedCode === example.language ? 'Copied!' : 'Copy'}
                                        </button>
                                    </div>
                                    <pre className="p-6 text-sm text-gray-300 overflow-x-auto">
                                        <code>{example.code}</code>
                                    </pre>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Getting Started */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-4xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Getting Started
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                            {[
                                { step: '1', title: 'Get API Key', description: 'Request your API key from the affiliate dashboard' },
                                { step: '2', title: 'Read Docs', description: 'Review the complete API documentation and examples' },
                                { step: '3', title: 'Test Integration', description: 'Test your integration in the sandbox environment' },
                                { step: '4', title: 'Go Live', description: 'Deploy your integration to production' }
                            ].map((item, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="text-center"
                                >
                                    <div className="w-12 h-12 bg-[#EA5455] text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                                        {item.step}
                                    </div>
                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                        {item.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {item.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Support */}
                <section className="py-20">
                    <div className="max-w-4xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-gradient-to-r from-[#EA5455] to-[#d63384] rounded-2xl p-8 text-white"
                        >
                            <h2 className="text-3xl font-bold mb-4">
                                Need API Support?
                            </h2>
                            <p className="text-xl text-white/90 mb-8">
                                Our technical team is here to help you with API integration
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button 
                                    onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                                    className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-colors duration-200"
                                >
                                    Contact API Support
                                </button>
                                <button 
                                    onClick={() => window.open('https://docs.mybrokerforex.com/api', '_blank')}
                                    className="bg-white/20 text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/30 transition-colors duration-200"
                                >
                                    View Full Documentation
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-[#EA5455]">
                    <div className="max-w-7xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                Ready to Integrate?
                            </h2>
                            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                Join our affiliate program and get access to our powerful API
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                Join Affiliate Program
                            </button>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PageLayout>
    )
}

export default AffiliateApiPage
