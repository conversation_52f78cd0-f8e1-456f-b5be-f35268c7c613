'use client'

import { motion } from 'framer-motion'
import { Tb<PERSON>ews, TbT<PERSON>dingUp, Tb<PERSON>lock, TbTarget, TbChartLine, TbWorld } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

const TradingNewsPage = () => {
    const newsFeatures = [
        {
            icon: TbNews,
            title: 'Real-Time News',
            description: 'Get instant access to breaking financial news that impacts currency markets.',
            features: ['Breaking News Alerts', 'Market Updates', 'Economic Reports', 'Central Bank News']
        },
        {
            icon: TbChartLine,
            title: 'Market Analysis',
            description: 'Expert analysis and commentary on how news events affect trading opportunities.',
            features: ['Expert Commentary', 'Market Impact Analysis', 'Trading Implications', 'Price Forecasts']
        },
        {
            icon: TbWorld,
            title: 'Global Coverage',
            description: 'Comprehensive coverage of financial news from major markets worldwide.',
            features: ['US Markets', 'European Markets', 'Asian Markets', 'Emerging Markets']
        },
        {
            icon: TbTarget,
            title: 'Trading Insights',
            description: 'Actionable insights to help you make informed trading decisions.',
            features: ['Trade Ideas', 'Risk Alerts', 'Opportunity Identification', 'Market Sentiment']
        }
    ]

    const latestNews = [
        {
            title: 'Federal Reserve Maintains Interest Rates at 5.25-5.50%',
            summary: 'The Fed keeps rates unchanged as inflation shows signs of cooling, signaling potential rate cuts in 2024.',
            category: 'Central Banks',
            time: '2 hours ago',
            impact: 'High',
            image: '/images/news/fed-meeting.jpg'
        },
        {
            title: 'EUR/USD Breaks Above 1.0900 on ECB Hawkish Comments',
            summary: 'European Central Bank officials hint at prolonged restrictive monetary policy stance.',
            category: 'Currency Markets',
            time: '4 hours ago',
            impact: 'Medium',
            image: '/images/news/ecb-policy.jpg'
        },
        {
            title: 'Gold Prices Surge to $2,050 on Safe-Haven Demand',
            summary: 'Geopolitical tensions and inflation concerns drive investors toward precious metals.',
            category: 'Commodities',
            time: '6 hours ago',
            impact: 'High',
            image: '/images/news/gold-surge.jpg'
        },
        {
            title: 'UK GDP Growth Exceeds Expectations at 0.3%',
            summary: 'British economy shows resilience despite ongoing challenges, supporting GBP strength.',
            category: 'Economic Data',
            time: '8 hours ago',
            impact: 'Medium',
            image: '/images/news/uk-gdp.jpg'
        },
        {
            title: 'Japanese Yen Weakens as BoJ Maintains Ultra-Loose Policy',
            summary: 'Bank of Japan keeps negative interest rates, widening yield differential with other major currencies.',
            category: 'Central Banks',
            time: '12 hours ago',
            impact: 'High',
            image: '/images/news/boj-policy.jpg'
        },
        {
            title: 'Oil Prices Rally on OPEC+ Production Cut Extension',
            summary: 'OPEC+ members agree to extend production cuts through Q2 2024, supporting crude prices.',
            category: 'Commodities',
            time: '1 day ago',
            impact: 'Medium',
            image: '/images/news/opec-cuts.jpg'
        }
    ]

    const impactColors: { [key: string]: string } = {
        'High': 'bg-[#EA5455] text-white',
        'Medium': 'bg-orange-500 text-white',
        'Low': 'bg-[#28C76F] text-white'
    }

    const categoryColors: { [key: string]: string } = {
        'Central Banks': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        'Currency Markets': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        'Commodities': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        'Economic Data': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
    }

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl md:text-6xl font-bold mb-6">
                                Market News
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Stay informed with real-time financial news and expert market analysis
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                className="bg-[#EA5455] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#d63384] transition-colors duration-200"
                            >
                                Start Trading Now
                            </button>
                        </motion.div>
                    </div>
                </section>

                {/* News Features */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                News Features
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Comprehensive financial news coverage with expert analysis and trading insights
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {newsFeatures.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center mb-6">
                                        <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                            <feature.icon className="w-6 h-6 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {feature.title}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {feature.description}
                                    </p>
                                    <ul className="space-y-2">
                                        {feature.features.map((item, itemIndex) => (
                                            <li key={itemIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                {item}
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Latest News */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Latest Market News
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Breaking news and analysis from global financial markets
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {latestNews.map((article, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden hover:shadow-lg transition-shadow duration-300"
                                >
                                    <div className="h-48 bg-gradient-to-br from-[#EA5455] to-[#d63384] flex items-center justify-center">
                                        <TbNews className="w-16 h-16 text-white/50" />
                                    </div>
                                    <div className="p-6">
                                        <div className="flex items-center justify-between mb-4">
                                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${categoryColors[article.category] || 'bg-gray-100 text-gray-800'}`}>
                                                {article.category}
                                            </span>
                                            <span className={`px-2 py-1 rounded text-xs font-medium ${impactColors[article.impact] || 'bg-gray-500 text-white'}`}>
                                                {article.impact}
                                            </span>
                                        </div>
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3 line-clamp-2">
                                            {article.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                                            {article.summary}
                                        </p>
                                        <div className="flex items-center justify-between">
                                            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                                <TbClock className="w-4 h-4 mr-1" />
                                                {article.time}
                                            </span>
                                            <button className="text-[#EA5455] hover:text-[#d63384] text-sm font-medium transition-colors duration-200">
                                                Read More
                                            </button>
                                        </div>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Market Sentiment */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-gradient-to-r from-[#1E1E1E] to-gray-800 rounded-2xl p-8 md:p-12 text-white"
                        >
                            <div className="text-center mb-8">
                                <h2 className="text-3xl md:text-4xl font-bold mb-4">
                                    Current Market Sentiment
                                </h2>
                                <p className="text-xl text-gray-300">
                                    Real-time analysis of market mood and trader positioning
                                </p>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="text-center">
                                    <TbTrendingUp className="w-12 h-12 mx-auto mb-4 text-[#28C76F]" />
                                    <h3 className="text-xl font-bold mb-2 text-[#28C76F]">Risk-On</h3>
                                    <p className="text-gray-300">
                                        Investors showing appetite for higher-risk assets
                                    </p>
                                </div>
                                <div className="text-center">
                                    <TbChartLine className="w-12 h-12 mx-auto mb-4 text-orange-400" />
                                    <h3 className="text-xl font-bold mb-2 text-orange-400">Mixed Signals</h3>
                                    <p className="text-gray-300">
                                        Markets showing conflicting signals across asset classes
                                    </p>
                                </div>
                                <div className="text-center">
                                    <TbTarget className="w-12 h-12 mx-auto mb-4 text-[#EA5455]" />
                                    <h3 className="text-xl font-bold mb-2 text-[#EA5455]">Volatility Alert</h3>
                                    <p className="text-gray-300">
                                        Increased volatility expected due to upcoming events
                                    </p>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-20 bg-[#EA5455]">
                    <div className="max-w-7xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                Trade with Market Intelligence
                            </h2>
                            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                Stay ahead of the markets with our comprehensive news coverage and analysis
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                Open Trading Account
                            </button>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PublicPageLayout>
    )
}

export default TradingNewsPage
