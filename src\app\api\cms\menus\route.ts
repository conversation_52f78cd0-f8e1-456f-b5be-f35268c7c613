import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

// GET /api/cms/menus - Get all menus (with tree structure)
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const active = searchParams.get('active')
        const tree = searchParams.get('tree') // if true, return as tree structure
        
        let query = 'SELECT * FROM menus'
        const params: any[] = []
        
        if (active) {
            query += ' WHERE is_active = ?'
            params.push(active === 'true')
        }
        
        query += ' ORDER BY `order`, id'
        
        const [rows] = await pool.execute<RowDataPacket[]>(query, params)
        
        // If tree structure is requested, convert flat list to tree
        if (tree === 'true') {
            const map: { [key: number]: any } = {}
            const treeData: any[] = []
            
            // Create map of all items
            for (const row of rows) {
                map[row.id] = { ...row, children: [] }
            }
            
            // Build tree structure
            for (const row of rows) {
                if (row.parent_id && map[row.parent_id]) {
                    map[row.parent_id].children.push(map[row.id])
                } else {
                    treeData.push(map[row.id])
                }
            }
            
            return NextResponse.json({
                success: true,
                data: treeData
            })
        }
        
        return NextResponse.json({
            success: true,
            data: rows
        })
    } catch (error) {
        console.error('Error fetching menus:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch menus' },
            { status: 500 }
        )
    }
}

// POST /api/cms/menus - Create new menu item
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { label, slug, parent_id, icon, order = 0, is_active = true } = body
        
        if (!label || !slug) {
            return NextResponse.json(
                { success: false, error: 'Label and slug are required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO menus (label, slug, parent_id, icon, `order`, is_active) VALUES (?, ?, ?, ?, ?, ?)',
            [label, slug, parent_id, icon, order, is_active]
        )
        
        return NextResponse.json({
            success: true,
            data: { id: result.insertId, label, slug, parent_id, icon, order, is_active }
        }, { status: 201 })
    } catch (error) {
        console.error('Error creating menu item:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to create menu item' },
            { status: 500 }
        )
    }
}

// PUT /api/cms/menus - Update menu item
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { id, label, slug, parent_id, icon, order, is_active } = body
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Menu item ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE menus SET label = ?, slug = ?, parent_id = ?, icon = ?, `order` = ?, is_active = ? WHERE id = ?',
            [label, slug, parent_id, icon, order, is_active, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Menu item not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { id, label, slug, parent_id, icon, order, is_active }
        })
    } catch (error) {
        console.error('Error updating menu item:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to update menu item' },
            { status: 500 }
        )
    }
}

// DELETE /api/cms/menus - Delete menu item
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Menu item ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM menus WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Menu item not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Menu item deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting menu item:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete menu item' },
            { status: 500 }
        )
    }
}
