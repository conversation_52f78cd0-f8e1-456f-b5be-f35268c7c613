const AnalysisCenterPage = () => {
  return (
    <div className="mt-20">
      <main className="px-4 py-12 max-w-5xl mx-auto text-base bg-white dark:bg-gray-900">
        <h1 className="text-3xl font-bold mb-6">Analysis Center</h1>
        <p className="mb-4">Get the latest technical and fundamental analysis, plus a live economic calendar to inform your trading decisions.</p>
        {/* TODO: Integrate Technical Analysis, Fundamental Analysis, and Economic Calendar using existing components */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Technical Analysis</h2>
          <div className="h-32 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center text-gray-400">Technical Analysis Coming Soon</div>
        </div>
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Fundamental Analysis</h2>
          <div className="h-32 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center text-gray-400">Fundamental Analysis Coming Soon</div>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">Economic Calendar</h2>
          <div className="h-32 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center text-gray-400">Economic Calendar Coming Soon</div>
        </div>
      </main>
    </div>
  );
};

export default AnalysisCenterPage;