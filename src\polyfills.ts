// Minimal polyfills for browser compatibility (client-side only)

// Client-side polyfills for browser environment
// This ensures all browser APIs are available

// Only run polyfills on the client side to avoid SSR issues
if (typeof window !== 'undefined') {
    // Define self if not available
    if (typeof self === 'undefined') {
        (window as any).self = window
    }

    // Ensure globalThis is available
    if (typeof globalThis === 'undefined') {
        (window as any).globalThis = window
    }

    // Polyfill for requestAnimationFrame if not available
    if (!window.requestAnimationFrame) {
        window.requestAnimationFrame = (callback) => {
            return setTimeout(callback, 16);
        };
    }

    // Polyfill for cancelAnimationFrame if not available
    if (!window.cancelAnimationFrame) {
        window.cancelAnimationFrame = (id) => {
            clearTimeout(id);
        };
    }

    // Ensure ResizeObserver is available
    if (!window.ResizeObserver) {
        (window as any).ResizeObserver = class ResizeObserver {
            constructor(callback: any) {
                this.callback = callback;
            }
            observe() {}
            unobserve() {}
            disconnect() {}
            private callback: any;
        };
    }

    // Ensure IntersectionObserver is available
    if (!window.IntersectionObserver) {
        (window as any).IntersectionObserver = class IntersectionObserver {
            constructor(callback: any) {
                this.callback = callback;
            }
            observe() {}
            unobserve() {}
            disconnect() {}
            private callback: any;
        };
    }

    // Clean up browser extension attributes that cause hydration issues
    const cleanup = () => {
        try {
            const elements = document.querySelectorAll('[bis_skin_checked]')
            elements.forEach(el => el.removeAttribute('bis_skin_checked'))
        } catch (e) {
            // Ignore errors during cleanup
        }
    }

    // Run cleanup after DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', cleanup)
    } else {
        cleanup()
    }

    // Also run cleanup on window load as a fallback
    window.addEventListener('load', cleanup)

    // Periodic cleanup for dynamic content
    setInterval(cleanup, 5000)
}
