'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

const AffiliatePortalPage = () => {
    const router = useRouter()

    useEffect(() => {
        // Redirect to the sign-in page as requested
        router.push('/sign-in')
    }, [router])

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#EA5455] mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-300">Redirecting to sign-in...</p>
            </div>
        </div>
    )
}

export default AffiliatePortalPage
