'use client'

import { Card } from '@/components/ui/Card'
import { Progress } from '@/components/ui/Progress'
import { ExternalLinkIcon } from 'lucide-react'
import Image from 'next/image'
import type { TopChannelData } from '../types'

interface TopChannelProps {
    data: {
        topChannel: TopChannelData
    }
}

const TopChannel = ({ data }: TopChannelProps) => {
    return (
        <Card 
            header={{ content: `Top Traffic Channels (${data.topChannel.visitors.toLocaleString()} total visitors)` }}
            className="p-6"
        >
            <div className="space-y-6">
                {data.topChannel.channels.map((channel, index) => (
                    <div key={channel.id} className="space-y-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <div className="relative w-8 h-8">
                                    <Image
                                        src={channel.img}
                                        alt={channel.name}
                                        width={32}
                                        height={32}
                                        className="w-8 h-8 rounded-lg object-cover"
                                        onError={() => {
                                            // Fallback handled by Next.js Image component
                                        }}
                                    />
                                </div>
                                <div>
                                    <div className="flex items-center gap-2">
                                        <span className="font-medium">{channel.name}</span>
                                        <ExternalLinkIcon className="h-3 w-3 text-muted-foreground" />
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                        {channel.total.toLocaleString()} visitors
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="font-semibold text-lg">{channel.percentage}%</p>
                                <p className="text-sm text-muted-foreground">of total</p>
                            </div>
                        </div>
                        
                        <Progress
                            percent={channel.percentage}
                            className="h-2"
                            style={{
                                '--progress-background': index === 0 ? '#EA5455' :
                                                       index === 1 ? '#28C76F' :
                                                       index === 2 ? '#2a85ff' : '#6c757d'
                            } as React.CSSProperties}
                        />
                        
                        <div className="flex justify-between text-sm text-muted-foreground">
                            <span>Rank #{index + 1}</span>
                            <span>
                                {((channel.total / data.topChannel.visitors) * 100).toFixed(1)}% conversion
                            </span>
                        </div>
                    </div>
                ))}
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                        <p className="text-2xl font-bold text-green-600">
                            {data.topChannel.channels.reduce((sum, channel) => sum + channel.total, 0).toLocaleString()}
                        </p>
                        <p className="text-sm text-muted-foreground">Total Channel Traffic</p>
                    </div>
                    <div>
                        <p className="text-2xl font-bold text-blue-600">
                            {data.topChannel.channels.length}
                        </p>
                        <p className="text-sm text-muted-foreground">Active Channels</p>
                    </div>
                </div>
            </div>
        </Card>
    )
}

export default TopChannel
