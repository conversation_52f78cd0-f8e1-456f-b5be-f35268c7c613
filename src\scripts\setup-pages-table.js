const mysql = require('mysql2/promise');

async function setupPagesTable() {
    let connection;
    
    try {
        // Create connection
        connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'mybrokerforex'
        });

        console.log('Connected to MySQL database');

        // Create pages table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS pages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) UNIQUE NOT NULL,
                content LONGTEXT,
                seo_meta JSON,
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        console.log('Pages table created successfully');

        // Insert sample pages
        const samplePages = [
            {
                title: 'Home',
                slug: 'home',
                content: '<h1>Welcome to MyBrokerForex</h1><p>Your trusted partner in forex trading with competitive spreads and professional support.</p>',
                seo_meta: JSON.stringify({
                    title: 'MyBrokerForex - Professional Forex Trading Platform',
                    description: 'Trade forex with confidence on our professional platform. Competitive spreads, advanced tools, and expert support.',
                    keywords: 'forex, trading, broker, currency, financial markets'
                }),
                status: 'published'
            },
            {
                title: 'About Us',
                slug: 'about',
                content: '<h1>About MyBrokerForex</h1><p>We are a leading forex broker committed to providing exceptional trading experiences.</p>',
                seo_meta: JSON.stringify({
                    title: 'About MyBrokerForex - Leading Forex Broker',
                    description: 'Learn about our company, mission, and commitment to providing exceptional forex trading services.',
                    keywords: 'about, company, forex broker, trading services'
                }),
                status: 'published'
            },
            {
                title: 'Account Types',
                slug: 'account-types',
                content: '<h1>Account Types</h1><p>Choose from our range of account types designed to meet your trading needs.</p>',
                seo_meta: JSON.stringify({
                    title: 'Forex Account Types - MyBrokerForex',
                    description: 'Explore our different account types with varying features, spreads, and minimum deposits.',
                    keywords: 'account types, forex accounts, trading accounts'
                }),
                status: 'published'
            },
            {
                title: 'Education Center',
                slug: 'education-center',
                content: '<h1>Education Center</h1><p>Enhance your trading knowledge with our comprehensive educational resources.</p>',
                seo_meta: JSON.stringify({
                    title: 'Forex Education Center - Learn Trading',
                    description: 'Access free educational resources, tutorials, and guides to improve your forex trading skills.',
                    keywords: 'forex education, trading tutorials, learning resources'
                }),
                status: 'published'
            },
            {
                title: 'Support',
                slug: 'support',
                content: '<h1>Customer Support</h1><p>Get help when you need it with our 24/7 customer support team.</p>',
                seo_meta: JSON.stringify({
                    title: 'Customer Support - MyBrokerForex',
                    description: 'Contact our support team for assistance with your trading account and platform.',
                    keywords: 'customer support, help, contact, assistance'
                }),
                status: 'published'
            },
            {
                title: 'Privacy Policy',
                slug: 'privacy-policy',
                content: '<h1>Privacy Policy</h1><p>Your privacy is important to us. Learn how we protect your personal information.</p>',
                seo_meta: JSON.stringify({
                    title: 'Privacy Policy - MyBrokerForex',
                    description: 'Read our privacy policy to understand how we collect, use, and protect your personal data.',
                    keywords: 'privacy policy, data protection, personal information'
                }),
                status: 'published'
            },
            {
                title: 'Terms of Service',
                slug: 'terms-of-service',
                content: '<h1>Terms of Service</h1><p>Please read our terms of service before using our platform.</p>',
                seo_meta: JSON.stringify({
                    title: 'Terms of Service - MyBrokerForex',
                    description: 'Read our terms of service and user agreement for using our trading platform.',
                    keywords: 'terms of service, user agreement, legal terms'
                }),
                status: 'published'
            }
        ];

        for (const page of samplePages) {
            await connection.execute(
                "INSERT IGNORE INTO pages (title, slug, content, seo_meta, status) VALUES (?, ?, ?, ?, ?)",
                [page.title, page.slug, page.content, page.seo_meta, page.status]
            );
        }

        console.log('Sample pages inserted successfully');
        console.log('Pages table setup completed!');

    } catch (error) {
        console.error('Error setting up pages table:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

setupPagesTable();
