'use client'

import { motion } from 'framer-motion'
import { TbMail, TbPhone, TbMessageCircle, TbBook, Tb<PERSON>lock, TbHeadset } from 'react-icons/tb'
import PageLayout from '@/components/layout/PageLayout'

const AffiliateSupportPage = () => {
    const supportChannels = [
        {
            icon: TbMail,
            title: 'Email Support',
            description: 'Get detailed help via email from our affiliate specialists',
            contact: '<EMAIL>',
            responseTime: '24 hours',
            availability: '24/7'
        },
        {
            icon: TbMessageCircle,
            title: 'Live Chat',
            description: 'Instant support through our live chat system',
            contact: 'Available in affiliate dashboard',
            responseTime: 'Instant',
            availability: 'Mon-Fri 9AM-6PM GMT'
        },
        {
            icon: TbPhone,
            title: 'Phone Support',
            description: 'Speak directly with our affiliate support team',
            contact: '******-MBFX-AFF',
            responseTime: 'Immediate',
            availability: 'Mon-Fri 9AM-6PM GMT'
        }
    ]

    const supportCategories = [
        {
            icon: TbBook,
            title: 'Account Setup',
            description: 'Help with registration, verification, and account configuration',
            topics: ['Registration assistance', 'Document verification', 'Account approval', 'Dashboard access']
        },
        {
            icon: TbHeadset,
            title: 'Marketing Support',
            description: 'Guidance on marketing strategies and promotional materials',
            topics: ['Marketing materials', 'Campaign optimization', 'Compliance guidelines', 'Best practices']
        },
        {
            icon: TbClock,
            title: 'Technical Issues',
            description: 'Technical support for tracking, links, and platform issues',
            topics: ['Tracking problems', 'Link generation', 'API integration', 'Dashboard issues']
        }
    ]

    const quickLinks = [
        { title: 'Getting Started Guide', description: 'Complete guide for new affiliates', link: '/affiliate/getting-started' },
        { title: 'FAQ', description: 'Answers to frequently asked questions', link: '/affiliate/faq' },
        { title: 'Marketing Materials', description: 'Download promotional content', link: '/affiliate/materials' },
        { title: 'API Documentation', description: 'Technical integration guide', link: '/affiliate/api' },
        { title: 'Commission Structure', description: 'Understand your earning potential', link: '/affiliate/commissions' },
        { title: 'Terms & Conditions', description: 'Program terms and guidelines', link: '/affiliate/terms' }
    ]

    return (
        <PageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                Affiliate Support
                            </h1>
                            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Get the help you need to succeed as a MyBrokerForex affiliate
                            </p>
                        </motion.div>
                    </div>
                </section>

                {/* Support Channels */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Contact Our Support Team
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Multiple ways to get the support you need, when you need it
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {supportChannels.map((channel, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455]/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <channel.icon className="w-8 h-8 text-[#EA5455]" />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        {channel.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {channel.description}
                                    </p>
                                    <div className="space-y-3">
                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                            <div className="text-sm text-gray-500 dark:text-gray-400">Contact</div>
                                            <div className="font-semibold text-gray-900 dark:text-white">{channel.contact}</div>
                                        </div>
                                        <div className="grid grid-cols-2 gap-3">
                                            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                                <div className="text-sm text-gray-500 dark:text-gray-400">Response</div>
                                                <div className="font-semibold text-gray-900 dark:text-white">{channel.responseTime}</div>
                                            </div>
                                            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                                <div className="text-sm text-gray-500 dark:text-gray-400">Available</div>
                                                <div className="font-semibold text-gray-900 dark:text-white">{channel.availability}</div>
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Support Categories */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                What We Can Help With
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Our support team specializes in these key areas
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {supportCategories.map((category, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-8"
                                >
                                    <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mb-6">
                                        <category.icon className="w-6 h-6 text-[#EA5455]" />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        {category.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        {category.description}
                                    </p>
                                    <ul className="space-y-2">
                                        {category.topics.map((topic, topicIndex) => (
                                            <li key={topicIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                <div className="w-2 h-2 bg-[#EA5455] rounded-full mr-3"></div>
                                                {topic}
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Quick Links */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Self-Service Resources
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                Find answers quickly with our comprehensive resource library
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {quickLinks.map((link, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                        {link.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                                        {link.description}
                                    </p>
                                    <button 
                                        onClick={() => window.location.href = link.link}
                                        className="text-[#EA5455] hover:text-[#d63384] font-medium transition-colors duration-200"
                                    >
                                        Learn More →
                                    </button>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Support Hours */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-4xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="bg-gradient-to-r from-[#EA5455] to-[#d63384] rounded-2xl p-8 text-white text-center"
                        >
                            <h2 className="text-3xl font-bold mb-4">
                                24/7 Email Support
                            </h2>
                            <p className="text-xl text-white/90 mb-6">
                                While our live chat and phone support have specific hours, our email support is available 24/7
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                                <div className="bg-white/20 rounded-xl p-4">
                                    <h3 className="font-bold mb-2">Live Support Hours</h3>
                                    <p className="text-white/90">Monday - Friday<br />9:00 AM - 6:00 PM GMT</p>
                                </div>
                                <div className="bg-white/20 rounded-xl p-4">
                                    <h3 className="font-bold mb-2">Email Support</h3>
                                    <p className="text-white/90">24/7 Availability<br />Response within 24 hours</p>
                                </div>
                            </div>
                            <button 
                                onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-colors duration-200"
                            >
                                Contact Support Now
                            </button>
                        </motion.div>
                    </div>
                </section>

                {/* Emergency Contact */}
                <section className="py-20">
                    <div className="max-w-4xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                        >
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                                Need Urgent Help?
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                For urgent issues that can't wait, contact our emergency support line
                            </p>
                            <div className="bg-gray-100 dark:bg-gray-800 rounded-xl p-6 inline-block">
                                <div className="text-2xl font-bold text-[#EA5455] mb-2">
                                    Emergency Support
                                </div>
                                <div className="text-lg text-gray-900 dark:text-white">
                                    ******-MBFX-911
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                    Available 24/7 for critical issues only
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-16 bg-[#EA5455]">
                    <div className="max-w-7xl mx-auto px-6 text-center">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                        >
                            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                Ready to Get Started?
                            </h2>
                            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                Join our affiliate program and get access to dedicated support
                            </p>
                            <button 
                                onClick={() => window.open('https://mbf.mybrokerforex.com/affiliate/register', '_blank')}
                                className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                Join Affiliate Program
                            </button>
                        </motion.div>
                    </div>
                </section>
            </div>
        </PageLayout>
    )
}

export default AffiliateSupportPage
