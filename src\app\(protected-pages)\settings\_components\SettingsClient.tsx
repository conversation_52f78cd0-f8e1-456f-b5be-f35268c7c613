'use client'

import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Textarea } from '@/components/ui/Textarea'
import { Switch } from '@/components/ui/Switch'
import Tabs from '@/components/ui/Tabs'
import HydrationSafeWrapper from '@/components/shared/HydrationSafeWrapper'
import { SaveIcon, SettingsIcon, GlobeIcon, MailIcon, ShieldIcon, UploadIcon } from 'lucide-react'
import { useState, useEffect } from 'react'

const SettingsClient = () => {
    // Client-side hydration safety
    const [isClient, setIsClient] = useState(false)
    const [isMounted, setIsMounted] = useState(false)

    const [logoFile, setLogoFile] = useState<File | null>(null)
    const [logoPreview, setLogoPreview] = useState<string>('')
    const [currentLogo, setCurrentLogo] = useState<string>('')
    const [isUploading, setIsUploading] = useState(false)
    const [uploadMessage, setUploadMessage] = useState('')

    // Dark mode logo states
    const [darkLogoFile, setDarkLogoFile] = useState<File | null>(null)
    const [darkLogoPreview, setDarkLogoPreview] = useState<string>('')
    const [currentDarkLogo, setCurrentDarkLogo] = useState<string>('')
    const [isDarkLogoUploading, setIsDarkLogoUploading] = useState(false)
    const [darkLogoUploadMessage, setDarkLogoUploadMessage] = useState('')

    // Favicon states
    const [faviconFile, setFaviconFile] = useState<File | null>(null)
    const [faviconPreview, setFaviconPreview] = useState<string>('')
    const [currentFavicon, setCurrentFavicon] = useState<string>('')
    const [isFaviconUploading, setIsFaviconUploading] = useState(false)
    const [faviconUploadMessage, setFaviconUploadMessage] = useState('')

    // SEO Settings states
    const [seoSettings, setSeoSettings] = useState({
        site_title: '',
        site_description: '',
        meta_keywords: '',
        google_analytics_id: '',
        google_search_console: '',
        robots_txt: '',
        sitemap_enabled: true
    })

    // Email Settings states
    const [emailSettings, setEmailSettings] = useState({
        smtp_host: '',
        smtp_port: '587',
        smtp_username: '',
        smtp_password: '',
        smtp_encryption: 'tls',
        from_email: '',
        from_name: '',
        test_email: ''
    })

    // Security Settings states
    const [securitySettings, setSecuritySettings] = useState({
        two_factor_enabled: false,
        session_timeout: '30',
        max_login_attempts: '5',
        password_min_length: '8',
        require_special_chars: true,
        ip_whitelist: '',
        maintenance_mode: false
    })

    const [isSaving, setIsSaving] = useState(false)
    const [saveMessage, setSaveMessage] = useState('')

    // Set client-side flag to prevent hydration mismatch
    useEffect(() => {
        setIsClient(true)
        setIsMounted(true)
    }, [])

    // Fetch current logo, favicon, and all settings on component mount
    useEffect(() => {
        if (!isClient) return

        const fetchCurrentAssets = async () => {
            try {
                // Fetch current logo
                const logoResponse = await fetch('/api/settings/logo')
                const logoData = await logoResponse.json()
                if (logoData.success && logoData.logoPath) {
                    setCurrentLogo(logoData.logoPath)
                }

                // Fetch current favicon
                const faviconResponse = await fetch('/api/settings/favicon')
                const faviconData = await faviconResponse.json()
                if (faviconData.success && faviconData.faviconPath) {
                    setCurrentFavicon(faviconData.faviconPath)
                }

                // Fetch current dark logo
                const darkLogoResponse = await fetch('/api/settings/dark-logo')
                const darkLogoData = await darkLogoResponse.json()
                if (darkLogoData.darkLogoPath) {
                    setCurrentDarkLogo(darkLogoData.darkLogoPath)
                }

                // Fetch all settings
                const settingsResponse = await fetch('/api/settings')
                const settingsData = await settingsResponse.json()
                if (settingsData.success && settingsData.data) {
                    const settings = settingsData.data

                    // Parse SEO settings
                    const seoData: any = { ...seoSettings }
                    settings.forEach((setting: any) => {
                        if (setting.setting_key.startsWith('seo_')) {
                            const key = setting.setting_key.replace('seo_', '')
                            if (key in seoData) {
                                seoData[key] = setting.setting_value
                            }
                        }
                    })
                    setSeoSettings(seoData)

                    // Parse Email settings
                    const emailData = { ...emailSettings }
                    settings.forEach((setting: any) => {
                        if (setting.setting_key.startsWith('email_')) {
                            const key = setting.setting_key.replace('email_', '')
                            if (key in emailData) {
                                emailData[key as keyof typeof emailData] = setting.setting_value
                            }
                        }
                    })
                    setEmailSettings(emailData)

                    // Parse Security settings
                    const securityData: any = { ...securitySettings }
                    settings.forEach((setting: any) => {
                        if (setting.setting_key.startsWith('security_')) {
                            const key = setting.setting_key.replace('security_', '')
                            if (key in securityData) {
                                securityData[key] = setting.setting_value
                            }
                        }
                    })
                    setSecuritySettings(securityData)
                }
            } catch (error) {
                console.error('Error fetching current assets:', error)
            }
        }

        fetchCurrentAssets()
    }, [isClient])

    const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file) {
            setLogoFile(file)
            const reader = new FileReader()
            reader.onload = (e) => {
                setLogoPreview(e.target?.result as string)
            }
            reader.readAsDataURL(file)
        }
    }

    const handleFaviconUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file) {
            setFaviconFile(file)
            const reader = new FileReader()
            reader.onload = (e) => {
                setFaviconPreview(e.target?.result as string)
            }
            reader.readAsDataURL(file)
        }
    }

    const handleSaveLogo = async () => {
        if (!logoFile) return

        setIsUploading(true)
        setUploadMessage('')

        try {
            const formData = new FormData()
            formData.append('logo', logoFile)

            const response = await fetch('/api/settings/logo', {
                method: 'POST',
                body: formData,
            })

            const result = await response.json()

            if (result.success) {
                setUploadMessage('Logo uploaded successfully!')
                setCurrentLogo(result.logoPath)
                // Clear the file input
                setLogoFile(null)
                setLogoPreview('')
                // Trigger a page refresh to update logo across the site
                setTimeout(() => {
                    if (typeof window !== 'undefined') {
                        window.location.reload()
                    }
                }, 1000)
            } else {
                setUploadMessage(result.error || 'Failed to upload logo')
            }
        } catch (error) {
            setUploadMessage('Error uploading logo')
            console.error('Upload error:', error)
        } finally {
            setIsUploading(false)
        }
    }

    const handleSaveFavicon = async () => {
        if (!faviconFile) return

        setIsFaviconUploading(true)
        setFaviconUploadMessage('')

        try {
            const formData = new FormData()
            formData.append('favicon', faviconFile)

            const response = await fetch('/api/settings/favicon', {
                method: 'POST',
                body: formData,
            })

            const result = await response.json()

            if (result.success) {
                setFaviconUploadMessage('Favicon uploaded successfully!')
                setCurrentFavicon(result.faviconPath)
                // Clear the file input
                setFaviconFile(null)
                setFaviconPreview('')
                // Update favicon in the document head safely
                try {
                    const existingLink = document.querySelector("link[rel*='icon']") as HTMLLinkElement
                    if (existingLink) {
                        existingLink.href = result.faviconPath
                    } else {
                        const link = document.createElement('link')
                        link.type = 'image/x-icon'
                        link.rel = 'shortcut icon'
                        link.href = result.faviconPath
                        document.head.appendChild(link)
                    }
                } catch (error) {
                    console.error('Error updating favicon in DOM:', error)
                }
            } else {
                setFaviconUploadMessage(result.error || 'Failed to upload favicon')
            }
        } catch (error) {
            setFaviconUploadMessage('Error uploading favicon')
            console.error('Favicon upload error:', error)
        } finally {
            setIsFaviconUploading(false)
        }
    }

    const handleDarkLogoFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (file) {
            setDarkLogoFile(file)
            const reader = new FileReader()
            reader.onload = (e) => {
                setDarkLogoPreview(e.target?.result as string)
            }
            reader.readAsDataURL(file)
        }
    }

    const handleSaveDarkLogo = async () => {
        if (!darkLogoFile) return

        setIsDarkLogoUploading(true)
        setDarkLogoUploadMessage('')

        try {
            const formData = new FormData()
            formData.append('darkLogo', darkLogoFile)

            const response = await fetch('/api/settings/dark-logo', {
                method: 'POST',
                body: formData,
            })

            const result = await response.json()

            if (result.success) {
                setDarkLogoUploadMessage('Dark logo uploaded successfully!')
                setCurrentDarkLogo(result.darkLogoPath)
                // Clear the file input
                setDarkLogoFile(null)
                setDarkLogoPreview('')
                // Reload page to reflect changes
                setTimeout(() => {
                    if (typeof window !== 'undefined') {
                        window.location.reload()
                    }
                }, 1000)
            } else {
                setDarkLogoUploadMessage(result.error || 'Failed to upload dark logo')
            }
        } catch (error) {
            setDarkLogoUploadMessage('Error uploading dark logo')
            console.error('Dark logo upload error:', error)
        } finally {
            setIsDarkLogoUploading(false)
        }
    }

    // Save SEO Settings
    const handleSaveSeoSettings = async () => {
        setIsSaving(true)
        setSaveMessage('')

        try {
            const promises = Object.entries(seoSettings).map(([key, value]) =>
                fetch('/api/settings', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        setting_key: `seo_${key}`,
                        setting_value: value,
                        setting_type: typeof value === 'boolean' ? 'boolean' : 'text',
                        description: `SEO setting: ${key}`
                    })
                })
            )

            const responses = await Promise.all(promises)
            const allSuccessful = responses.every(response => response.ok)

            if (allSuccessful) {
                setSaveMessage('SEO settings saved successfully!')
            } else {
                setSaveMessage('Some SEO settings failed to save')
            }
        } catch (error) {
            setSaveMessage('Error saving SEO settings')
            console.error('SEO save error:', error)
        } finally {
            setIsSaving(false)
        }
    }

    // Save Email Settings
    const handleSaveEmailSettings = async () => {
        setIsSaving(true)
        setSaveMessage('')

        try {
            const promises = Object.entries(emailSettings).map(([key, value]) =>
                fetch('/api/settings', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        setting_key: `email_${key}`,
                        setting_value: value,
                        setting_type: 'text',
                        description: `Email setting: ${key}`
                    })
                })
            )

            const responses = await Promise.all(promises)
            const allSuccessful = responses.every(response => response.ok)

            if (allSuccessful) {
                setSaveMessage('Email settings saved successfully!')
            } else {
                setSaveMessage('Some email settings failed to save')
            }
        } catch (error) {
            setSaveMessage('Error saving email settings')
            console.error('Email save error:', error)
        } finally {
            setIsSaving(false)
        }
    }

    // Save Security Settings
    const handleSaveSecuritySettings = async () => {
        setIsSaving(true)
        setSaveMessage('')

        try {
            const promises = Object.entries(securitySettings).map(([key, value]) =>
                fetch('/api/settings', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        setting_key: `security_${key}`,
                        setting_value: value,
                        setting_type: typeof value === 'boolean' ? 'boolean' : 'text',
                        description: `Security setting: ${key}`
                    })
                })
            )

            const responses = await Promise.all(promises)
            const allSuccessful = responses.every(response => response.ok)

            if (allSuccessful) {
                setSaveMessage('Security settings saved successfully!')
            } else {
                setSaveMessage('Some security settings failed to save')
            }
        } catch (error) {
            setSaveMessage('Error saving security settings')
            console.error('Security save error:', error)
        } finally {
            setIsSaving(false)
        }
    }

    // Test email function
    const handleTestEmail = async () => {
        if (!emailSettings.test_email) {
            setSaveMessage('Please enter a test email address')
            return
        }

        setIsSaving(true)
        setSaveMessage('Sending test email...')

        try {
            const response = await fetch('/api/settings/test-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    to: emailSettings.test_email,
                    settings: emailSettings
                })
            })

            const result = await response.json()
            if (result.success) {
                setSaveMessage('Test email sent successfully!')
            } else {
                setSaveMessage(result.error || 'Failed to send test email')
            }
        } catch (error) {
            setSaveMessage('Error sending test email')
            console.error('Test email error:', error)
        } finally {
            setIsSaving(false)
        }
    }

    // Show loading state during hydration
    if (!isClient || !isMounted) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Site Settings</h1>
                        <p className="text-muted-foreground">
                            Manage general site configuration and settings
                        </p>
                    </div>
                </div>
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Site Settings</h1>
                    <p className="text-muted-foreground">
                        Manage general site configuration and settings
                    </p>
                </div>
                <Button icon={<SaveIcon />}>
                    <span>Save All Changes</span>
                </Button>
            </div>

            <div suppressHydrationWarning>
                <Tabs defaultValue="general" className="space-y-6">
                    <div suppressHydrationWarning>
                        <Tabs.TabList>
                            <Tabs.TabNav value="general">General</Tabs.TabNav>
                            <Tabs.TabNav value="branding">Branding</Tabs.TabNav>
                            <Tabs.TabNav value="seo">SEO</Tabs.TabNav>
                            <Tabs.TabNav value="email">Email</Tabs.TabNav>
                            <Tabs.TabNav value="security">Security</Tabs.TabNav>
                        </Tabs.TabList>
                    </div>

                <Tabs.TabContent value="general" className="space-y-6">
                    <Card 
                        header={{ 
                            content: (
                                <div className="flex items-center gap-2">
                                    <GlobeIcon className="h-5 w-5" />
                                    Site Information
                                </div>
                            )
                        }}
                        className="p-6"
                    >
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="site-name">Site Name</Label>
                                    <Input 
                                        id="site-name" 
                                        defaultValue="MyBrokerForex" 
                                        placeholder="Enter site name"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="site-url">Site URL</Label>
                                    <Input 
                                        id="site-url" 
                                        defaultValue="https://mybrokerforex.com" 
                                        placeholder="Enter site URL"
                                    />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="site-description">Site Description</Label>
                                <Textarea 
                                    id="site-description" 
                                    defaultValue="Your trusted partner in forex trading with competitive spreads and professional support."
                                    placeholder="Enter site description"
                                    rows={3}
                                />
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="admin-email">Admin Email</Label>
                                    <Input 
                                        id="admin-email" 
                                        type="email"
                                        defaultValue="<EMAIL>" 
                                        placeholder="Enter admin email"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="support-email">Support Email</Label>
                                    <Input 
                                        id="support-email" 
                                        type="email"
                                        defaultValue="<EMAIL>" 
                                        placeholder="Enter support email"
                                    />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card 
                        header={{ content: "Site Features" }}
                        className="p-6"
                    >
                        <div className="space-y-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label>User Registration</Label>
                                    <p className="text-sm text-muted-foreground">Allow new users to register accounts</p>
                                </div>
                                <Switch defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label>Email Verification</Label>
                                    <p className="text-sm text-muted-foreground">Require email verification for new accounts</p>
                                </div>
                                <Switch defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label>Maintenance Mode</Label>
                                    <p className="text-sm text-muted-foreground">Put site in maintenance mode</p>
                                </div>
                                <Switch />
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="space-y-0.5">
                                    <Label>Analytics Tracking</Label>
                                    <p className="text-sm text-muted-foreground">Enable Google Analytics tracking</p>
                                </div>
                                <Switch defaultChecked />
                            </div>
                        </div>
                    </Card>
                </Tabs.TabContent>

                <Tabs.TabContent value="branding" className="space-y-6">
                    <Card 
                        header={{ 
                            content: (
                                <div className="flex items-center gap-2">
                                    <UploadIcon className="h-5 w-5" />
                                    Logo Management
                                </div>
                            )
                        }}
                        className="p-6"
                    >
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                {/* Light Mode Logo */}
                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="logo-upload">Light Mode Logo</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Used in light theme. Supported formats: PNG, JPG, SVG. Recommended size: 200x60px
                                        </p>
                                    </div>
                                    <Input
                                        id="logo-upload"
                                        type="file"
                                        accept=".png,.jpg,.jpeg,.svg"
                                        onChange={handleLogoUpload}
                                    />
                                    {logoPreview && (
                                        <div className="space-y-2">
                                            <Label>Preview:</Label>
                                            <div className="border rounded-lg p-4 bg-gray-50">
                                                <img
                                                    src={logoPreview}
                                                    alt="Logo preview"
                                                    className="max-h-16 object-contain"
                                                />
                                            </div>
                                            <Button
                                                onClick={handleSaveLogo}
                                                className="w-full"
                                                disabled={isUploading}
                                                icon={<SaveIcon />}
                                            >
                                                <span>{isUploading ? 'Uploading...' : 'Save Light Logo'}</span>
                                            </Button>
                                            {uploadMessage && (
                                                <div className={`text-sm mt-2 ${uploadMessage.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
                                                    {uploadMessage}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                    <div className="space-y-2">
                                        <Label>Current Light Logo:</Label>
                                        <div className="border rounded-lg p-4 bg-gray-50">
                                            {currentLogo ? (
                                                <img
                                                    src={currentLogo}
                                                    alt="Current light logo"
                                                    className="max-h-16 object-contain"
                                                />
                                            ) : (
                                                <div className="text-center text-gray-500 py-4">
                                                    No light logo uploaded yet
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Dark Mode Logo */}
                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="dark-logo-upload">Dark Mode Logo</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Used in dark theme. Supported formats: PNG, JPG, SVG. Recommended size: 200x60px
                                        </p>
                                    </div>
                                    <Input
                                        id="dark-logo-upload"
                                        type="file"
                                        accept=".png,.jpg,.jpeg,.svg"
                                        onChange={handleDarkLogoFileChange}
                                    />
                                    {darkLogoPreview && (
                                        <div className="space-y-2">
                                            <Label>Preview:</Label>
                                            <div className="border rounded-lg p-4 bg-gray-800">
                                                <img
                                                    src={darkLogoPreview}
                                                    alt="Dark logo preview"
                                                    className="max-h-16 object-contain"
                                                />
                                            </div>
                                            <Button
                                                onClick={handleSaveDarkLogo}
                                                className="w-full"
                                                disabled={isDarkLogoUploading}
                                                icon={<SaveIcon />}
                                            >
                                                <span>{isDarkLogoUploading ? 'Uploading...' : 'Save Dark Logo'}</span>
                                            </Button>
                                            {darkLogoUploadMessage && (
                                                <div className={`text-sm mt-2 ${darkLogoUploadMessage.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
                                                    {darkLogoUploadMessage}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                    <div className="space-y-2">
                                        <Label>Current Dark Logo:</Label>
                                        <div className="border rounded-lg p-4 bg-gray-800">
                                            {currentDarkLogo ? (
                                                <img
                                                    src={currentDarkLogo}
                                                    alt="Current dark logo"
                                                    className="max-h-16 object-contain"
                                                />
                                            ) : (
                                                <div className="text-center text-gray-400 py-4">
                                                    No dark logo uploaded yet
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card
                        header={{
                            content: (
                                <div className="flex items-center gap-2">
                                    <UploadIcon className="h-5 w-5" />
                                    Favicon Management
                                </div>
                            )
                        }}
                        className="p-6"
                    >
                        <div className="space-y-6">
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="favicon-upload">Upload New Favicon</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Supported formats: ICO, PNG. Recommended size: 32x32px or 16x16px
                                    </p>
                                </div>
                                <Input
                                    id="favicon-upload"
                                    type="file"
                                    accept=".ico,.png"
                                    onChange={handleFaviconUpload}
                                />
                                {faviconPreview && (
                                    <div className="space-y-2">
                                        <Label>Preview:</Label>
                                        <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                                            <img
                                                src={faviconPreview}
                                                alt="Favicon preview"
                                                className="w-8 h-8 object-contain"
                                            />
                                        </div>
                                        <Button
                                            onClick={handleSaveFavicon}
                                            className="w-full"
                                            disabled={isFaviconUploading}
                                            icon={<SaveIcon />}
                                        >
                                            <span>{isFaviconUploading ? 'Uploading...' : 'Save Favicon'}</span>
                                        </Button>
                                        {faviconUploadMessage && (
                                            <div className={`text-sm mt-2 ${faviconUploadMessage.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
                                                {faviconUploadMessage}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Label>Current Favicon:</Label>
                                <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                                    {currentFavicon ? (
                                        <img
                                            src={currentFavicon}
                                            alt="Current favicon"
                                            className="w-8 h-8 object-contain"
                                        />
                                    ) : (
                                        <div className="text-center text-gray-500 py-4">
                                            No favicon uploaded yet
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </Card>
                </Tabs.TabContent>

                {/* SEO Settings Tab */}
                <Tabs.TabContent value="seo" className="space-y-6">
                    <Card
                        header={{
                            content: (
                                <div className="flex items-center gap-2">
                                    <GlobeIcon className="h-5 w-5" />
                                    SEO Configuration
                                </div>
                            )
                        }}
                        className="p-6"
                    >
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="site_title">Site Title</Label>
                                    <Input
                                        id="site_title"
                                        value={seoSettings.site_title}
                                        onChange={(e) => setSeoSettings(prev => ({ ...prev, site_title: e.target.value }))}
                                        placeholder="MyBrokerForex - Professional Forex Trading"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="google_analytics_id">Google Analytics ID</Label>
                                    <Input
                                        id="google_analytics_id"
                                        value={seoSettings.google_analytics_id}
                                        onChange={(e) => setSeoSettings(prev => ({ ...prev, google_analytics_id: e.target.value }))}
                                        placeholder="G-XXXXXXXXXX"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="site_description">Site Description</Label>
                                <Textarea
                                    id="site_description"
                                    value={seoSettings.site_description}
                                    onChange={(e) => setSeoSettings(prev => ({ ...prev, site_description: e.target.value }))}
                                    placeholder="Professional forex trading platform with competitive spreads..."
                                    rows={3}
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="meta_keywords">Meta Keywords</Label>
                                <Input
                                    id="meta_keywords"
                                    value={seoSettings.meta_keywords}
                                    onChange={(e) => setSeoSettings(prev => ({ ...prev, meta_keywords: e.target.value }))}
                                    placeholder="forex, trading, broker, currency, investment"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="google_search_console">Google Search Console</Label>
                                <Input
                                    id="google_search_console"
                                    value={seoSettings.google_search_console}
                                    onChange={(e) => setSeoSettings(prev => ({ ...prev, google_search_console: e.target.value }))}
                                    placeholder="google-site-verification=..."
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="robots_txt">Robots.txt Content</Label>
                                <Textarea
                                    id="robots_txt"
                                    value={seoSettings.robots_txt}
                                    onChange={(e) => setSeoSettings(prev => ({ ...prev, robots_txt: e.target.value }))}
                                    placeholder="User-agent: *&#10;Allow: /"
                                    rows={4}
                                />
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch
                                    checked={seoSettings.sitemap_enabled}
                                    onChange={(checked) => setSeoSettings(prev => ({ ...prev, sitemap_enabled: checked }))}
                                />
                                <Label>Enable XML Sitemap Generation</Label>
                            </div>

                            <Button onClick={handleSaveSeoSettings} disabled={isSaving} className="w-full" icon={<SaveIcon />}>
                                <span>{isSaving ? 'Saving...' : 'Save SEO Settings'}</span>
                            </Button>

                            {saveMessage && (
                                <div className={`text-sm ${saveMessage.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
                                    {saveMessage}
                                </div>
                            )}
                        </div>
                    </Card>
                </Tabs.TabContent>

                {/* Email Settings Tab */}
                <Tabs.TabContent value="email" className="space-y-6">
                    <Card
                        header={{
                            content: (
                                <div className="flex items-center gap-2">
                                    <MailIcon className="h-5 w-5" />
                                    Email Configuration
                                </div>
                            )
                        }}
                        className="p-6"
                    >
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="smtp_host">SMTP Host</Label>
                                    <Input
                                        id="smtp_host"
                                        value={emailSettings.smtp_host}
                                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_host: e.target.value }))}
                                        placeholder="smtp.gmail.com"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="smtp_port">SMTP Port</Label>
                                    <Input
                                        id="smtp_port"
                                        value={emailSettings.smtp_port}
                                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_port: e.target.value }))}
                                        placeholder="587"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="smtp_username">SMTP Username</Label>
                                    <Input
                                        id="smtp_username"
                                        value={emailSettings.smtp_username}
                                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_username: e.target.value }))}
                                        placeholder="<EMAIL>"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="smtp_password">SMTP Password</Label>
                                    <Input
                                        id="smtp_password"
                                        type="password"
                                        value={emailSettings.smtp_password}
                                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_password: e.target.value }))}
                                        placeholder="••••••••"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="from_email">From Email</Label>
                                    <Input
                                        id="from_email"
                                        value={emailSettings.from_email}
                                        onChange={(e) => setEmailSettings(prev => ({ ...prev, from_email: e.target.value }))}
                                        placeholder="<EMAIL>"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="from_name">From Name</Label>
                                    <Input
                                        id="from_name"
                                        value={emailSettings.from_name}
                                        onChange={(e) => setEmailSettings(prev => ({ ...prev, from_name: e.target.value }))}
                                        placeholder="MyBrokerForex"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="smtp_encryption">Encryption</Label>
                                <select
                                    id="smtp_encryption"
                                    value={emailSettings.smtp_encryption}
                                    onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_encryption: e.target.value }))}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="tls">TLS</option>
                                    <option value="ssl">SSL</option>
                                    <option value="none">None</option>
                                </select>
                            </div>

                            <div className="border-t pt-6">
                                <h3 className="text-lg font-semibold mb-4">Test Email Configuration</h3>
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="test_email">Test Email Address</Label>
                                        <Input
                                            id="test_email"
                                            type="email"
                                            value={emailSettings.test_email}
                                            onChange={(e) => setEmailSettings(prev => ({ ...prev, test_email: e.target.value }))}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                    <div className="flex gap-4">
                                        <Button onClick={handleTestEmail} disabled={isSaving} variant="plain" icon={<MailIcon />}>
                                            <span>Send Test Email</span>
                                        </Button>
                                        <Button onClick={handleSaveEmailSettings} disabled={isSaving} icon={<SaveIcon />}>
                                            <span>{isSaving ? 'Saving...' : 'Save Email Settings'}</span>
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            {saveMessage && (
                                <div className={`text-sm ${saveMessage.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
                                    {saveMessage}
                                </div>
                            )}
                        </div>
                    </Card>
                </Tabs.TabContent>

                {/* Security Settings Tab */}
                <Tabs.TabContent value="security" className="space-y-6">
                    <Card
                        header={{
                            content: (
                                <div className="flex items-center gap-2">
                                    <ShieldIcon className="h-5 w-5" />
                                    Security Configuration
                                </div>
                            )
                        }}
                        className="p-6"
                    >
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="session_timeout">Session Timeout (minutes)</Label>
                                    <Input
                                        id="session_timeout"
                                        type="number"
                                        value={securitySettings.session_timeout}
                                        onChange={(e) => setSecuritySettings(prev => ({ ...prev, session_timeout: e.target.value }))}
                                        placeholder="30"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="max_login_attempts">Max Login Attempts</Label>
                                    <Input
                                        id="max_login_attempts"
                                        type="number"
                                        value={securitySettings.max_login_attempts}
                                        onChange={(e) => setSecuritySettings(prev => ({ ...prev, max_login_attempts: e.target.value }))}
                                        placeholder="5"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="password_min_length">Minimum Password Length</Label>
                                <Input
                                    id="password_min_length"
                                    type="number"
                                    value={securitySettings.password_min_length}
                                    onChange={(e) => setSecuritySettings(prev => ({ ...prev, password_min_length: e.target.value }))}
                                    placeholder="8"
                                />
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        checked={securitySettings.two_factor_enabled}
                                        onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, two_factor_enabled: checked }))}
                                    />
                                    <Label>Enable Two-Factor Authentication</Label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch
                                        checked={securitySettings.require_special_chars}
                                        onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, require_special_chars: checked }))}
                                    />
                                    <Label>Require Special Characters in Passwords</Label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch
                                        checked={securitySettings.maintenance_mode}
                                        onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, maintenance_mode: checked }))}
                                    />
                                    <Label>Maintenance Mode</Label>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="ip_whitelist">IP Whitelist (one per line)</Label>
                                <Textarea
                                    id="ip_whitelist"
                                    value={securitySettings.ip_whitelist}
                                    onChange={(e) => setSecuritySettings(prev => ({ ...prev, ip_whitelist: e.target.value }))}
                                    placeholder="***********&#10;********"
                                    rows={4}
                                />
                                <p className="text-sm text-muted-foreground">
                                    Leave empty to allow all IPs. Add trusted IP addresses for admin access.
                                </p>
                            </div>

                            <Button onClick={handleSaveSecuritySettings} disabled={isSaving} className="w-full" icon={<SaveIcon />}>
                                <span>{isSaving ? 'Saving...' : 'Save Security Settings'}</span>
                            </Button>

                            {saveMessage && (
                                <div className={`text-sm ${saveMessage.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
                                    {saveMessage}
                                </div>
                            )}
                        </div>
                    </Card>
                </Tabs.TabContent>
                </Tabs>
            </div>
        </div>
    )
}

export default SettingsClient
