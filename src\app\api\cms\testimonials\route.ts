import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'
import cache, { CacheKeys, CacheTTL, invalidateCache } from '@/utils/cache'

// GET /api/cms/testimonials - Get all testimonials
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const featured = searchParams.get('featured')
        const active = searchParams.get('active')

        // Check cache first
        const cacheKey = CacheKeys.testimonials(active === 'true', featured === 'true')
        const cachedData = cache.get(cacheKey)

        if (cachedData) {
            return NextResponse.json({
                success: true,
                data: cachedData,
                cached: true
            })
        }

        let query = 'SELECT * FROM testimonials WHERE 1=1'
        const params: any[] = []

        if (featured) {
            query += ' AND is_featured = ?'
            params.push(featured === 'true')
        }

        if (active) {
            query += ' AND is_active = ?'
            params.push(active === 'true')
        }

        query += ' ORDER BY created_at DESC'

        const [rows] = await pool.execute<RowDataPacket[]>(query, params)

        // Cache the result for 15 minutes
        cache.set(cacheKey, rows, CacheTTL.LONG)

        return NextResponse.json({
            success: true,
            data: rows
        })
    } catch (error) {
        console.error('Error fetching testimonials:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch testimonials' },
            { status: 500 }
        )
    }
}

// POST /api/cms/testimonials - Create new testimonial
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            client_name, 
            quote, 
            image, 
            rating = 5, 
            company, 
            position, 
            is_featured = false, 
            is_active = true 
        } = body
        
        if (!client_name || !quote) {
            return NextResponse.json(
                { success: false, error: 'Client name and quote are required' },
                { status: 400 }
            )
        }
        
        // Validate rating
        if (rating < 1 || rating > 5) {
            return NextResponse.json(
                { success: false, error: 'Rating must be between 1 and 5' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO testimonials (client_name, quote, image, rating, company, position, is_featured, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [client_name, quote, image, rating, company, position, is_featured, is_active]
        )
        
        return NextResponse.json({
            success: true,
            data: { 
                id: result.insertId, 
                client_name, 
                quote, 
                image, 
                rating, 
                company, 
                position, 
                is_featured, 
                is_active 
            }
        }, { status: 201 })
    } catch (error) {
        console.error('Error creating testimonial:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to create testimonial' },
            { status: 500 }
        )
    }
}

// PUT /api/cms/testimonials - Update testimonial
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            id, 
            client_name, 
            quote, 
            image, 
            rating, 
            company, 
            position, 
            is_featured, 
            is_active 
        } = body
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Testimonial ID is required' },
                { status: 400 }
            )
        }
        
        // Validate rating if provided
        if (rating && (rating < 1 || rating > 5)) {
            return NextResponse.json(
                { success: false, error: 'Rating must be between 1 and 5' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE testimonials SET client_name = ?, quote = ?, image = ?, rating = ?, company = ?, position = ?, is_featured = ?, is_active = ? WHERE id = ?',
            [client_name, quote, image, rating, company, position, is_featured, is_active, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Testimonial not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { id, client_name, quote, image, rating, company, position, is_featured, is_active }
        })
    } catch (error) {
        console.error('Error updating testimonial:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to update testimonial' },
            { status: 500 }
        )
    }
}

// DELETE /api/cms/testimonials - Delete testimonial
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Testimonial ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM testimonials WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Testimonial not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Testimonial deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting testimonial:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete testimonial' },
            { status: 500 }
        )
    }
}
