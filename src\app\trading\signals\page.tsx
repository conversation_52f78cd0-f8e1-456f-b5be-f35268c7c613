'use client'

import { motion } from 'framer-motion'
import { TbBell, TbTrendingUp, TbTrendingDown, TbClock, TbTarget, TbChartLine } from 'react-icons/tb'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

const TradingSignalsPage = () => {
    const signalFeatures = [
        {
            icon: TbBell,
            title: 'Real-Time Alerts',
            description: 'Receive instant notifications when trading opportunities arise in the market.',
            features: ['SMS Alerts', 'Email Notifications', 'Push Notifications', 'In-App Alerts']
        },
        {
            icon: TbChartLine,
            title: 'Technical Analysis',
            description: 'Signals based on advanced technical analysis and proven trading strategies.',
            features: ['Chart Patterns', 'Technical Indicators', 'Price Action', 'Trend Analysis']
        },
        {
            icon: TbTarget,
            title: 'Entry & Exit Points',
            description: 'Clear entry points, stop loss, and take profit levels for each signal.',
            features: ['Entry Price', 'Stop Loss', 'Take Profit', 'Risk Management']
        },
        {
            icon: Tb<PERSON><PERSON>,
            title: '24/7 Monitoring',
            description: 'Continuous market monitoring across all major currency pairs and timeframes.',
            features: ['Major Pairs', 'Minor Pairs', 'Exotic Pairs', 'Multiple Timeframes']
        }
    ]

    const recentSignals = [
        {
            pair: 'EUR/USD',
            type: 'BUY',
            entry: '1.0875',
            stopLoss: '1.0825',
            takeProfit: '1.0950',
            status: 'Active',
            time: '15 minutes ago',
            pips: '+25'
        },
        {
            pair: 'GBP/USD',
            type: 'SELL',
            entry: '1.2650',
            stopLoss: '1.2700',
            takeProfit: '1.2575',
            status: 'Closed',
            time: '2 hours ago',
            pips: '+75'
        },
        {
            pair: 'USD/JPY',
            type: 'BUY',
            entry: '149.25',
            stopLoss: '148.75',
            takeProfit: '150.00',
            status: 'Active',
            time: '4 hours ago',
            pips: '+15'
        },
        {
            pair: 'AUD/USD',
            type: 'SELL',
            entry: '0.6525',
            stopLoss: '0.6575',
            takeProfit: '0.6450',
            status: 'Closed',
            time: '1 day ago',
            pips: '+75'
        }
    ]

    const signalStats = [
        { label: 'Success Rate', value: '87%', color: 'text-[#28C76F]' },
        { label: 'Average Pips', value: '65', color: 'text-[#EA5455]' },
        { label: 'Signals Today', value: '12', color: 'text-blue-500' },
        { label: 'Active Signals', value: '5', color: 'text-orange-500' }
    ]

    return (
        <div className="mt-0">
            <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Trading Signals
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Professional trading signals with high accuracy rates and detailed analysis
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-[#EA5455] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#d63384] transition-colors duration-200"
                                >
                                    Get Trading Signals
                                </button>
                            </motion.div>
                        </div>
                    </section>

                    {/* Signal Stats */}
                    <section className="py-16 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                                {signalStats.map((stat, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="text-center"
                                    >
                                        <div className={`text-3xl md:text-4xl font-bold mb-2 ${stat.color}`}>
                                            {stat.value}
                                        </div>
                                        <div className="text-gray-600 dark:text-gray-300 font-medium">
                                            {stat.label}
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Signal Features */}
                    <section className="py-20">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Signal Features
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Advanced trading signals with comprehensive analysis and risk management
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {signalFeatures.map((feature, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                                    >
                                        <div className="flex items-center mb-6">
                                            <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mr-4">
                                                <feature.icon className="w-6 h-6 text-[#EA5455]" />
                                            </div>
                                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                                {feature.title}
                                            </h3>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                                            {feature.description}
                                        </p>
                                        <ul className="space-y-2">
                                            {feature.features.map((item, itemIndex) => (
                                                <li key={itemIndex} className="flex items-center text-gray-600 dark:text-gray-300">
                                                    <TbTarget className="w-4 h-4 text-[#28C76F] mr-2" />
                                                    {item}
                                                </li>
                                            ))}
                                        </ul>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Recent Signals */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Recent Signals
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Track our latest trading signals and their performance
                                </p>
                            </motion.div>

                            <div className="overflow-x-auto">
                                <table className="w-full bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden">
                                    <thead className="bg-gray-100 dark:bg-gray-600">
                                        <tr>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Pair</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Type</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Entry</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">S/L</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">T/P</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Status</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Pips</th>
                                            <th className="px-6 py-4 text-left text-gray-900 dark:text-white font-semibold">Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {recentSignals.map((signal, index) => (
                                            <motion.tr
                                                key={index}
                                                initial={{ opacity: 0, y: 20 }}
                                                whileInView={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.4, delay: index * 0.1 }}
                                                className="border-b border-gray-200 dark:border-gray-600"
                                            >
                                                <td className="px-6 py-4 font-semibold text-gray-900 dark:text-white">
                                                    {signal.pair}
                                                </td>
                                                <td className="px-6 py-4">
                                                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                                        signal.type === 'BUY' 
                                                            ? 'bg-[#28C76F] text-white' 
                                                            : 'bg-[#EA5455] text-white'
                                                    }`}>
                                                        {signal.type}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 text-gray-600 dark:text-gray-300">{signal.entry}</td>
                                                <td className="px-6 py-4 text-gray-600 dark:text-gray-300">{signal.stopLoss}</td>
                                                <td className="px-6 py-4 text-gray-600 dark:text-gray-300">{signal.takeProfit}</td>
                                                <td className="px-6 py-4">
                                                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                                        signal.status === 'Active' 
                                                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' 
                                                            : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                                                    }`}>
                                                        {signal.status}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 font-semibold text-[#28C76F]">{signal.pips}</td>
                                                <td className="px-6 py-4 text-gray-500 dark:text-gray-400 text-sm">{signal.time}</td>
                                            </motion.tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-[#EA5455]">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Start Receiving Professional Trading Signals
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                                    Join thousands of traders who trust our signals for profitable trading
                                </p>
                                <button 
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="bg-white text-[#EA5455] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-200"
                                >
                                    Open Trading Account
                                </button>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
        </div>
    )
}

export default TradingSignalsPage
