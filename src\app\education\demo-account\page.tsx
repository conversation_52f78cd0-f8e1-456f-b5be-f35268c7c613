'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbShield, TbTrendingUp, TbTarget, TbClock, TbUsers, TbChartLine, TbSettings, TbBrain } from 'react-icons/tb'

const DemoAccountPage = () => {
    const demoFeatures = [
        {
            icon: TbShield,
            title: 'Risk-Free Trading',
            description: 'Practice with virtual money without risking your real capital.',
            benefits: ['No financial risk', 'Learn without pressure', 'Test strategies safely', 'Build confidence']
        },
        {
            icon: TbChartLine,
            title: 'Real Market Conditions',
            description: 'Experience live market data and real-time price movements.',
            benefits: ['Live market prices', 'Real-time execution', 'Actual market volatility', 'Authentic trading experience']
        },
        {
            icon: TbSettings,
            title: 'Full Platform Access',
            description: 'Access all trading tools and features available in live accounts.',
            benefits: ['Complete platform features', 'All trading instruments', 'Advanced charting tools', 'Technical indicators']
        },
        {
            icon: TbBrain,
            title: 'Strategy Testing',
            description: 'Test and refine your trading strategies before going live.',
            benefits: ['Strategy validation', 'Performance tracking', 'Risk assessment', 'Optimization opportunities']
        }
    ]

    const demoSpecs = [
        { feature: 'Virtual Balance', value: '$100,000', description: 'Start with substantial virtual capital' },
        { feature: 'Account Duration', value: '30 Days', description: 'Renewable demo account period' },
        { feature: 'Currency Pairs', value: '50+', description: 'Major, minor, and exotic pairs' },
        { feature: 'Leverage', value: 'Up to 1:500', description: 'Same leverage as live accounts' },
        { feature: 'Minimum Trade', value: '0.01 lots', description: 'Micro lot trading available' },
        { feature: 'Spreads', value: 'From 0.1 pips', description: 'Competitive live spreads' }
    ]

    const learningPath = [
        {
            step: '01',
            title: 'Account Setup',
            description: 'Create your demo account and familiarize yourself with the platform.',
            duration: '15 minutes',
            tasks: ['Register demo account', 'Download platform', 'Explore interface', 'Set up charts']
        },
        {
            step: '02',
            title: 'Basic Trading',
            description: 'Execute your first trades and understand order types.',
            duration: '30 minutes',
            tasks: ['Place market orders', 'Set stop losses', 'Use take profits', 'Monitor positions']
        },
        {
            step: '03',
            title: 'Strategy Development',
            description: 'Develop and test your trading strategies.',
            duration: '2-4 weeks',
            tasks: ['Create trading plan', 'Test strategies', 'Track performance', 'Refine approach']
        },
        {
            step: '04',
            title: 'Advanced Features',
            description: 'Master advanced tools and risk management.',
            duration: '1-2 weeks',
            tasks: ['Use advanced orders', 'Apply indicators', 'Manage portfolio', 'Analyze results']
        }
    ]

    const commonMistakes = [
        {
            mistake: 'Treating Demo Like a Game',
            solution: 'Trade with the same discipline as you would with real money.',
            impact: 'High'
        },
        {
            mistake: 'Over-leveraging Positions',
            solution: 'Use realistic position sizes that match your actual capital.',
            impact: 'High'
        },
        {
            mistake: 'Ignoring Risk Management',
            solution: 'Practice proper stop losses and position sizing from the start.',
            impact: 'Critical'
        },
        {
            mistake: 'Not Keeping Trading Records',
            solution: 'Maintain a trading journal to track performance and learn.',
            impact: 'Medium'
        },
        {
            mistake: 'Rushing to Live Trading',
            solution: 'Ensure consistent profitability before switching to real money.',
            impact: 'High'
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Demo Account
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                    Practice trading risk-free with virtual money and real market conditions
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-[#28C76F] hover:bg-[#28C76F]/90 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors duration-300"
                                    >
                                        Open Demo Account
                                    </button>
                                    <button 
                                        className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-[#1E1E1E] transition-colors duration-300"
                                    >
                                        Learn More
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Demo Features Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Why Use a Demo Account?
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Demo accounts provide the perfect environment to learn, practice, and perfect your trading skills.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                {demoFeatures.map((feature, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start space-x-4">
                                            <div className="w-12 h-12 bg-[#28C76F]/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <feature.icon className="w-6 h-6 text-[#28C76F]" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                                    {feature.title}
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                                    {feature.description}
                                                </p>
                                                <ul className="space-y-2">
                                                    {feature.benefits.map((benefit, benefitIndex) => (
                                                        <li key={benefitIndex} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                            <div className="w-1.5 h-1.5 bg-[#28C76F] rounded-full"></div>
                                                            <span>{benefit}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Demo Specifications Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Demo Account Specifications
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Our demo accounts mirror live trading conditions to provide authentic practice experience.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {demoSpecs.map((spec, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                                            {spec.feature}
                                        </h3>
                                        <div className="text-2xl font-bold text-[#28C76F] mb-3">
                                            {spec.value}
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {spec.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Learning Path Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Demo Trading Learning Path
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Follow this structured approach to maximize your demo trading experience.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                {learningPath.map((step, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="text-center mb-4">
                                            <div className="w-12 h-12 bg-[#EA5455] text-white rounded-full flex items-center justify-center mx-auto mb-3 text-lg font-bold">
                                                {step.step}
                                            </div>
                                            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                                                {step.title}
                                            </h3>
                                            <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                                                {step.description}
                                            </p>
                                            <span className="inline-block px-3 py-1 bg-[#28C76F]/10 text-[#28C76F] rounded-full text-xs font-medium">
                                                {step.duration}
                                            </span>
                                        </div>

                                        <div className="space-y-2">
                                            {step.tasks.map((task, taskIndex) => (
                                                <div key={taskIndex} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                                    <div className="w-1.5 h-1.5 bg-[#EA5455] rounded-full flex-shrink-0"></div>
                                                    <span>{task}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Common Mistakes Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Avoid Common Demo Trading Mistakes
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Learn from these common pitfalls to make your demo trading more effective.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {commonMistakes.map((item, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <div className="flex items-start justify-between mb-4">
                                            <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                                                {item.mistake}
                                            </h3>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                item.impact === 'Critical'
                                                    ? 'bg-[#EA5455]/10 text-[#EA5455]'
                                                    : item.impact === 'High'
                                                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                                                    : 'bg-[#28C76F]/10 text-[#28C76F]'
                                            }`}>
                                                {item.impact}
                                            </span>
                                        </div>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            <strong>Solution:</strong> {item.solution}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#28C76F] to-[#28C76F]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Start Your Risk-Free Trading Journey
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Open your demo account today and begin practicing with $100,000 virtual capital.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-white text-[#28C76F] px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Open Demo Account
                                    </button>
                                    <button
                                        onClick={() => window.open('/education/trading-basics', '_blank')}
                                        className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-[#28C76F] transition-colors duration-300"
                                    >
                                        Learn Trading Basics
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default DemoAccountPage
