interface CacheItem<T> {
    data: T
    timestamp: number
    ttl: number
}

class SimpleCache {
    private cache = new Map<string, CacheItem<any>>()
    private defaultTTL = 5 * 60 * 1000 // 5 minutes default

    set<T>(key: string, data: T, ttl?: number): void {
        const item: CacheItem<T> = {
            data,
            timestamp: Date.now(),
            ttl: ttl || this.defaultTTL
        }
        this.cache.set(key, item)
    }

    get<T>(key: string): T | null {
        const item = this.cache.get(key)
        
        if (!item) {
            return null
        }

        // Check if item has expired
        if (Date.now() - item.timestamp > item.ttl) {
            this.cache.delete(key)
            return null
        }

        return item.data as T
    }

    delete(key: string): boolean {
        return this.cache.delete(key)
    }

    clear(): void {
        this.cache.clear()
    }

    // Clear expired items
    cleanup(): void {
        const now = Date.now()
        for (const [key, item] of this.cache.entries()) {
            if (now - item.timestamp > item.ttl) {
                this.cache.delete(key)
            }
        }
    }

    // Get cache stats
    getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        }
    }
}

// Create a singleton instance
const cache = new SimpleCache()

// Cleanup expired items every 10 minutes
setInterval(() => {
    cache.cleanup()
}, 10 * 60 * 1000)

export default cache

// Cache key generators for different types of content
export const CacheKeys = {
    heroSections: (active?: boolean) => `hero-sections${active ? '-active' : ''}`,
    testimonials: (active?: boolean, featured?: boolean) => 
        `testimonials${active ? '-active' : ''}${featured ? '-featured' : ''}`,
    posts: (status?: string, category?: string, featured?: boolean) => 
        `posts${status ? `-${status}` : ''}${category ? `-${category}` : ''}${featured ? '-featured' : ''}`,
    pages: (status?: string) => `pages${status ? `-${status}` : ''}`,
    settings: (type: string) => `settings-${type}`,
    partners: (type: 'ib' | 'affiliate', status?: string) => 
        `partners-${type}${status ? `-${status}` : ''}`,
    analytics: (period?: string) => `analytics${period ? `-${period}` : ''}`,
}

// Cache TTL constants (in milliseconds)
export const CacheTTL = {
    SHORT: 2 * 60 * 1000,      // 2 minutes
    MEDIUM: 5 * 60 * 1000,     // 5 minutes  
    LONG: 15 * 60 * 1000,      // 15 minutes
    VERY_LONG: 60 * 60 * 1000, // 1 hour
}

// Helper function to invalidate related cache entries
export const invalidateCache = (pattern: string) => {
    const keys = Array.from(cache.getStats().keys)
    keys.forEach(key => {
        if (key.includes(pattern)) {
            cache.delete(key)
        }
    })
}
