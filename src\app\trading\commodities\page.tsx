'use client'

import { motion } from 'framer-motion'
import { TbTrendingUp, TbWorld, TbShield, Tb<PERSON>lock, TbChartLine, TbCoin } from 'react-icons/tb'
import { HiArrowRight } from 'react-icons/hi2'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function CommoditiesPage() {
    const commodities = [
        {
            name: 'Gold',
            symbol: 'XAUUSD',
            price: '$2,045.30',
            change: '+1.2%',
            icon: '🥇'
        },
        {
            name: 'Silver',
            symbol: 'XAGUSD',
            price: '$24.85',
            change: '+0.8%',
            icon: '🥈'
        },
        {
            name: 'Crude Oil',
            symbol: 'USOIL',
            price: '$78.45',
            change: '-0.5%',
            icon: '🛢️'
        },
        {
            name: 'Natural Gas',
            symbol: 'NATGAS',
            price: '$2.89',
            change: '+2.1%',
            icon: '⛽'
        },
        {
            name: 'Copper',
            symbol: 'COPPER',
            price: '$8,245',
            change: '+1.5%',
            icon: '🔶'
        },
        {
            name: 'Platinum',
            symbol: 'XPTUSD',
            price: '$1,025.60',
            change: '-0.3%',
            icon: '⚪'
        }
    ]

    const features = [
        {
            icon: TbTrendingUp,
            title: 'Diversification',
            description: 'Diversify your portfolio with precious metals, energy, and agricultural commodities.'
        },
        {
            icon: TbWorld,
            title: 'Global Markets',
            description: 'Access major commodity exchanges worldwide with real-time pricing and execution.'
        },
        {
            icon: TbShield,
            title: 'Inflation Hedge',
            description: 'Protect your wealth against inflation with traditional safe-haven assets.'
        },
        {
            icon: TbClock,
            title: '24/7 Trading',
            description: 'Trade commodities around the clock during market hours across different time zones.'
        }
    ]

    const tradingBenefits = [
        'Low spreads on major commodities',
        'No commission on commodity CFDs',
        'Advanced charting and analysis tools',
        'Real-time market news and analysis',
        'Risk management tools',
        'Mobile and desktop platforms'
    ]

    return (
        <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
                {/* Hero Section */}
                <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center text-white"
                        >
                            <h1 className="text-5xl text-gray-300 font-bold mb-6 mt-8">
                                Commodities Trading
                            </h1>
                            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                                Trade CFDs on gold, oil, silver, and other major commodities. Diversify your portfolio 
                                with precious metals and energy markets from around the world.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button
                                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                    className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center"
                                >
                                    Start Trading Commodities
                                    <HiArrowRight className="ml-2 w-5 h-5" />
                                </button>
                                <button className="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-gray-900 transition-colors duration-200">
                                    Try Demo Account
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* Live Prices Section */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Live Commodity Prices
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Real-time pricing on major commodities
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {commodities.map((commodity, index) => (
                                <motion.div
                                    key={commodity.symbol}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                                >
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex items-center">
                                            <span className="text-2xl mr-3">{commodity.icon}</span>
                                            <div>
                                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                                    {commodity.name}
                                                </h3>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    {commodity.symbol}
                                                </p>
                                            </div>
                                        </div>
                                        <TbChartLine className="w-6 h-6 text-[#EA5455]" />
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                                            {commodity.price}
                                        </span>
                                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                            commodity.change.startsWith('+') 
                                                ? 'bg-[#28C76F] bg-opacity-10 text-[#28C76F]' 
                                                : 'bg-[#EA5455] bg-opacity-10 text-[#EA5455]'
                                        }`}>
                                            {commodity.change}
                                        </span>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Why Trade Commodities?
                            </h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">
                                Discover the benefits of commodity trading
                            </p>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 30 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 0.1 * index }}
                                    className="text-center"
                                >
                                    <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                        <feature.icon className="w-8 h-8 text-white" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Trading Benefits Section */}
                <section className="py-20">
                    <div className="max-w-7xl mx-auto px-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                            <motion.div
                                initial={{ opacity: 0, x: -30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Advanced Trading Platform
                                </h2>
                                <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                                    Trade commodities with our professional-grade platform featuring advanced 
                                    charting tools, real-time data, and seamless execution.
                                </p>
                                <ul className="space-y-4">
                                    {tradingBenefits.map((benefit, index) => (
                                        <li key={index} className="flex items-center">
                                            <div className="w-2 h-2 bg-[#EA5455] rounded-full mr-4"></div>
                                            <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                                        </li>
                                    ))}
                                </ul>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, x: 30 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg"
                            >
                                <div className="text-center">
                                    <TbCoin className="w-16 h-16 text-[#EA5455] mx-auto mb-6" />
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                        Start Trading Today
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                                        Open your account and start trading commodities with competitive spreads 
                                        and professional tools.
                                    </p>
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="w-full py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200"
                                    >
                                        Open Trading Account
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                </section>

                {/* Market Categories */}
                <section className="py-20 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto px-6">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                Commodity Categories
                            </h2>
                        </motion.div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 }}
                                className="bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl text-center"
                            >
                                <span className="text-4xl mb-4 block">🥇</span>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    Precious Metals
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Gold, Silver, Platinum, and Palladium
                                </p>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                                className="bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl text-center"
                            >
                                <span className="text-4xl mb-4 block">⚡</span>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    Energy
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Crude Oil, Natural Gas, and Heating Oil
                                </p>
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.3 }}
                                className="bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl text-center"
                            >
                                <span className="text-4xl mb-4 block">🌾</span>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    Agricultural
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Wheat, Corn, Soybeans, and Coffee
                                </p>
                            </motion.div>
                        </div>
                    </div>
                </section>
            </div>
        </PublicPageLayout>
    )
}
