import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import navigationConfig from '@/configs/navigation.config'

export async function GET(request: NextRequest) {
    try {
        // Get database navigation
        const [dbRows] = await pool.query(
            "SELECT id, label, slug, parent_id, icon, `order` FROM menus ORDER BY `order`, id"
        );
        
        // Get static navigation
        const staticNav = navigationConfig;
        
        return NextResponse.json({
            success: true,
            data: {
                databaseNavigation: dbRows,
                staticNavigation: staticNav.slice(0, 3), // First 3 items for comparison
                databaseCount: Array.isArray(dbRows) ? dbRows.length : 0,
                staticCount: staticNav.length
            }
        })
    } catch (error) {
        return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : String(error),
            staticNavigation: navigationConfig.slice(0, 3)
        })
    }
}
