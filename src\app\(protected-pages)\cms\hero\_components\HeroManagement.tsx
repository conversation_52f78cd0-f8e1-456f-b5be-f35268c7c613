'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { PlusIcon, EditIcon, TrashIcon, ToggleLeftIcon, ToggleRightIcon } from 'lucide-react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

interface HeroSection {
    id: number
    title: string
    subtitle: string
    video_url: string
    cta_link: string
    cta_text: string
    image: string
    background_type: 'image' | 'video' | 'gradient'
    is_active: boolean
    created_at: string
    updated_at: string
}

const HeroManagement = () => {
    const [heroSections, setHeroSections] = useState<HeroSection[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    // Fetch hero sections from API
    const fetchHeroSections = async () => {
        try {
            setLoading(true)
            const response = await fetch('/api/cms/hero')
            const data = await response.json()
            
            if (data.success) {
                setHeroSections(data.data)
            } else {
                setError(data.error || 'Failed to fetch hero sections')
            }
        } catch (err) {
            setError('Failed to fetch hero sections')
            console.error('Error fetching hero sections:', err)
        } finally {
            setLoading(false)
        }
    }

    // Toggle hero section active status
    const toggleActive = async (id: number, currentStatus: boolean) => {
        try {
            const response = await fetch('/api/cms/hero', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id,
                    is_active: !currentStatus
                })
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Hero section {!currentStatus ? 'activated' : 'deactivated'}
                    </Notification>
                )
                fetchHeroSections() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to update hero section'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to update hero section
                </Notification>
            )
            console.error('Error updating hero section:', err)
        }
    }

    // Delete hero section
    const deleteHeroSection = async (id: number) => {
        if (!confirm('Are you sure you want to delete this hero section?')) {
            return
        }

        try {
            const response = await fetch(`/api/cms/hero?id=${id}`, {
                method: 'DELETE'
            })
            const data = await response.json()
            
            if (data.success) {
                toast.push(
                    <Notification type="success" title="Success">
                        Hero section deleted successfully
                    </Notification>
                )
                fetchHeroSections() // Refresh the list
            } else {
                toast.push(
                    <Notification type="danger" title="Error">
                        {data.error || 'Failed to delete hero section'}
                    </Notification>
                )
            }
        } catch (err) {
            toast.push(
                <Notification type="danger" title="Error">
                    Failed to delete hero section
                </Notification>
            )
            console.error('Error deleting hero section:', err)
        }
    }

    // Get background type badge color
    const getBackgroundTypeColor = (type: string) => {
        switch (type) {
            case 'image':
                return 'bg-blue-100 text-blue-800'
            case 'video':
                return 'bg-purple-100 text-purple-800'
            case 'gradient':
                return 'bg-green-100 text-green-800'
            default:
                return 'bg-gray-100 text-gray-800'
        }
    }

    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    }

    useEffect(() => {
        fetchHeroSections()
    }, [])

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Hero Sections</h1>
                        <p className="text-muted-foreground">
                            Manage homepage hero banners
                        </p>
                    </div>
                </div>
                <Card className="p-6">
                    <div className="text-center">Loading hero sections...</div>
                </Card>
            </div>
        )
    }

    if (error) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Hero Sections</h1>
                        <p className="text-muted-foreground">
                            Manage homepage hero banners
                        </p>
                    </div>
                </div>
                <Card className="p-6">
                    <div className="text-center text-red-600">
                        Error: {error}
                        <br />
                        <Button onClick={fetchHeroSections} className="mt-4">
                            Retry
                        </Button>
                    </div>
                </Card>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Hero Sections</h1>
                    <p className="text-muted-foreground">
                        Manage homepage hero banners
                    </p>
                </div>
                <Button className="flex items-center gap-2">
                    <PlusIcon className="h-4 w-4" />
                    Add New Hero Section
                </Button>
            </div>

            <Card
                header={{
                    content: <h3 className="text-lg font-semibold">All Hero Sections ({heroSections.length})</h3>
                }}
            >
                    {heroSections.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-muted-foreground">No hero sections found</p>
                            <Button className="mt-4">
                                <PlusIcon className="h-4 w-4 mr-2" />
                                Create Your First Hero Section
                            </Button>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {heroSections.map((hero) => (
                                <div key={hero.id} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-1">
                                            <h3 className="font-semibold">{hero.title}</h3>
                                            <Badge className={getBackgroundTypeColor(hero.background_type)}>
                                                {hero.background_type}
                                            </Badge>
                                            <Badge className={hero.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                                                {hero.is_active ? 'Active' : 'Inactive'}
                                            </Badge>
                                        </div>
                                        <p className="text-sm text-muted-foreground mb-1">
                                            {hero.subtitle}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            Last updated: {formatDate(hero.updated_at)}
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="plain"
                                            size="sm"
                                            title={hero.is_active ? 'Deactivate' : 'Activate'}
                                            onClick={() => toggleActive(hero.id, hero.is_active)}
                                        >
                                            {hero.is_active ? (
                                                <ToggleRightIcon className="h-4 w-4" />
                                            ) : (
                                                <ToggleLeftIcon className="h-4 w-4" />
                                            )}
                                        </Button>
                                        <Button variant="plain" size="sm" title="Edit">
                                            <EditIcon className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="plain"
                                            size="sm"
                                            title="Delete"
                                            onClick={() => deleteHeroSection(hero.id)}
                                        >
                                            <TrashIcon className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
            </Card>
        </div>
    )
}

export default HeroManagement
