'use client'

import { motion } from 'framer-motion'
import PageLayout from '@/components/layout/PageLayout'
import { useRouter } from 'next/navigation'
import { TbGift, TbTrendingUp, TbUsers, TbClock, TbStar, TbArrowRight, TbCheck } from 'react-icons/tb'

const PromotionsPage = () => {
  const router = useRouter()

  const currentPromotions = [
    {
      id: 1,
      title: "Welcome Bonus",
      subtitle: "Up to $500 Trading Bonus",
      description: "Get a 100% deposit bonus up to $500 when you open your first live trading account with MyBrokerForex.",
      features: [
        "100% deposit bonus up to $500",
        "No hidden fees or charges",
        "Instant bonus credit",
        "Available for new clients only"
      ],
      validUntil: "December 31, 2024",
      buttonText: "Claim Bonus",
      gradient: "from-[#EA5455] to-[#ff6b6b]",
      icon: TbGift,
      featured: true
    },
    {
      id: 2,
      title: "Refer a Friend",
      subtitle: "Earn $100 for Each Referral",
      description: "Invite your friends to join MyBrokerForex and earn $100 for each successful referral who makes a deposit.",
      features: [
        "$100 cash reward per referral",
        "No limit on referrals",
        "Friend gets welcome bonus too",
        "Instant payout to your account"
      ],
      validUntil: "Ongoing",
      buttonText: "Start Referring",
      gradient: "from-[#28C76F] to-[#34c779]",
      icon: TbUsers,
      featured: false
    },
    {
      id: 3,
      title: "Cashback Program",
      subtitle: "Get 15% Cashback on Spreads",
      description: "Earn cashback on every trade you make. Get 15% of your spreads back credited to your account monthly.",
      features: [
        "15% cashback on all spreads",
        "Monthly automatic credit",
        "No minimum trading volume",
        "Available for all account types"
      ],
      validUntil: "Ongoing",
      buttonText: "Learn More",
      gradient: "from-[#2a85ff] to-[#4996ff]",
      icon: TbTrendingUp,
      featured: false
    }
  ]

  const seasonalOffers = [
    {
      title: "Black Friday Special",
      description: "50% reduced spreads on major currency pairs",
      validUntil: "November 30, 2024",
      status: "Limited Time"
    },
    {
      title: "New Year Trading Contest",
      description: "Win up to $10,000 in our annual trading competition",
      validUntil: "January 31, 2025",
      status: "Coming Soon"
    }
  ]

  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Exclusive <span className="text-[#EA5455]">Trading Promotions</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Maximize your trading potential with our exclusive bonuses, cashback programs, and special offers designed to boost your trading success.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Current Promotions */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Current Promotions</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Take advantage of our current offers and start trading with extra benefits
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {currentPromotions.map((promotion, index) => {
              const IconComponent = promotion.icon
              return (
                <motion.div
                  key={promotion.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`relative bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 ${
                    promotion.featured ? 'ring-2 ring-[#EA5455] scale-105' : ''
                  }`}
                >
                  {promotion.featured && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-[#EA5455] text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                        <TbStar className="h-4 w-4" />
                        Most Popular
                      </div>
                    </div>
                  )}

                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${promotion.gradient} flex items-center justify-center mb-6`}>
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>

                  <h3 className="text-2xl font-bold mb-2">{promotion.title}</h3>
                  <h4 className="text-lg font-semibold text-[#EA5455] mb-4">{promotion.subtitle}</h4>
                  <p className="text-muted-foreground mb-6">{promotion.description}</p>

                  <div className="space-y-3 mb-6">
                    {promotion.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center gap-3">
                        <TbCheck className="h-5 w-5 text-[#28C76F] flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <TbClock className="h-4 w-4" />
                      Valid until: {promotion.validUntil}
                    </div>
                  </div>

                  <button
                    onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                    className={`w-full bg-gradient-to-r ${promotion.gradient} text-white py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity flex items-center justify-center gap-2`}
                  >
                    {promotion.buttonText}
                    <TbArrowRight className="h-4 w-4" />
                  </button>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Seasonal Offers */}
      <section className="py-20 px-4 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Seasonal Offers</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Don't miss our limited-time seasonal promotions and special events
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {seasonalOffers.map((offer, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white dark:bg-gray-900 rounded-2xl p-8 border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-xl font-bold">{offer.title}</h3>
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                    offer.status === 'Limited Time' 
                      ? 'bg-[#EA5455]/10 text-[#EA5455]' 
                      : 'bg-[#28C76F]/10 text-[#28C76F]'
                  }`}>
                    {offer.status}
                  </span>
                </div>
                <p className="text-muted-foreground mb-4">{offer.description}</p>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <TbClock className="h-4 w-4" />
                  Valid until: {offer.validUntil}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Terms and Conditions */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Terms & Conditions</h2>
            <p className="text-xl text-muted-foreground">
              Please read our promotion terms carefully before participating
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700"
          >
            <div className="space-y-4 text-sm text-muted-foreground">
              <p>• All promotions are subject to MyBrokerForex terms and conditions</p>
              <p>• Bonuses cannot be withdrawn directly and must be traded according to bonus terms</p>
              <p>• Minimum deposit requirements may apply for certain promotions</p>
              <p>• MyBrokerForex reserves the right to modify or cancel promotions at any time</p>
              <p>• Promotions are available to eligible clients only and may be restricted in certain jurisdictions</p>
              <p>• For complete terms and conditions, please visit our website or contact customer support</p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-[#1E1E1E] text-white">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Claim Your Bonus?
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Open your trading account today and start benefiting from our exclusive promotions and bonuses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                Open Live Account
              </button>
              <button
                onClick={() => window.open('https://mbf.mybrokerforex.com/user/register?demo=true', '_blank')}
                className="border-2 border-white text-white hover:bg-white hover:text-[#1E1E1E] px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                Try Demo First
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    </PageLayout>
  )
}

export default PromotionsPage
