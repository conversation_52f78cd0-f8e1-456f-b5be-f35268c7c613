{"nav": {"dashboard": {"dashboard": "Dashboard", "ecommerce": "Ecommerce", "analytic": "Analytic", "project": "Project", "marketing": "Marketing"}, "concepts": "Concepts", "conceptsAi": {"ai": "AI", "chat": "Cha<PERSON>", "image": "Image", "aiDesc": "AI tools and resources", "chatDesc": "AI-powered chat systems", "imageDesc": "AI image processing"}, "conceptsProducts": {"products": "Products", "productList": "List", "productEdit": "Edit", "productCreate": "Create", "productsDesc": "Product inventory management", "productListDesc": "All products listed", "productEditDesc": "Edit product details", "productCreateDesc": "Add new product"}, "conceptsProjects": {"projects": "Projects", "projectList": "List", "projectDetails": "Details", "projectTasks": "Tasks", "projectIssue": "Issue", "scrumBoard": "Scrum Board", "projectsDesc": "Manage and track projects", "scrumBoardDesc": "Manage your scrum workflow", "projectListDesc": "Organize all projects", "projectDetailsDesc": "Project detailed information", "projectTasksDesc": "Manage project tasks", "projectIssueDesc": "Resolve project issues"}, "conceptsOrders": {"orders": "Orders", "orderList": "List", "orderEdit": "Edit", "orderDetails": "Details", "orderCreate": "Create", "ordersDesc": "Customer orders management", "orderListDesc": "View all customer orders", "orderEditDesc": "Edit order details", "orderCreateDesc": "Create new order", "orderDetailsDesc": "Detailed order information"}, "conceptsAccount": {"account": "Account", "settings": "Settings", "activityLog": "Activity log", "pricing": "Pricing", "rolesPermissions": "Roles & Permissions", "accountDesc": "Account settings and info", "settingsDesc": "Configure your settings", "activityLogDesc": "View recent activities", "rolesPermissionsDesc": "Manage roles & permissions", "pricingDesc": "View pricing plans"}, "conceptsHelpCenter": {"helpCenter": "Help Center", "supportHub": "Support Hub", "article": "Article", "editArticle": "Edit Article", "manageArticle": "Manage Article", "faq": "FAQ", "helpCenterDesc": "Support and articles", "supportHubDesc": "Central support hub", "articleDesc": "Read support articles", "editArticleDesc": "Modify article content", "manageArticleDesc": "Article management"}, "calendar": "Calendar", "fileManager": "File Manager", "mail": "Mail", "chat": "Cha<PERSON>", "uiComponents": "UI Components", "calendarDesc": "Schedule and events", "fileManagerDesc": "File management", "mailDesc": "Manage your emails", "chatDesc": "Chat with friends", "uiComponentsCommon": {"common": "Common", "grid": "Grid", "button": "<PERSON><PERSON>", "typography": "Typography", "icons": "Icons", "commonDesc": "Common UI elements", "buttonDesc": "Interactive buttons", "gridDesc": "Layout grid system", "typographyDesc": "Text styling tools", "iconsDesc": "Visual icon set"}, "uiComponentsFeedback": {"feedback": "<PERSON><PERSON><PERSON>", "alert": "<PERSON><PERSON>", "dialog": "Dialog", "drawer": "Drawer", "progress": "Progress", "skeleton": "Skeleton", "spinner": "Spinner", "toast": "Toast", "feedbackDesc": "User feedback components", "alertDesc": "Notification alerts", "dialogDesc": "Modal dialog boxes", "drawerDesc": "Sidebar drawers", "progressDesc": "Progress indicators", "skeletonDesc": "Loading skeletons", "spinnerDesc": "Loading spinners", "toastDesc": "Toast notifications"}, "uiComponentsDataDisplay": {"dataDisplay": "Data Display", "avatar": "Avatar", "badge": "Badge", "calendar": "Calendar", "cards": "Cards", "table": "Table", "tag": "Tag", "timeline": "Timeline", "tooltip": "<PERSON><PERSON><PERSON>", "dataDisplayDesc": "Components for showing data", "avatarDesc": "User profile pictures", "badgeDesc": "Status indicators", "calendarDesc": "Date selectors", "cardsDesc": "Content cards", "tableDesc": "Data tables", "tagDesc": "Label tags", "timelineDesc": "Event timelines", "tooltipDesc": "Hover tooltips"}, "uiComponentsForms": {"forms": "Forms", "checkbox": "Checkbox", "datePicker": "Date Picker", "formControl": "Form Control", "input": "Input", "inputGroup": "Input Group", "radio": "Radio", "segment": "Segment", "select": "Select", "slider": "Slide<PERSON>", "switcher": "Switcher", "timeInput": "Time Input", "upload": "Upload", "formsDesc": "Form elements", "checkboxDesc": "Tickable checkboxes", "datePickerDesc": "Select dates", "formControlDesc": "Form control elements", "inputDesc": "Text inputs", "inputGroupDesc": "Grouped inputs", "radioDesc": "Radio buttons", "segmentDesc": "Input segments", "selectDesc": "Dropdown selects", "sliderDesc": "Input sliders", "switcherDesc": "Toggle switches", "timeInputDesc": "Time inputs", "uploadDesc": "File uploaders"}, "uiComponentsNavigation": {"navigation": "Navigation", "dropdown": "Dropdown", "menu": "<PERSON><PERSON>", "pagination": "Pagination", "steps": "Steps", "tabs": "Tabs", "navigationDesc": "Navigation elements", "dropdownDesc": "Dropdown menus", "menuDesc": "Menu navigation", "paginationDesc": "Pagination controls", "stepsDesc": "Sequential steps", "tabsDesc": "Tab navigation"}, "uiComponentsGraph": {"graph": "Graph", "charts": "Charts", "maps": "Maps", "graphDesc": "Graphical elements", "chartsDesc": "Various charts", "mapsDesc": "Geographic maps"}, "pages": {"pages": "Pages", "welcome": "Welcome", "accessDenied": "Access Denied"}, "authentication": {"authentication": "Authentication", "signIn": "Sign In", "signInSimple": "Simple", "signInSide": "Side", "signInSplit": "Split", "signUp": "Sign Up", "signUpSimple": "Simple", "signUpSide": "Side", "signUpSplit": "Split", "forgotPassword": "Forgot Password", "forgotPasswordSimple": "Simple", "forgotPasswordSide": "Side", "forgotPasswordSplit": "Split", "resetPassword": "Reset Password", "resetPasswordSimple": "Simple", "resetPasswordSide": "Side", "resetPasswordSplit": "Split", "otpVerification": "Otp Verification", "otpVerificationSimple": "Simple", "otpVerificationSide": "Side", "otpVerificationSplit": "Split"}, "others": {"others": "Others", "accessDenied": "Access Denied", "accessDeniedDesc": "Access denied page", "landing": "Landing", "landingDesc": "Landing page"}, "guide": {"guide": "Guide", "documentation": "Documentation", "sharedComponentDoc": "Shared Component", "utilsDoc": "Utilities", "changeLog": "Changelog"}, "cms": {"pages": "Pages", "pagesDesc": "Manage website pages and content", "posts": "Blog Posts", "postsDesc": "Manage blog posts and news", "menus": "Navigation Menus", "menusDesc": "Manage website navigation structure", "hero": "Hero Sections", "heroDesc": "Manage homepage hero banners", "widgets": "Widgets", "widgetsDesc": "Manage forex/crypto widgets and APIs", "testimonials": "Testimonials", "testimonialsDesc": "Manage client testimonials and reviews"}, "partners": {"ib": "IB Partners", "ibDesc": "Manage Introducing Broker partners", "affiliate": "Affiliate Partners", "affiliateDesc": "Manage affiliate marketing partners"}, "settings": {"general": "General Settings", "generalDesc": "Site-wide configurations and preferences"}}}