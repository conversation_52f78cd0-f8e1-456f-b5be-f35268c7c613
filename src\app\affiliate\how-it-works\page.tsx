'use client'

import { motion } from 'framer-motion'
import { TbUserPlus, TbShare, TbCash, TbChartLine, TbGift, TbUsers } from 'react-icons/tb'
import { HiArrowRight } from 'react-icons/hi2'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'

export default function HowItWorksPage() {
    const steps = [
        {
            icon: TbUserPlus,
            title: 'Sign Up',
            description: 'Register for our affiliate program with a simple application process. Get approved within 24 hours.'
        },
        {
            icon: TbShare,
            title: 'Promote',
            description: 'Share your unique referral links through your website, social media, or marketing campaigns.'
        },
        {
            icon: TbUsers,
            title: 'Refer Clients',
            description: 'When someone clicks your link and opens a trading account, they become your referred client.'
        },
        {
            icon: TbCash,
            title: 'Earn Commissions',
            description: 'Receive competitive commissions for every trade your referred clients make. Payments monthly.'
        }
    ]

    const benefits = [
        {
            icon: TbChartLine,
            title: 'High Conversion Rates',
            description: 'Our proven trading platform and competitive spreads help convert your traffic into active traders.'
        },
        {
            icon: TbGift,
            title: 'Generous Commissions',
            description: 'Earn up to $1,200 per lot with our tiered commission structure that rewards top performers.'
        },
        {
            icon: TbUsers,
            title: 'Dedicated Support',
            description: 'Get personalized support from our affiliate team to optimize your marketing campaigns.'
        }
    ]

    const commissionTiers = [
        { tier: 'Bronze', volume: '0-50 lots', commission: '$400 per lot' },
        { tier: 'Silver', volume: '51-100 lots', commission: '$600 per lot' },
        { tier: 'Gold', volume: '101-200 lots', commission: '$800 per lot' },
        { tier: 'Platinum', volume: '200+ lots', commission: '$1,200 per lot' }
    ]

    return (
        <div className="mt-0">
            <PublicPageLayout>
            <div className="bg-gray-50 dark:bg-gray-900">
            {/* Hero Section */}
            <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        <h1 className="text-5xl text-gray-300 mt-8 font-bold mb-6">
                            How Our Affiliate Program Works
                        </h1>
                        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                            Join our lucrative affiliate program and start earning competitive commissions 
                            by referring traders to our platform. Simple, transparent, and profitable.
                        </p>
                        <button className="px-8 py-4 bg-[#EA5455] text-white font-semibold rounded-xl hover:bg-[#d63384] transition-colors duration-200 flex items-center justify-center mx-auto">
                            Join Affiliate Program
                            <HiArrowRight className="ml-2 w-5 h-5" />
                        </button>
                    </motion.div>
                </div>
            </section>

            {/* How It Works Steps */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                        className="text-center mb-16"
                    >
                        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                            4 Simple Steps to Start Earning
                        </h2>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            Our affiliate program is designed to be straightforward and profitable for partners of all sizes.
                        </p>
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {steps.map((step, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 * index }}
                                className="text-center"
                            >
                                <div className="relative mb-8">
                                    <div className="w-20 h-20 bg-[#EA5455] rounded-full flex items-center justify-center mx-auto mb-4">
                                        <step.icon className="w-10 h-10 text-white" />
                                    </div>
                                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-[#28C76F] rounded-full flex items-center justify-center text-white font-bold text-sm">
                                        {index + 1}
                                    </div>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    {step.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    {step.description}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Commission Structure */}
            <section className="py-20 bg-white dark:bg-gray-800">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center mb-16"
                    >
                        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                            Commission Structure
                        </h2>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            Our tiered commission structure rewards high-performing affiliates with increased rates.
                        </p>
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {commissionTiers.map((tier, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 * index }}
                                className={`p-6 rounded-2xl border-2 ${
                                    tier.tier === 'Platinum' 
                                        ? 'border-[#EA5455] bg-[#EA5455] text-white' 
                                        : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700'
                                }`}
                            >
                                <h3 className={`text-xl font-bold mb-2 ${
                                    tier.tier === 'Platinum' ? 'text-white' : 'text-gray-900 dark:text-white'
                                }`}>
                                    {tier.tier}
                                </h3>
                                <p className={`text-sm mb-4 ${
                                    tier.tier === 'Platinum' ? 'text-white opacity-90' : 'text-gray-600 dark:text-gray-300'
                                }`}>
                                    {tier.volume}
                                </p>
                                <p className={`text-2xl font-bold ${
                                    tier.tier === 'Platinum' ? 'text-white' : 'text-[#EA5455]'
                                }`}>
                                    {tier.commission}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Benefits Section */}
            <section className="py-20">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center mb-16"
                    >
                        <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                            Why Partner With Us?
                        </h2>
                    </motion.div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {benefits.map((benefit, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.1 * index }}
                                className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg text-center"
                            >
                                <div className="w-16 h-16 bg-[#EA5455] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                    <benefit.icon className="w-8 h-8 text-white" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                    {benefit.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    {benefit.description}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-gradient-to-br from-[#EA5455] to-[#d63384]">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="text-center text-white"
                    >
                        <h2 className="text-4xl font-bold mb-6">
                            Ready to Start Earning?
                        </h2>
                        <p className="text-xl mb-8 max-w-3xl mx-auto">
                            Join thousands of successful affiliates who are earning substantial commissions with our program.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button className="px-8 py-4 bg-white text-[#EA5455] font-semibold rounded-xl hover:bg-gray-100 transition-colors duration-200">
                                Apply Now
                            </button>
                            <button className="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-[#EA5455] transition-colors duration-200">
                                Learn More
                            </button>
                        </div>
                    </motion.div>
                </div>
            </section>
            </div>
                    </PublicPageLayout>
        </div>
    )
}
