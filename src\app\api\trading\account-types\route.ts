import { NextRequest, NextResponse } from 'next/server'
import pool from '@/server/db'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

// GET /api/trading/account-types - Get all account types
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const active = searchParams.get('active')
        
        let query = 'SELECT * FROM account_types'
        const params: any[] = []
        
        if (active) {
            query += ' WHERE is_active = ?'
            params.push(active === 'true')
        }
        
        query += ' ORDER BY display_order, id'
        
        const [rows] = await pool.execute<RowDataPacket[]>(query, params)
        
        // Parse JSON features for each account type
        const accountTypes = rows.map(account => ({
            ...account,
            features: account.features ? JSON.parse(account.features) : []
        }))
        
        return NextResponse.json({
            success: true,
            data: accountTypes
        })
    } catch (error) {
        console.error('Error fetching account types:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to fetch account types' },
            { status: 500 }
        )
    }
}

// POST /api/trading/account-types - Create new account type
export async function POST(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            name, 
            min_deposit, 
            spread_from, 
            leverage, 
            execution_type, 
            bonus_percentage = 0.00, 
            islamic_option = false, 
            features = [], 
            is_active = true, 
            display_order = 0 
        } = body
        
        if (!name || !min_deposit || !spread_from || !leverage || !execution_type) {
            return NextResponse.json(
                { success: false, error: 'Name, min_deposit, spread_from, leverage, and execution_type are required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'INSERT INTO account_types (name, min_deposit, spread_from, leverage, execution_type, bonus_percentage, islamic_option, features, is_active, display_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [name, min_deposit, spread_from, leverage, execution_type, bonus_percentage, islamic_option, JSON.stringify(features), is_active, display_order]
        )
        
        return NextResponse.json({
            success: true,
            data: { 
                id: result.insertId, 
                name, 
                min_deposit, 
                spread_from, 
                leverage, 
                execution_type, 
                bonus_percentage, 
                islamic_option, 
                features, 
                is_active, 
                display_order 
            }
        }, { status: 201 })
    } catch (error) {
        console.error('Error creating account type:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to create account type' },
            { status: 500 }
        )
    }
}

// PUT /api/trading/account-types - Update account type
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json()
        const { 
            id, 
            name, 
            min_deposit, 
            spread_from, 
            leverage, 
            execution_type, 
            bonus_percentage, 
            islamic_option, 
            features, 
            is_active, 
            display_order 
        } = body
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Account type ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'UPDATE account_types SET name = ?, min_deposit = ?, spread_from = ?, leverage = ?, execution_type = ?, bonus_percentage = ?, islamic_option = ?, features = ?, is_active = ?, display_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [name, min_deposit, spread_from, leverage, execution_type, bonus_percentage, islamic_option, JSON.stringify(features), is_active, display_order, id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Account type not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            data: { id, name, min_deposit, spread_from, leverage, execution_type, bonus_percentage, islamic_option, features, is_active, display_order }
        })
    } catch (error) {
        console.error('Error updating account type:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to update account type' },
            { status: 500 }
        )
    }
}

// DELETE /api/trading/account-types - Delete account type
export async function DELETE(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams
        const id = searchParams.get('id')
        
        if (!id) {
            return NextResponse.json(
                { success: false, error: 'Account type ID is required' },
                { status: 400 }
            )
        }
        
        const [result] = await pool.execute<ResultSetHeader>(
            'DELETE FROM account_types WHERE id = ?',
            [id]
        )
        
        if (result.affectedRows === 0) {
            return NextResponse.json(
                { success: false, error: 'Account type not found' },
                { status: 404 }
            )
        }
        
        return NextResponse.json({
            success: true,
            message: 'Account type deleted successfully'
        })
    } catch (error) {
        console.error('Error deleting account type:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to delete account type' },
            { status: 500 }
        )
    }
}
