import Container from './LandingContainer'
import { motion } from 'framer-motion'
import { Tb<PERSON><PERSON><PERSON>, TbTrendingUp, TbShield, TbClock, TbHeadphones, TbChartLine, TbWorld } from 'react-icons/tb'
import type { Mode } from '@/@types/theme'

type FeaturesProps = {
    mode: Mode
    onModeChange: (value: boolean) => void
}

const forexFeatures = [
    {
        icon: TbTrendingUp,
        title: 'Competitive Spreads',
        description: 'Trade with industry-leading spreads starting from 0.1 pips on major currency pairs.',
        color: 'text-[#28C76F]'
    },
    {
        icon: TbShield,
        title: 'Market News & Updates',
        description: 'Stay ahead of the curve and make well-informed trading decisions with the latest news and insights available at your fingertips.',
        color: 'text-[#EA5455]'
    },
    {
        icon: TbClock,
        title: 'Light Fasting Execution',
        description: 'Experience fast, seamless trading with top-tier liquidity, low latency, and high fill rates  all at competitive prices.',
        color: 'text-blue-500'
    },
    {
        icon: TbHeadphones,
        title: 'Expert Support',
        description: 'Get professional support from our experienced trading specialists whenever you need it.',
        color: 'text-purple-500'
    },
    {
        icon: TbChartLine,
        title: 'Flexible & Tailored Service',
        description: 'Support and offers customized to each trader’s needs, delivering a brokerage experience that’s truly client-focused.',
        color: 'text-orange-500'
    },
    {
        icon: TbWorld,
        title: 'Global Markets',
        description: 'Trade 50+ currency pairs, commodities, indices, and cryptocurrencies from one platform.',
        color: 'text-teal-500'
    }
]

const Features = ({ mode }: FeaturesProps) => {
    return (
        <Container id="features">
            <div className="max-w-7xl mx-auto px-4 py-20" suppressHydrationWarning>
                <div className="text-center mb-16" suppressHydrationWarning>
                    <motion.h2
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="text-3xl md:text-5xl font-bold mb-4"
                    >
                        Why Choose <span className="text-[#EA5455]">MyBrokerForex</span>
                    </motion.h2>
                    <motion.p
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="text-xl text-muted-foreground max-w-3xl mx-auto"
                    >
                        Experience professional forex trading with industry-leading features, competitive spreads, and expert support.
                    </motion.p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" suppressHydrationWarning>
                    {forexFeatures.map((feature, index) => {
                        const IconComponent = feature.icon
                        return (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: index * 0.1 }}
                                className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-300"
                                suppressHydrationWarning
                            >
                                <div className={`w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4`} suppressHydrationWarning>
                                    <IconComponent className={`h-6 w-6 ${feature.color}`} />
                                </div>
                                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                                <p className="text-muted-foreground">{feature.description}</p>
                            </motion.div>
                        )
                    })}
                </div>

                {/* Trading Statistics */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mt-20 bg-[#1E1E1E] rounded-2xl p-8 text-white"
                >
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                        <div>
                            <div className="text-3xl font-bold mb-2">50+</div>
                            <div className="text-white/80">Currency Pairs</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold mb-2">0.1</div>
                            <div className="text-white/80">Pip Spreads</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold mb-2">24/7</div>
                            <div className="text-white/80">Market Access</div>
                        </div>
                        <div>
                            <div className="text-3xl font-bold mb-2">100K+</div>
                            <div className="text-white/80">Active Traders</div>
                        </div>
                    </div>
                </motion.div>
            </div>
        </Container>
    )
}

export default Features
