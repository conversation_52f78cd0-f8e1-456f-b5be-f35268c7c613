'use client'

import { motion } from 'framer-motion'
import PublicPageLayout from '@/components/layouts/PublicPageLayout'
import { TbVideo, TbPlayerPlay, TbClock, TbUser, TbStar, TbChartLine, TbSettings, TbBrain } from 'react-icons/tb'

const VideoTutorialsPage = () => {
    const tutorialCategories = [
        {
            icon: TbPlayerPlay,
            title: 'Getting Started',
            description: 'Essential tutorials for new traders to begin their forex journey.',
            videoCount: 12,
            totalDuration: '2.5 hours',
            level: 'Beginner',
            color: 'bg-[#28C76F]/10 text-[#28C76F]'
        },
        {
            icon: TbChartLine,
            title: 'Technical Analysis',
            description: 'Master chart reading, indicators, and technical trading strategies.',
            videoCount: 18,
            totalDuration: '4.2 hours',
            level: 'Intermediate',
            color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
        },
        {
            icon: TbSettings,
            title: 'Platform Training',
            description: 'Complete guides to using our trading platform effectively.',
            videoCount: 15,
            totalDuration: '3.1 hours',
            level: 'Beginner',
            color: 'bg-[#28C76F]/10 text-[#28C76F]'
        },
        {
            icon: TbBrain,
            title: 'Advanced Strategies',
            description: 'Sophisticated trading techniques for experienced traders.',
            videoCount: 22,
            totalDuration: '5.8 hours',
            level: 'Advanced',
            color: 'bg-[#EA5455]/10 text-[#EA5455]'
        }
    ]

    const featuredVideos = [
        {
            title: 'Forex Trading Basics: Complete Beginner Guide',
            description: 'Learn the fundamentals of forex trading from scratch',
            duration: '25:30',
            views: '125K',
            rating: 4.9,
            thumbnail: '/img/education/video-1.jpg',
            level: 'Beginner',
            category: 'Getting Started'
        },
        {
            title: 'How to Use MetaTrader 5 Platform',
            description: 'Complete walkthrough of MT5 features and tools',
            duration: '18:45',
            views: '89K',
            rating: 4.8,
            thumbnail: '/img/education/video-2.jpg',
            level: 'Beginner',
            category: 'Platform Training'
        },
        {
            title: 'Technical Analysis: Support and Resistance',
            description: 'Master key levels for better trade entries and exits',
            duration: '22:15',
            views: '156K',
            rating: 4.9,
            thumbnail: '/img/education/video-3.jpg',
            level: 'Intermediate',
            category: 'Technical Analysis'
        },
        {
            title: 'Risk Management Strategies',
            description: 'Protect your capital with proven risk management techniques',
            duration: '19:30',
            views: '98K',
            rating: 4.7,
            thumbnail: '/img/education/video-4.jpg',
            level: 'Intermediate',
            category: 'Getting Started'
        },
        {
            title: 'Advanced Price Action Trading',
            description: 'Read market sentiment through pure price movement',
            duration: '31:20',
            views: '67K',
            rating: 4.8,
            thumbnail: '/img/education/video-5.jpg',
            level: 'Advanced',
            category: 'Advanced Strategies'
        },
        {
            title: 'Economic Calendar and News Trading',
            description: 'Trade high-impact news events successfully',
            duration: '16:45',
            views: '78K',
            rating: 4.6,
            thumbnail: '/img/education/video-6.jpg',
            level: 'Intermediate',
            category: 'Technical Analysis'
        }
    ]

    const learningPaths = [
        {
            title: 'Complete Beginner Path',
            description: 'Start from zero and build solid trading foundations',
            videos: 15,
            duration: '4.5 hours',
            steps: ['Forex Basics', 'Platform Setup', 'First Trade', 'Risk Management', 'Practice Trading']
        },
        {
            title: 'Technical Analysis Mastery',
            description: 'Become proficient in chart analysis and indicators',
            videos: 20,
            duration: '6.2 hours',
            steps: ['Chart Basics', 'Indicators', 'Patterns', 'Multi-Timeframe', 'Strategy Building']
        },
        {
            title: 'Professional Trader Path',
            description: 'Advanced techniques for serious traders',
            videos: 25,
            duration: '8.1 hours',
            steps: ['Advanced Analysis', 'Psychology', 'Portfolio Management', 'Automation', 'Professional Tools']
        }
    ]

    return (
        <PublicPageLayout>
                <div className="bg-gray-50 dark:bg-gray-900">
                    {/* Hero Section */}
                    <section className="relative py-20 bg-gradient-to-br from-[#1E1E1E] to-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center text-white"
                            >
                                <h1 className="text-4xl md:text-6xl text-gray-300 mt-8 font-bold mb-6">
                                    Video Tutorials
                                </h1>
                                <p className="text-xl md:text-2xl text-gray-100 mb-8 max-w-3xl mx-auto">
                                    Learn forex trading through comprehensive step-by-step video lessons from industry experts
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button 
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-[#EA5455] hover:bg-[#EA5455]/90 text-white px-5 py-2 rounded-lg font-semibold transition-colors duration-300"
                                    >
                                        Start Learning
                                    </button>
                                    <button 
                                        onClick={() => window.open('/education/demo-account', '_blank')}
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#1E1E1E] transition-colors duration-300"
                                    >
                                        Try Demo First
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>

                    {/* Tutorial Categories Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Tutorial Categories
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Choose from our organized video categories to learn at your own pace and skill level.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                {tutorialCategories.map((category, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300 cursor-pointer"
                                    >
                                        <div className="w-12 h-12 bg-[#EA5455]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                                            <category.icon className="w-6 h-6 text-[#EA5455]" />
                                        </div>
                                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                                            {category.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                            {category.description}
                                        </p>
                                        <div className="space-y-2">
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-500 dark:text-gray-400">Videos:</span>
                                                <span className="font-medium text-gray-900 dark:text-white">{category.videoCount}</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span className="text-gray-500 dark:text-gray-400">Duration:</span>
                                                <span className="font-medium text-gray-900 dark:text-white">{category.totalDuration}</span>
                                            </div>
                                            <div className="pt-2">
                                                <span className={`px-3 py-1 rounded-full text-xs font-medium ${category.color}`}>
                                                    {category.level}
                                                </span>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Featured Videos Section */}
                    <section className="py-20 bg-gray-50 dark:bg-gray-900">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Featured Video Tutorials
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Start with our most popular and highly-rated video tutorials.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                {featuredVideos.map((video, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer"
                                    >
                                        <div className="relative">
                                            <div className="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center">
                                                <TbPlayerPlay className="w-16 h-16 text-[#EA5455]" />
                                            </div>
                                            <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                                                {video.duration}
                                            </div>
                                            <div className="absolute top-2 left-2">
                                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                                    video.level === 'Beginner'
                                                        ? 'bg-[#28C76F]/90 text-white'
                                                        : video.level === 'Intermediate'
                                                        ? 'bg-blue-500/90 text-white'
                                                        : 'bg-[#EA5455]/90 text-white'
                                                }`}>
                                                    {video.level}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="p-6">
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="text-xs text-[#EA5455] font-medium">{video.category}</span>
                                                <div className="flex items-center space-x-1">
                                                    <TbStar className="w-4 h-4 text-yellow-400 fill-current" />
                                                    <span className="text-sm text-gray-600 dark:text-gray-400">{video.rating}</span>
                                                </div>
                                            </div>
                                            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                                                {video.title}
                                            </h3>
                                            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                                {video.description}
                                            </p>
                                            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                                                <div className="flex items-center space-x-1">
                                                    <TbUser className="w-4 h-4" />
                                                    <span>{video.views} views</span>
                                                </div>
                                                <div className="flex items-center space-x-1">
                                                    <TbClock className="w-4 h-4" />
                                                    <span>{video.duration}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Learning Paths Section */}
                    <section className="py-20 bg-white dark:bg-gray-800">
                        <div className="max-w-7xl mx-auto px-6">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                className="text-center mb-16"
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                                    Structured Learning Paths
                                </h2>
                                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                                    Follow our curated learning paths for systematic skill development.
                                </p>
                            </motion.div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                {learningPaths.map((path, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 30 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.6, delay: index * 0.1 }}
                                        className="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                                    >
                                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                                            {path.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                                            {path.description}
                                        </p>
                                        <div className="flex justify-between mb-6 text-sm">
                                            <div className="text-center">
                                                <div className="font-bold text-[#EA5455] text-lg">{path.videos}</div>
                                                <div className="text-gray-500 dark:text-gray-400">Videos</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="font-bold text-[#28C76F] text-lg">{path.duration}</div>
                                                <div className="text-gray-500 dark:text-gray-400">Duration</div>
                                            </div>
                                        </div>
                                        <div className="space-y-3">
                                            <h4 className="font-semibold text-gray-900 dark:text-white text-sm">Learning Steps:</h4>
                                            {path.steps.map((step, stepIndex) => (
                                                <div key={stepIndex} className="flex items-center space-x-3">
                                                    <div className="w-6 h-6 bg-[#EA5455] text-white rounded-full flex items-center justify-center text-xs font-bold">
                                                        {stepIndex + 1}
                                                    </div>
                                                    <span className="text-gray-700 dark:text-gray-300 text-sm">{step}</span>
                                                </div>
                                            ))}
                                        </div>
                                        <button className="w-full mt-6 bg-[#EA5455] hover:bg-[#EA5455]/90 text-white py-2 rounded-lg font-semibold transition-colors duration-300">
                                            Start Path
                                        </button>
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="py-20 bg-gradient-to-br from-[#EA5455] to-[#EA5455]/80">
                        <div className="max-w-7xl mx-auto px-6 text-center">
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                                    Ready to Start Your Video Learning Journey?
                                </h2>
                                <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                                    Access our complete video library and start building your trading skills today.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <button
                                        onClick={() => window.open('https://mbf.mybrokerforex.com/user/register', '_blank')}
                                        className="bg-white text-[#EA5455] px-5 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300"
                                    >
                                        Access All Videos
                                    </button>
                                    <button
                                        onClick={() => window.open('/education/demo-account', '_blank')}
                                        className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-[#EA5455] transition-colors duration-300"
                                    >
                                        Practice with Demo
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </section>
                </div>
            </PublicPageLayout>
    )
}

export default VideoTutorialsPage
